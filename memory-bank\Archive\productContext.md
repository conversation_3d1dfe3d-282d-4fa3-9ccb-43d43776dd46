# Product Context

The framework is designed for robust, auditable backtesting of portfolio allocation models. It enables users to:
- Swap and optimize allocation strategies
- Analyze results with comprehensive metrics and visualizations
- Use GUI or batch interface
- Optionally enable detailed EMA tracking for advanced debugging

User experience goals: transparency, flexibility, and auditability for research and production workflows.

_Add additional user stories or context as project evolves._

**Current status:**
- V3 GUI testing phase in progress
- Parameter audit and benchmark grouping complete
- No V3 backtests or reports yet
- User is concerned about V3 reporting
