"""
verify_trade_filter.py
Verification script to check if the trade filter is working properly.

Tests if trades are happening on dates where signals didn't change by more than 2%
(indicating the filter is NOT working correctly).

Example test date: 2020-01-09 - signals should be nearly identical to previous day.
"""

import sys
from pathlib import Path
import pandas as pd
import logging

# Add project root to path
project_root = Path(__file__).resolve().parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def verify_trade_filter():
    """
    Verify if trade filter is working by checking for trades on dates
    where signals shouldn't have changed significantly.
    """
    
    # Load the latest trade log
    output_dir = project_root / "v4_trace_outputs"
    
    # Find the most recent trade log
    trade_logs = list(output_dir.glob("trade_log_*.csv"))
    if not trade_logs:
        logger.error("No trade logs found in v4_trace_outputs")
        return False
    
    latest_trade_log = max(trade_logs, key=lambda f: f.stat().st_mtime)
    logger.info(f"Using trade log: {latest_trade_log}")
    
    # Load trade log
    try:
        trade_df = pd.read_csv(latest_trade_log)
        logger.info(f"Loaded {len(trade_df)} trades from log")
        
        if 'execution_date' not in trade_df.columns:
            if 'date' in trade_df.columns:
                trade_df['execution_date'] = trade_df['date']
            else:
                logger.error("No date column found in trade log")
                return False
        
        # Convert to datetime
        trade_df['execution_date'] = pd.to_datetime(trade_df['execution_date'])
        
    except Exception as e:
        logger.error(f"Error loading trade log: {e}")
        return False
    
    # Load signals to check for signal changes
    signals_file = output_dir / "signals_output_20250622_192844.csv"
    if not signals_file.exists():
        logger.error(f"Signals file not found: {signals_file}")
        return False
    
    try:
        signals_df = pd.read_csv(signals_file, index_col='Date', parse_dates=True)
        logger.info(f"Loaded signals with shape: {signals_df.shape}")
    except Exception as e:
        logger.error(f"Error loading signals: {e}")
        return False
    
    # Test specific dates where signals shouldn't change much
    test_dates = [
        '2020-01-09',  # Example problematic date mentioned by user
        '2020-01-10',
        '2020-01-13',
        '2020-01-14',
        '2020-01-15'
    ]
    
    filter_working = True
    
    for test_date_str in test_dates:
        test_date = pd.to_datetime(test_date_str)
        
        # Check if there are trades on this date
        trades_on_date = trade_df[trade_df['execution_date'].dt.date == test_date.date()]
        
        if len(trades_on_date) > 0:
            logger.warning(f"Found {len(trades_on_date)} trades on {test_date_str}")
            
            # Check signal change from previous day
            if test_date in signals_df.index:
                prev_date = None
                for idx in range(len(signals_df.index)):
                    if signals_df.index[idx] == test_date and idx > 0:
                        prev_date = signals_df.index[idx-1]
                        break
                
                if prev_date is not None:
                    current_signals = signals_df.loc[test_date]
                    prev_signals = signals_df.loc[prev_date]
                    
                    # Calculate max change in allocation
                    signal_changes = abs(current_signals - prev_signals)
                    max_change = signal_changes.max()
                    
                    logger.info(f"  Max signal change on {test_date_str}: {max_change:.4f} ({max_change:.2%})")
                    
                    if max_change < 0.02:  # Less than 2% change
                        logger.error(f"  FILTER FAILURE: Trades executed despite signal change < 2%!")
                        filter_working = False
                        
                        # Show the trades
                        for _, trade in trades_on_date.iterrows():
                            logger.error(f"    Trade: {trade.get('symbol', 'Unknown')} "
                                       f"Qty: {trade.get('quantity', 'Unknown')} "
                                       f"Amount: ${trade.get('amount', 'Unknown')}")
                    else:
                        logger.info(f"  Trade justified: signal change >= 2%")
            else:
                logger.warning(f"  No signal data for {test_date_str}")
        else:
            logger.info(f"No trades on {test_date_str} - Good (assuming signals didn't change)")
    
    if filter_working:
        logger.info("✅ TRADE FILTER APPEARS TO BE WORKING CORRECTLY")
        return True
    else:
        logger.error("❌ TRADE FILTER IS NOT WORKING - TRADES HAPPENING WITHOUT SUFFICIENT SIGNAL CHANGES")
        return False

if __name__ == "__main__":
    success = verify_trade_filter()
    sys.exit(0 if success else 1)
