"""
V3 Parameter GUI Refactor Validation Test

This script tests the refactored GUI parameter manager components:
- parameter_gui_access.py
- parameter_gui_controls.py
- parameter_gui_sync.py
- gui_parameter_manager.py (facade)

It validates that the refactoring maintains all functionality while improving code organization.
"""

import os
import sys
import logging
import tkinter as tk
from datetime import datetime
from pathlib import Path

# Add parent directory to path so we can import from the project
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Set up logging
log_dir = os.path.join(parent_dir, "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"v3_parameter_gui_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("v3_parameter_gui_test")

# Import the V3 modules we want to test
try:
    # Import the facade and its components
    from v3_engine.gui_parameter_manager import GuiParameterManager
    from v3_engine import parameter_gui_access
    from v3_engine import parameter_gui_controls
    from v3_engine import parameter_gui_sync
    
    # Import related V3 components
    from v3_engine.parameter_registry import get_registry, ParameterRegistry
    from v3_engine.parameters import NumericParameter, CategoricalParameter, CategoricalListParameter
    from v3_engine.strategy_parameter_set import get_available_strategies
    
    logger.info("Successfully imported V3 modules")
except Exception as e:
    logger.error(f"Error importing V3 modules: {e}")
    sys.exit(1)

def test_parameter_gui_access():
    """Test the parameter_gui_access module functionality."""
    logger.info("Testing parameter_gui_access module...")
    
    try:
        # Get the registry
        registry = get_registry()
        
        # Register test parameters
        num_param = NumericParameter(
            name="test_numeric_access",
            default=10,
            min_value=0,
            max_value=100,
            step=1,
            description="Test numeric parameter for access",
            group="test_access"
        )
        registry.register_parameter(num_param.group, num_param)
        
        cat_param = CategoricalParameter(
            name="test_categorical_access",
            default="option1",
            choices=["option1", "option2", "option3"],
            description="Test categorical parameter for access",
            group="test_access"
        )
        registry.register_parameter(cat_param.group, cat_param)
        
        # Test access functions
        numeric_params = parameter_gui_access.get_numeric_parameters(registry, "test_access")
        categorical_params = parameter_gui_access.get_categorical_parameters(registry, "test_access")
        
        logger.info(f"Got {len(numeric_params)} numeric parameters")
        logger.info(f"Got {len(categorical_params)} categorical parameters")
        
        # Verify parameters were retrieved correctly
        if len(numeric_params) == 1 and len(categorical_params) == 1:
            logger.info("Parameter access functions working correctly")
        else:
            logger.error(f"Parameter access functions returned unexpected results: {len(numeric_params)} numeric, {len(categorical_params)} categorical")
            return False
        
        # Test strategy list access
        strategies = parameter_gui_access.get_strategy_list()
        logger.info(f"Got {len(strategies)} strategies")
        
        return True
    except Exception as e:
        logger.error(f"Error in parameter_gui_access test: {e}")
        return False

def test_parameter_gui_controls():
    """Test the parameter_gui_controls module functionality."""
    logger.info("Testing parameter_gui_controls module...")
    
    try:
        # Create a root window
        root = tk.Tk()
        root.title("Parameter GUI Controls Test")
        root.geometry("800x600")
        
        # Create a frame
        frame = tk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create test parameters
        num_param = NumericParameter(
            name="test_numeric_control",
            default=50,
            min_value=0,
            max_value=100,
            step=5,
            description="Test numeric parameter for controls",
            group="test_controls"
        )
        
        cat_param = CategoricalParameter(
            name="test_categorical_control",
            default="option1",
            choices=["option1", "option2", "option3"],
            description="Test categorical parameter for controls",
            group="test_controls"
        )
        
        # Create dictionaries to store variables
        parameter_vars = {}
        optimize_vars = {}
        
        # Define update callbacks (these would normally be in the GuiParameterManager)
        def update_value(param, var):
            logger.info(f"Value update callback called for {param.name}")
            
        def update_optimize(param, var):
            logger.info(f"Optimize update callback called for {param.name}")
            
        def show_list(param):
            logger.info(f"Show list callback called for {param.name}")
        
        # Create controls
        numeric_control = parameter_gui_controls.create_numeric_control(
            frame, num_param, 0, parameter_vars, optimize_vars, update_value, update_optimize
        )
        
        categorical_control = parameter_gui_controls.create_categorical_control(
            frame, cat_param, 1, parameter_vars, optimize_vars, update_value, update_optimize
        )
        
        # Verify controls were created
        if numeric_control and categorical_control:
            logger.info("Controls created successfully")
        else:
            logger.error("Failed to create controls")
            return False
        
        # Verify variables were stored
        if "test_numeric_control" in parameter_vars and "test_categorical_control" in parameter_vars:
            logger.info("Parameter variables stored correctly")
        else:
            logger.error("Parameter variables not stored correctly")
            return False
        
        # Clean up
        root.destroy()
        
        return True
    except Exception as e:
        logger.error(f"Error in parameter_gui_controls test: {e}")
        return False

def test_parameter_gui_sync():
    """Test the parameter_gui_sync module functionality."""
    logger.info("Testing parameter_gui_sync module...")
    
    try:
        # Create a registry
        registry = get_registry()
        
        # Register test parameters
        num_param = NumericParameter(
            name="test_numeric_sync",
            default=25,
            min_value=0,
            max_value=100,
            step=5,
            description="Test numeric parameter for sync",
            group="test_sync"
        )
        registry.register_parameter(num_param.group, num_param)
        
        cat_param = CategoricalParameter(
            name="test_categorical_sync",
            default="option1",
            choices=["option1", "option2", "option3"],
            description="Test categorical parameter for sync",
            group="test_sync"
        )
        registry.register_parameter(cat_param.group, cat_param)
        
        # Create tkinter variables
        root = tk.Tk()
        parameter_vars = {
            "test_numeric_sync": tk.IntVar(value=25),
            "test_categorical_sync": tk.StringVar(value="option1")
        }
        optimize_vars = {
            "test_numeric_sync": tk.BooleanVar(value=False),
            "test_categorical_sync": tk.BooleanVar(value=False)
        }
        
        # Test getting values from GUI
        values = parameter_gui_sync.get_values_from_gui(parameter_vars)
        logger.info(f"Got values from GUI: {values}")
        
        if values["test_numeric_sync"] == 25 and values["test_categorical_sync"] == "option1":
            logger.info("get_values_from_gui working correctly")
        else:
            logger.error(f"get_values_from_gui returned unexpected values: {values}")
            return False
        
        # Test setting values to GUI
        new_values = {
            "test_numeric_sync": 50,
            "test_categorical_sync": "option2"
        }
        parameter_gui_sync.set_values_to_gui(parameter_vars, new_values)
        logger.info(f"Set values to GUI: {new_values}")
        
        # Verify values were set
        if parameter_vars["test_numeric_sync"].get() == 50 and parameter_vars["test_categorical_sync"].get() == "option2":
            logger.info("set_values_to_gui working correctly")
        else:
            logger.error(f"set_values_to_gui failed to set values correctly")
            return False
        
        # Test setting optimize flags
        optimize_flags = {
            "test_numeric_sync": True,
            "test_categorical_sync": True
        }
        parameter_gui_sync.set_optimizable_to_gui(optimize_vars, optimize_flags)
        logger.info(f"Set optimize flags: {optimize_flags}")
        
        # Verify optimize flags were set
        if optimize_vars["test_numeric_sync"].get() and optimize_vars["test_categorical_sync"].get():
            logger.info("set_optimizable_to_gui working correctly")
        else:
            logger.error(f"set_optimizable_to_gui failed to set flags correctly")
            return False
        
        # Test applying GUI values to registry
        parameter_gui_sync.apply_gui_values_to_registry(parameter_vars, optimize_vars, registry)
        logger.info("Applied GUI values to registry")
        
        # Verify registry values
        if registry.get_parameter("test_numeric_sync").value == 50 and registry.get_parameter("test_categorical_sync").value == "option2":
            logger.info("apply_gui_values_to_registry working correctly")
        else:
            logger.error(f"apply_gui_values_to_registry failed to update registry correctly")
            return False
        
        # Clean up
        root.destroy()
        
        return True
    except Exception as e:
        logger.error(f"Error in parameter_gui_sync test: {e}")
        return False

def test_gui_parameter_manager_facade():
    """Test the GuiParameterManager facade class."""
    logger.info("Testing GuiParameterManager facade...")
    
    try:
        # Create a root window
        root = tk.Tk()
        root.title("GUI Parameter Manager Test")
        root.geometry("800x600")
        
        # Create a frame
        frame = tk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create a GUI parameter manager
        manager = GuiParameterManager()
        logger.info(f"Created GUI parameter manager: {manager}")
        
        # Register test parameters
        registry = manager.registry
        
        # Numeric parameter
        num_param = NumericParameter(
            name="test_numeric_facade",
            default=75,
            min_value=0,
            max_value=100,
            step=5,
            description="Test numeric parameter for facade",
            group="test_facade"
        )
        registry.register_parameter(num_param.group, num_param)
        
        # Categorical parameter
        cat_param = CategoricalParameter(
            name="test_categorical_facade",
            default="option3",
            choices=["option1", "option2", "option3"],
            description="Test categorical parameter for facade",
            group="test_facade"
        )
        registry.register_parameter(cat_param.group, cat_param)
        
        logger.info("Registered test parameters")
        
        # Test getting parameters through facade
        numeric_params = manager.get_numeric_parameters("test_facade")
        categorical_params = manager.get_categorical_parameters("test_facade")
        
        logger.info(f"Got {len(numeric_params)} numeric parameters through facade")
        logger.info(f"Got {len(categorical_params)} categorical parameters through facade")
        
        # Create parameter controls through facade
        controls = manager.create_parameter_controls(frame, "test_facade")
        logger.info(f"Created {len(controls)} parameter controls through facade")
        
        # Test getting values from GUI through facade
        values = manager.get_values_from_gui()
        logger.info(f"Got values from GUI through facade: {values}")
        
        # Test setting values to GUI through facade
        test_values = {
            "test_numeric_facade": 25,
            "test_categorical_facade": "option1"
        }
        manager.set_values_to_gui(test_values)
        logger.info(f"Set values to GUI through facade: {test_values}")
        
        # Test getting values again to verify
        new_values = manager.get_values_from_gui()
        logger.info(f"Got updated values from GUI through facade: {new_values}")
        
        # Verify values were set correctly
        if new_values.get("test_numeric_facade") == 25 and new_values.get("test_categorical_facade") == "option1":
            logger.info("Facade value setting working correctly")
        else:
            logger.error(f"Facade value setting failed: {new_values}")
            return False
        
        # Test setting optimize flags through facade
        optimize_flags = {
            "test_numeric_facade": True,
            "test_categorical_facade": True
        }
        manager.set_optimizable_to_gui(optimize_flags)
        logger.info(f"Set optimize flags through facade: {optimize_flags}")
        
        # Test applying GUI values to registry through facade
        manager.apply_gui_values_to_registry()
        logger.info("Applied GUI values to registry through facade")
        
        # Verify registry values
        num_param_value = registry.get_parameter("test_numeric_facade").value
        cat_param_value = registry.get_parameter("test_categorical_facade").value
        logger.info(f"Registry values - numeric: {num_param_value}, categorical: {cat_param_value}")
        
        # Check if values match what we set
        if num_param_value == test_values["test_numeric_facade"] and cat_param_value == test_values["test_categorical_facade"]:
            logger.info("Facade registry synchronization working correctly")
        else:
            logger.error(f"Facade registry synchronization failed - expected: {test_values}, got: {{'test_numeric_facade': {num_param_value}, 'test_categorical_facade': {cat_param_value}}}")
            return False
        
        # Clean up
        root.destroy()
        logger.info("GUI facade test completed")
        
        return True
    except Exception as e:
        logger.error(f"Error in GUI parameter manager facade test: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    logger.info("Starting V3 parameter GUI refactor validation tests")
    
    tests = [
        ("Parameter GUI Access", test_parameter_gui_access),
        ("Parameter GUI Controls", test_parameter_gui_controls),
        ("Parameter GUI Sync", test_parameter_gui_sync),
        ("GUI Parameter Manager Facade", test_gui_parameter_manager_facade)
    ]
    
    results = {}
    all_passed = True
    
    for name, test_func in tests:
        logger.info(f"Running test: {name}")
        try:
            result = test_func()
            results[name] = result
            if not result:
                all_passed = False
            logger.info(f"Test {name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"Exception in test {name}: {e}")
            results[name] = False
            all_passed = False
    
    # Log summary
    logger.info("=== TEST SUMMARY ===")
    for name, result in results.items():
        logger.info(f"{name}: {'PASSED' if result else 'FAILED'}")
    logger.info(f"Overall result: {'PASSED' if all_passed else 'FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = run_all_tests()
    logger.info(f"Test script completed with status: {'SUCCESS' if success else 'FAILURE'}")
    
    # Write a summary file for quick reference
    summary_file = os.path.join(log_dir, "v3_parameter_gui_test_summary.txt")
    with open(summary_file, "w") as f:
        f.write(f"V3 Parameter GUI Refactor Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Status: {'SUCCESS' if success else 'FAILURE'}\n")
        f.write(f"Log file: {log_file}\n")
    
    sys.exit(0 if success else 1)
