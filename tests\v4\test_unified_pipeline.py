# tests/v4/test_unified_pipeline.py

import os
import pandas as pd
import pytest
from pathlib import Path
from datetime import date

# Add project paths for production modules
project_root = Path(__file__).resolve().parent.parent.parent
settings_path = str(project_root / 'tests/v4/test_settings_small_range.ini')
v4_trace_outputs = project_root / 'v4_trace_outputs'

from v4.run_unified_pipeline import run_unified_pipeline

@pytest.fixture(scope="module")
def historical_window():
    """Fixture for test historical window settings using existing data."""
    return {
        'start_date': '2020-01-01',
        'end_date': '2020-03-31',
        'output_files': [
            v4_trace_outputs / 'signals_output.parquet',
            v4_trace_outputs / 'allocation_history.csv',
            v4_trace_outputs / 'trade_log.csv',
            v4_trace_outputs / 'Signalwtg_output.parquet',
        ]
    }

@pytest.fixture(scope="module")
def cleanup_output_files(historical_window):
    """Delete output files before and after tests."""
    # Teardown: clean up test artifacts
    def remove_files(files):
        for file in files:
            if file.exists():
                os.remove(file)

    # Remove before test
    remove_files(historical_window['output_files'])

    yield

    # Remove after test
    remove_files(historical_window['output_files'])


def test_unified_pipeline_execution(historical_window, cleanup_output_files):
    """
    Test full execution of the unified pipeline using a small test range.
    """
    # Run the pipeline
    results = run_unified_pipeline(custom_settings_file=settings_path)

    # Assert that output files are created
    for file in historical_window['output_files']:
        assert file.exists(), f"Expected output file not found: {file}"

    # Check schema and example row counts from files
    signals_df = pd.read_parquet(historical_window['output_files'][0])
    allocation_df = pd.read_csv(historical_window['output_files'][1])
    trade_log_df = pd.read_csv(historical_window['output_files'][2])

    assert signals_df.shape[0] > 0 and signals_df.shape[1] == 5, "Signals DataFrame has unexpected shape"
    assert allocation_df.shape[0] > 0 and allocation_df.shape[1] == 6, "Allocation history has unexpected shape"
    assert trade_log_df.shape[0] > 0 and trade_log_df.shape[1] == 11, "Trade log has unexpected shape"

    # Compare against historical baselines (mocked threshold values)
    assert signals_df.notnull().all().all(), "Signals DataFrame contains null values"
    assert allocation_df.notnull().all().all(), "Allocation history contains null values"
    assert trade_log_df.notnull().all().all(), "Trade log contains null values"
