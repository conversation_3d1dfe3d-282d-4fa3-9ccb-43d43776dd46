# Task ID: 6
# Title: Create Monthly and Annual Returns Visualization
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Implement monthly returns heatmap and annual returns visualization according to standards
# Details:
This task focuses on creating high-quality monthly and annual returns visualizations:
1. Generate EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png following the standards
2. Create heatmap of monthly returns with years as rows and months as columns
3. Add annual returns in a single column next to the heatmap
4. Implement color bar/legend for return scale with red to green color coding
5. Set proper formatting (title, axis labels, percentages)
6. Ensure high DPI output (≥300, ideally 600) while keeping file size reasonable
7. Validate that returns match those from Allocation History and Trade Log
8. Split functionality into modules under 450 lines per module size limitation rule
9. Strip time components from dates to follow date-only standard

# Test Strategy:

