"""
Test script for validating the refactored GUI parameter manager.
This script tests the core functionality to ensure the refactoring hasn't broken anything.
"""

import os
import sys
import logging
import tkinter as tk
from datetime import datetime
from pathlib import Path

# Add parent directory to path so we can import from the project
parent_dir = str(Path(__file__).parent.parent)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Set up logging
log_dir = os.path.join(parent_dir, "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, f"parameter_gui_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("parameter_gui_test")

# Import the modules we want to test
try:
    from v3_engine.gui_parameter_manager import GuiParameterManager
    from v3_engine.parameter_registry import get_registry, ParameterRegistry
    from v3_engine.parameters import NumericParameter, CategoricalParameter
    logger.info("Successfully imported required modules")
except Exception as e:
    logger.error(f"Error importing modules: {e}")
    sys.exit(1)

def test_parameter_registry():
    """Test basic parameter registry functionality."""
    logger.info("Testing parameter registry...")
    
    try:
        # Get the registry
        registry = get_registry()
        logger.info(f"Got registry: {registry}")
        
        # Register a test parameter
        test_param = NumericParameter(
            name="test_param",
            default=10,
            min_value=0,
            max_value=100,
            step=1,
            description="Test parameter",
            group="test"
        )
        registry.register_parameter(test_param)
        logger.info(f"Registered test parameter: {test_param}")
        
        # Verify parameter is in registry
        params = registry.get_parameters("test")
        if "test_param" in params:
            logger.info("Parameter successfully registered")
        else:
            logger.error("Parameter not found in registry")
        
        return True
    except Exception as e:
        logger.error(f"Error in parameter registry test: {e}")
        return False

def test_gui_parameter_manager():
    """Test the GUI parameter manager functionality."""
    logger.info("Testing GUI parameter manager...")
    
    try:
        # Create a root window
        root = tk.Tk()
        root.title("Parameter GUI Test")
        root.geometry("800x600")
        
        # Create a frame
        frame = tk.Frame(root)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create a GUI parameter manager
        manager = GuiParameterManager()
        logger.info(f"Created GUI parameter manager: {manager}")
        
        # Register test parameters
        registry = manager.registry
        
        # Numeric parameter
        num_param = NumericParameter(
            name="test_numeric",
            default=50,
            min_value=0,
            max_value=100,
            step=5,
            description="Test numeric parameter",
            group="test"
        )
        registry.register_parameter(num_param)
        
        # Categorical parameter
        cat_param = CategoricalParameter(
            name="test_categorical",
            default="option1",
            choices=["option1", "option2", "option3"],
            description="Test categorical parameter",
            group="test"
        )
        registry.register_parameter(cat_param)
        
        logger.info("Registered test parameters")
        
        # Test getting parameters
        numeric_params = manager.get_numeric_parameters("test")
        categorical_params = manager.get_categorical_parameters("test")
        
        logger.info(f"Got {len(numeric_params)} numeric parameters")
        logger.info(f"Got {len(categorical_params)} categorical parameters")
        
        # Create parameter controls
        controls = manager.create_parameter_controls(frame, "test")
        logger.info(f"Created {len(controls)} parameter controls")
        
        # Test getting values from GUI
        values = manager.get_values_from_gui()
        logger.info(f"Got values from GUI: {values}")
        
        # Test setting values to GUI
        test_values = {
            "test_numeric": 75,
            "test_categorical": "option2"
        }
        manager.set_values_to_gui(test_values)
        logger.info(f"Set values to GUI: {test_values}")
        
        # Test getting values again to verify
        new_values = manager.get_values_from_gui()
        logger.info(f"Got updated values from GUI: {new_values}")
        
        # Test setting optimize flags
        optimize_flags = {
            "test_numeric": True,
            "test_categorical": True
        }
        manager.set_optimizable_to_gui(optimize_flags)
        logger.info(f"Set optimize flags: {optimize_flags}")
        
        # Test applying GUI values to registry
        manager.apply_gui_values_to_registry()
        logger.info("Applied GUI values to registry")
        
        # Verify registry values
        num_param_value = registry.get_parameter("test_numeric").value
        cat_param_value = registry.get_parameter("test_categorical").value
        logger.info(f"Registry values - numeric: {num_param_value}, categorical: {cat_param_value}")
        
        # Check if values match what we set
        if num_param_value == test_values["test_numeric"] and cat_param_value == test_values["test_categorical"]:
            logger.info("Values successfully synchronized between GUI and registry")
        else:
            logger.error(f"Value mismatch - expected: {test_values}, got: {{'test_numeric': {num_param_value}, 'test_categorical': {cat_param_value}}}")
        
        # Clean up
        root.destroy()
        logger.info("GUI test completed")
        
        return True
    except Exception as e:
        logger.error(f"Error in GUI parameter manager test: {e}")
        return False

def run_all_tests():
    """Run all tests and report results."""
    logger.info("Starting parameter GUI refactor validation tests")
    
    tests = [
        ("Parameter Registry", test_parameter_registry),
        ("GUI Parameter Manager", test_gui_parameter_manager)
    ]
    
    results = {}
    all_passed = True
    
    for name, test_func in tests:
        logger.info(f"Running test: {name}")
        try:
            result = test_func()
            results[name] = result
            if not result:
                all_passed = False
            logger.info(f"Test {name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            logger.error(f"Exception in test {name}: {e}")
            results[name] = False
            all_passed = False
    
    # Log summary
    logger.info("=== TEST SUMMARY ===")
    for name, result in results.items():
        logger.info(f"{name}: {'PASSED' if result else 'FAILED'}")
    logger.info(f"Overall result: {'PASSED' if all_passed else 'FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = run_all_tests()
    logger.info(f"Test script completed with status: {'SUCCESS' if success else 'FAILURE'}")
    
    # Write a summary file for quick reference
    summary_file = os.path.join(log_dir, "parameter_gui_test_summary.txt")
    with open(summary_file, "w") as f:
        f.write(f"Parameter GUI Refactor Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Status: {'SUCCESS' if success else 'FAILURE'}\n")
        f.write(f"Log file: {log_file}\n")
    
    sys.exit(0 if success else 1)
