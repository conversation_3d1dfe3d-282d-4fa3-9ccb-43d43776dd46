RETRY ASSET INVENTORY
====================
Generated: 2025-01-22

This inventory catalogs all retry-related assets found in the repository for the following search tokens:
- enable_retry_wrapper
- max_retries
- retry_delay_seconds
- retry_on_db_errors
- retry_on_api_errors
- retry_wrapper
- @retry
- retry(

=== CONFIGURATION FILES (*.ini / *.cfg) ===

v4/settings/settings_parameters_v4.ini:
  Line 310: ; enable_retry_wrapper: True = enable retry wrapper around trading phase, False = no retry
  Line 311: enable_retry_wrapper = True
  Line 313: ; max_retries: Maximum number of retry attempts (0 = no retries, 1 = max 1 retry)
  Line 314: max_retries = 1
  Line 316: ; retry_delay_seconds: Delay between retry attempts in seconds
  Line 317: retry_delay_seconds = 5
  Line 319: ; retry_on_db_errors: Whether to retry on database connectivity errors
  Line 320: retry_on_db_errors = True
  Line 322: ; retry_on_api_errors: Whether to retry on API connectivity errors
  Line 323: retry_on_api_errors = True

tests/v4/test_settings_small_range.ini:
  Line 154: enable_retry_wrapper = True
  Line 155: max_retries = 1
  Line 156: retry_delay_seconds = 2
  Line 157: retry_on_db_errors = True
  Line 158: retry_on_api_errors = True

=== PYTHON SOURCE FILES (*.py) ===

v4/utils/retry_wrapper.py:
  Line 4: v4/utils/retry_wrapper.py
  Line 60: max_retries: int = 1,
  Line 62: retry_on_db_errors: bool = True,
  Line 63: retry_on_api_errors: bool = True,
  Line 70: max_retries: Maximum number of retry attempts (0 = no retries)
  Line 72: retry_on_db_errors: Whether to retry on database errors
  Line 73: retry_on_api_errors: Whether to retry on API errors
  Line 76: self.max_retries = max_retries
  Line 78: self.retry_on_db_errors = retry_on_db_errors
  Line 79: self.retry_on_api_errors = retry_on_api_errors
  Line 83: if retry_on_db_errors:
  Line 85: if retry_on_api_errors:
  Line 107: max_retries=system_settings.get('max_retries', 1),
  Line 108: delay_seconds=system_settings.get('retry_delay_seconds', 5.0),
  Line 109: retry_on_db_errors=system_settings.get('retry_on_db_errors', True),
  Line 110: retry_on_api_errors=system_settings.get('retry_on_api_errors', True)
  Line 119: def retry_wrapper(
  Line 129: func: Function to wrap (when used as @retry_wrapper)
  Line 152: if retry_config.max_retries <= 0:
  Line 162: for attempt in range(retry_config.max_retries + 1):
  Line 173: f"Retrying {f.__name__} (attempt {attempt + 1}/{retry_config.max_retries + 1})",
  Line 201: if attempt >= retry_config.max_retries:
  Line 204: f"Max retries ({retry_config.max_retries}) exceeded for {f.__name__}: {type(e).__name__}: {e}"
  Line 231: raise RuntimeError(f"Function {f.__name__} failed after {retry_config.max_retries} retries")
  Line 235: # Handle both @retry_wrapper and @retry_wrapper(...) syntax
  Line 274: if not config or config.max_retries <= 0:
  Line 283: wrapped_function = retry_wrapper(trading_function, config=config, logger=logger)

v4/settings/settings_CPS_v4.py:
  Line 63: RETRY_DELAY_SECONDS = 5  # Delay between retry attempts

v4/run_unified_pipeline.py:
  Line 42: from v4.utils.retry_wrapper import retry_trading_phase, load_retry_config_from_settings
  Line 281: retry_enabled = system_settings.get('enable_retry_wrapper', True)

tests/v4/test_milestone_and_retry.py:
  Line 33: from v4.utils.retry_wrapper import (
  Line 34: retry_wrapper, retry_trading_phase, RetryConfig,
  Line 69: def test_retry_wrapper_basic():
  Line 78: config = RetryConfig(max_retries=2, delay_seconds=1.0)
  Line 81: @retry_wrapper(config=config, logger=logger)
  Line 91: @retry_wrapper(config=config, logger=logger)
  Line 106: def test_retry_wrapper_with_retryable_errors():
  Line 115: config = RetryConfig(max_retries=2, delay_seconds=0.5, retry_on_db_errors=True)
  Line 120: @retry_wrapper(config=config, logger=logger)
  Line 139: @retry_wrapper(config=config, logger=logger)
  Line 167: logger.info(f"Loaded retry config: max_retries={config.max_retries}, "
  Line 171: assert 0 <= config.max_retries <= 10, "max_retries should be between 0 and 10"
  Line 195: config = RetryConfig(max_retries=0)
  Line 214: config = RetryConfig(retry_on_db_errors=True, retry_on_api_errors=True)
  Line 225: config_no_db = RetryConfig(retry_on_db_errors=False, retry_on_api_errors=True)
  Line 237: test_retry_wrapper_basic()
  Line 238: test_retry_wrapper_with_retryable_errors()

=== MARKDOWN/DOCUMENTATION FILES (*.md) ===

docs/step8_milestone_logging_retry.md:
  Line 46: ### 2. Retry Wrapper (`v4/utils/retry_wrapper.py`)
  Line 65: enable_retry_wrapper = True
  Line 66: max_retries = 1
  Line 67: retry_delay_seconds = 5
  Line 68: retry_on_db_errors = True
  Line 69: retry_on_api_errors = True
  Line 75: from v4.utils.retry_wrapper import retry_wrapper, retry_trading_phase
  Line 78: @retry_wrapper
  Line 148: - `enable_retry_wrapper = True`
  Line 149: - `max_retries = 1`
  Line 150: - `retry_delay_seconds = 5`
  Line 154: - `enable_retry_wrapper = False` (for faster failure feedback)
  Line 155: - Or reduce `retry_delay_seconds` to speed up testing
  Line 158: - Consider increasing `retry_delay_seconds` to 10-15 seconds
  Line 159: - Ensure both `retry_on_db_errors` and `retry_on_api_errors` are enabled
  Line 167: │   └── retry_wrapper.py      # Retry wrapper utility

memory-bank/session_updates/2025-06-30_unified_pipeline_issues_and_corrections.md:
  Line 17: - `v4/utils/retry_wrapper.py` (if exists)
  Line 67: enable_retry_wrapper = False  # Set to False or remove entirely
  Line 68: max_retries = 0
  Line 69: retry_delay_seconds = 0

memory-bank/CPS_V4_Unified_Pipeline_Testing_Project.md:
  Line 29: - Retry settings: max_retries=1, delay=2 seconds

=== SUMMARY ===

CONFIGURATION FILES:
- v4/settings/settings_parameters_v4.ini (primary configuration)
- tests/v4/test_settings_small_range.ini (test configuration)

PYTHON SOURCE FILES:
- v4/utils/retry_wrapper.py (main retry logic implementation)
- v4/settings/settings_CPS_v4.py (settings constants)
- v4/run_unified_pipeline.py (retry integration)
- tests/v4/test_milestone_and_retry.py (comprehensive tests)

DOCUMENTATION FILES:
- docs/step8_milestone_logging_retry.md (main documentation)
- memory-bank/session_updates/2025-06-30_unified_pipeline_issues_and_corrections.md (session notes)
- memory-bank/CPS_V4_Unified_Pipeline_Testing_Project.md (project notes)

TOTAL FILES WITH RETRY REFERENCES: 8 files
