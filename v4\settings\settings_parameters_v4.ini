; settings_parameters_v4.ini
; Central Parameter System v4 - Single Source of Truth
; Date: 2025-06-07
; This file contains all parameters for the CPS v4 system

; =======================================================================
; PARAMETER STRUCTURE TEMPLATES
; =======================================================================
; Below are templates for different parameter types. Copy, paste, and modify
; as needed to change or add new parameters.
;
; Type 1: SimpleA - Simple Alphanumeric Value
; Format: variable_name = value
; Example: risk_free_ticker = ^IRX
;
; Type 2: SimpleN - Simple Numeric Value
; Format: variable_name = numeric_value
; Example: initial_capital = 1000000
;
; Type 3: ComplexN - Optimizable Numeric Value
; Format: variable_name = (optimize=True/False, default_value=value, min_value=min, max_value=max, increment=step)
; Example: ema_short_period = (optimize=True, default_value=12, min_value=5, max_value=20, increment=1)
;
; Type 4: AlphaList - List of Values with Pick List
; Format: variable_name = (default_list, picklist_variable)
; Example: tickers = (Group1ETFBase, ETFpicklist)
;
; CONVERTING BETWEEN TYPES:
;
; SimpleN to ComplexN (Make a parameter optimizable): 
; Change: initial_capital = 1000000
; To:     initial_capital = (optimize=True, default_value=1000000, min_value=500000, max_value=2000000, increment=10000)
;
; SimpleA with Options (Convert to pick list):
; Change: signal_algo = ema
; To:     signal_algo = (Strategy_EMA, StrategyAlgos)
;       Where Strategy_EMA and StrategyAlgos are defined in [Lists] section
;
; ADDING NEW LISTS:
; In [Lists] section:
; NewListName = ('item1', 'item2', 'item3')
; NewPicklist = ListName1, ListName2, ListName3

; =====================================================
; PARAMETER LISTS
; =====================================================
; These are named lists that can be referenced by parameters
; To Do = Fix Risk Free Rate

[Lists]
; ETF Groups
Group1ETFBase = ('SPY', 'SHV', 'EFA', 'TLT', 'PFF')
Group2ETF = ('GLD', 'SLV', 'OIL')
Group3ETF = ('VTI', 'BND', 'VEA', 'VWO')
AllETF = ('SPY', 'SHV', 'EFA', 'TLT', 'PFF', 'GLD', 'SLV', 'OIL', 'VTI', 'BND', 'VEA', 'VWO')

; Pick list options
ETFpicklist = Group1ETFBase, Group2ETF, Group3ETF, AllETF

; Benchmark tickers
Benchmark_SP500 = '^GSPC'
Benchmark_DJI = '^DJI'
Benchmark_NASDAQ = '^IXIC'
BenchmarkTickers = (Benchmark_SP500, Benchmark_DJI, Benchmark_NASDAQ)

; Strategy algorithms
; IMPORTANT: Algorithm names should be unquoted strings (e.g. ema not 'ema')
; as they are used directly for dictionary lookups in allocation rules
Strategy_EMA = ema
Strategy_SMA = 'sma'
Strategy_MACD = 'macd'
Strategy_RSI = 'rsi'
Strategy_BB = 'bollinger'
StrategyAlgos = Strategy_EMA, Strategy_SMA, Strategy_MACD, Strategy_RSI, Strategy_BB

; Benchmark strategies
Benchmark_Equal = 'equal_weight'
Benchmark_Market = 'market_cap'
Benchmark_Risk = 'risk_parity'
BenchmarkStrategies = Benchmark_Equal, Benchmark_Market, Benchmark_Risk

; Rebalance frequencies
Rebalance_Daily = 'daily'
Rebalance_Weekly = 'weekly'
Rebalance_Monthly = 'monthly'
Rebalance_Quarterly = 'quarterly'
Rebalance_Yearly = 'yearly'
RebalanceFreqs = Rebalance_Daily, Rebalance_Weekly, Rebalance_Monthly, Rebalance_Quarterly, Rebalance_Yearly

; Performance metrics
Metric_Sharpe = 'sharpe'
Metric_Sortino = 'sortino'
Metric_MaxDD = 'max_drawdown'
Metric_AnnRet = 'annualized_return'
Metric_Vol = 'volatility'
Metric_Calmar = 'calmar'
Metric_Omega = 'omega'
MetricsList = (Metric_Sharpe, Metric_Sortino, Metric_MaxDD, Metric_AnnRet, Metric_Vol, Metric_Calmar, Metric_Omega)
MetricsPicklist = MetricsList

; Chart types
Chart_Perf = 'performance'
Chart_Alloc = 'allocation'
Chart_DD = 'drawdown'
Chart_Rolling = 'rolling_returns'
Chart_Corr = 'correlation'
ChartTypesList = (Chart_Perf, Chart_Alloc, Chart_DD, Chart_Rolling, Chart_Corr)
ChartTypesPicklist = ChartTypesList

; Chart styles
Style_Seaborn = 'seaborn'
Style_Default = 'default'
Style_Dark = 'dark_background'
Style_Minimal = 'bmh'
ChartStyles = Style_Seaborn, Style_Default, Style_Dark, Style_Minimal

; Chart formats
Format_PNG = 'png'
Format_PDF = 'pdf'
Format_SVG = 'svg'
Format_JPG = 'jpg'
ChartFormats = Format_PNG, Format_PDF, Format_SVG, Format_JPG

; Excel formats
Excel_XLSX = 'xlsx'
Excel_XLS = 'xls'
Excel_CSV = 'csv'
ExcelFormats = Excel_XLSX, Excel_XLS, Excel_CSV

; Boolean options
True_Option = True
False_Option = False
BooleanOptions = True_Option, False_Option

; =====================================================
; CORE PARAMETERS
; =====================================================

[Core]
; SimpleA parameters
risk_free_ticker = ^IRX
start_date = 2020-01-01
end_date = 2024-12-31

; SimpleN parameters
; initial_capital parameter is defined in [Backtest] section; duplicate removed
; initial_capital = 1000000
; Removed temporary lookback and top_n - using system_lookback and system_top_n instead

; ComplexN parameters (optimize=True/False, default_value, min_value, max_value, increment)
max_allocation = (optimize=False, default_value=0.25, min_value=0.1, max_value=0.5, increment=0.05)
min_allocation = (optimize=False, default_value=0.0, min_value=0.0, max_value=0.1, increment=0.01)

; AlphaList parameters (default, picklist)
tickers = (Group1ETFBase, ETFpicklist)
benchmark_ticker = (Benchmark_SP500, BenchmarkTickers)

; =====================================================
; STRATEGY PARAMETERS
; =====================================================

[Strategy]
; SimpleA parameters with pick lists (default, picklist)
signal_algo = (Strategy_EMA, StrategyAlgos)

; SimpleN parameters for weight constraints
min_weight = 0.0
max_weight = 1.0
strategy_name = EMA_Crossover

; SimpleN parameters
execution_delay = 1

; ComplexN parameters
ema_short_period = (optimize=False, default_value=12, min_value=5, max_value=20, increment=1)
ema_long_period = (optimize=False, default_value=26, min_value=10, max_value=100, increment=1)
st_lookback = (optimize=False, default_value=15, min_value=5, max_value=30, increment=1)
mt_lookback = (optimize=False, default_value=70, min_value=30, max_value=100, increment=5)
lt_lookback = (optimize=False, default_value=100, min_value=50, max_value=200, increment=10)

; =====================================================
; EMA MODEL PARAMETERS
; =====================================================

[ema_model]
short_period = 12
medium_period = 26
long_period = 50
; st_lookback parameter taken from [Strategy] section - duplicate removed
; mt_lookback parameter taken from [Strategy] section - duplicate removed

; =====================================================
; BACKTEST PARAMETERS
; =====================================================

[Backtest]
; SimpleN parameters
initial_capital = 1000000
commission_rate = 0.001
slippage_rate = 0.0005
; Trade filter deviation threshold (percentage points of total portfolio)
; Controls when rebalancing occurs based on allocation drift
; Lower values = more frequent rebalancing, higher values = less frequent
; Recommended: 0.01-0.02 for daily, 0.02-0.05 for weekly/monthly strategies
deviation_threshold = 0.02

; SimpleA parameters with pick lists (default, picklist)
rebalance_freq = (Rebalance_Monthly, RebalanceFreqs)
benchmark_rebalance_freq = (Rebalance_Yearly, RebalanceFreqs)

; SimpleA (boolean) parameters
signal_history = False
include_cash = True

; =====================================================
; ALLOCATION PARAMETERS
; =====================================================

[Allocation]
; SimpleA (boolean) parameters
history = True
target_chart_include = True

; SimpleA parameters with pick lists (default, picklist)
chart_type = (Chart_Perf, ChartTypesList)

; =====================================================
; REPORT PARAMETERS
; =====================================================

[Report]
; SimpleA (boolean) parameters
create_excel = false
save_trade_log = True
include_summary = True
export_simple_validation_files = true

; SimpleA parameters with pick lists (default, picklist)
excel_format = (Excel_XLSX, ExcelFormats)

; SimpleA parameters for output
output_directory = output

; =====================================================
; PERFORMANCE PARAMETERS
; =====================================================

[Performance]
; SimpleN parameters
risk_free_rate = 0.02

; SimpleA (boolean) parameters
include_benchmark = True

; SimpleA parameters with pick lists (default, picklist)
benchmark_strategy = (Benchmark_Equal, BenchmarkStrategies)

; AlphaList parameters (default, picklist)
metrics = (MetricsList, MetricsPicklist)

; =====================================================
; VISUALIZATION PARAMETERS
; =====================================================

[Visualization]
; SimpleA (boolean) parameters
create_charts = True
colorblind_friendly = False
show_annotations = True

; SimpleA parameters with pick lists (default, picklist)
chart_style = (Style_Seaborn, ChartStyles)
chart_format = (Format_PNG, ChartFormats)

; SimpleN parameters
chart_dpi = 300

; AlphaList parameters (default, picklist)
chart_types = (ChartTypesList, ChartTypesPicklist)

; =====================================================
; OUTPUT PARAMETERS
; =====================================================

[Output]
; SimpleA parameters
path_format = {strategy_name}_{tickers}_{date}
date_format = %Y%m%d

; =====================================================
; SYSTEM PARAMETERS
; =====================================================

[System]
; SimpleA parameters
log_level = INFO
log_file = logs/cps_v4.log

; Unified vs Legacy Pipeline Control
; integration_flag: True = unified in-memory pipeline, False = legacy decoupled pipeline
integration_flag = True

; CSV Signal Output Control (only applies when integration_flag = True)
; retain_csv_signal_output: True = save signal CSV files, False = memory-only handoff
retain_csv_signal_output = True

; ComplexN parameters - System-level core parameters from parameter mapping
system_lookback = (optimize=True, default_value=60, min_value=20, max_value=120, increment=5)
system_top_n = (optimize=False, default_value=2, min_value=1, max_value=5, increment=1)


; =====================================================
; DATA PARAMETERS
; =====================================================

[data_params]
; Required parameters for data loading
; ALL DATA FILTERING PARAMETERS MUST BE VISIBLE HERE (per rules update)
tickers = (Group1ETFBase, ETFpicklist)
start_date = 2020-01-01
end_date = 2025-07-21
price_field = Close
;Read → load from the existing XLSX (error if missing)
;Save → fetch fresh data and write the XLSX
;New → fetch fresh data but don't save
data_storage_mode = Save

; Date filtering parameters (must be in .ini file per rules)
enable_date_filtering = False
filter_start_date = 2020-01-01
filter_end_date = 2024-12-31
