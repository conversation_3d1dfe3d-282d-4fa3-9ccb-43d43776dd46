---
description: Efficient validation process for reviewing filtered logs
---

# Filtered Log Review Workflow
// turbo-all

## Purpose
This workflow provides a standardized process for quickly reviewing filtered logs after any run to validate success or identify errors in the signal generation phase.

## Process

1. After any run, find the latest filtered log:
```
dir S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\filtered_*.txt /b /o-d
```

2. View the latest filtered log for errors or warnings:
```
type S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\filtered_[LATEST_TIMESTAMP].txt
```

3. Verify signal phase outputs exist (independent of trading phase):
```
dir S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_* /b
dir S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_* /b
dir S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signal_history_* /b
dir S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_* /b
dir S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\Signalwtg_output_* /b
```

## Success Criteria

Signal phase successful if:
- Filtered log shows "[MILESTONE] Signal generation complete."
- All expected CSV output files exist

Trading phase successful if:
- No errors in filtered log after "[MILESTONE] Starting Trading Phase"
- Allocation history files are created in v4_trace_outputs

## Common Issues

- Missing CSV outputs: Check if `ema_signal_bridge.py` was called with trace_mode=True
- AttributeError in trading phase: Trading phase issue, but should not impact signal outputs
