# V3 Reporting System Product Requirements Document (PRD)

## 1. Objective

Develop a robust, compliant, and automated reporting system for the V3 backtest engine that generates performance reports, allocation reports, trade logs, and visualizations as specified in v3_performance_reporting_standards_a.md. The system must integrate with the V3 parameter registry, resolve known issues from reporting_system_AI.md, and support scalable AI-driven development with Windsurf and Cline in a Python-based environment on Windows 11.

## 2. Scope

This PRD covers the implementation of the V3 reporting system, including:

* Excel-based performance tables (Performance Table Report with tabs: Signal History, Allocation History, Trade Log).
* PNG visualizations (Allocation Weights, Monthly & Annual Returns, Combined Cumulative Returns & Drawdown).
* Integration with the V3 parameter registry for consistent parameter flow.
* Systematic debugging and testing to address issues like signal history population and adapter integration.
* Verification scripts to ensure compliance with reporting standards.

## 3. Requirements

### 3.1 Functional Requirements

1. **Performance Table Report (XLSX)**
   
   * **Filename**: EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx
   
   * **Tabs**:
     
     * **Signal History**:
       * Rows: Daily dates (YYYY-MM-DD, no timestamps).
       * Columns: Tickers + Cash, with % allocation signals summing to 100% per row.
       * Format: 0.00%.
     
     * **Allocation History**:
       * Matches Signal History structure.
       * Reflects actual capital allocations from executed trades, held constant until the next trade, with a 1-day lag from signals.
       * Each row sums to 100%, no gaps in dates.
       * Format: 0.00%.
       * Includes a PNG visualization as a stacked area or bar chart showing allocation evolution.
     
     * **Trade Log**:
       * Columns: trade_num, symbol, quantity, execution_date, execution_price, commission+slippage, amount, pnl.
       * Sorted by execution_date and trade_num.
       * Includes all executed trades, including final position closures.
   
   * **Header**: Cell A1 in each tab displays main/default parameters (e.g., st_lookback=30, mt_lookback=90, lt_lookback=180, execution_delay=1, top_n=5).
   
   * **Performance Tab**:
     * Columns: Parameters on the left; Metrics on the right.
     * One row per parameter combination, plus a benchmark row at the top.
     * Formatting: Percentages (0.00%), Ratios (0.00), Currency ($#,##0.00).

2. **Monthly & Annual Returns Graphic (PNG)**
   
   * **Filename**: EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png
   
   * **Content**:
     * Heatmap of monthly returns (years as rows, months as columns, color-coded red to green).
     * Annual returns in a single column next to the heatmap.
     * Color bar/legend for return scale.
   
   * **Formatting**:
     * Title: "Monthly Returns".
     * Axis labels: Year (rows), Month (columns: Jan–Dec).
     * Values: Percentages (0.00%), no missing months/years.
     * High DPI (≥300, ideally 600).

3. **Combined Cumulative Returns & Drawdown Graphic (PNG)**
   
   * **Filename**: EMA_V3_1_cumulative_returns_drawdown_YYYYMMDD_HHMMSS.png
   
   * **Content**:
     * Top panel: Cumulative returns for strategy and benchmark.
     * Bottom panel: Drawdowns over the same period.
     * Subtitle with key metrics (CAGR, Sharpe, Sortino, Max DD).
     * Legend showing strategy and benchmark names.
   
   * **Formatting**:
     * Title: "Cumulative Returns & Drawdown".
     * Axis labels: Date (x), Return % (y).
     * Grid lines, value labels at regular intervals.
     * High DPI (≥300, ideally 600).

4. **Parameter Flow and Integration**
   
   * Parameters must flow from GUI → Parameter Registry → Backtest Engine → Performance Reporter Adapter → Reporting Modules.
   
   * Header rows in Excel outputs must dynamically reflect all parameter values.
   
   * Logging must track parameter values at key points in the flow.

5. **Error Handling and Logging**
   
   * Log signal history creation and usage in engine/backtest.py and reporting/allocation_report.py.
   
   * Handle cases where signal_history is None or empty with clear error messages and recovery logic.
   
   * Use DEBUG level logging for high-volume messages (e.g., trade updates) and INFO for significant events.
   
   * Generate v3_debug_{timestamp}.txt log file with parameter values, signal generation logs, and report generation status.

### 3.2 Non-Functional Requirements

1. **Performance**:
   
   * Report generation completes within 5 seconds for standard backtests.
   
   * Excel reports are optimized for size (≤1MB) and load time.
   
   * PNG files are high quality (≥300 DPI) but ≤500KB.

2. **Usability**:
   
   * Clear parameter labeling in Excel and PNG outputs.
   
   * Consistent formatting across all reports.
   
   * Automatic file naming with strategy, timestamp, and content type.

3. **Reliability**:
   
   * Error handling for missing or invalid data.
   
   * Verification checks for report generation.
   
   * Recovery options for failed report generation.

4. **Scalability**:
   
   * Support for multiple strategies, each with custom parameters.
   
   * Handle large datasets (≥5 years of daily data) efficiently.
   
   * Separate core functionality from strategy-specific logic.

5. **Maintainability**:
   
   * Modular code structure with clear separation of concerns (parameter handling, backtest logic, reporting, visualization).
   
   * Comprehensive docstrings and comments for AI-generated code.
   
   * Integration with v3_register_parameters.py for parameter management.

## 4. Implementation Plan

### 4.1 Phase 1: Parameter Registration and Flow

* **Objective**: Ensure all reporting parameters are integrated into the V3 parameter registry.

* **Tasks**:
  
  * Define parameter classes for reporting parameters (create_excel, create_charts, metrics, chart_types).
  
  * Register these parameters in v3_register_parameters.py.
  
  * Update v3_engine/performance_reporter_adapter.py to retrieve and pass these parameters to reporting modules.
  
  * Modify reporting/performance_reporting.py and reporting/allocation_report.py to use parameters from the adapter.

### 4.2 Phase 2: Signal History and Allocation Report Fixes

* **Objective**: Resolve issues with signal history population and allocation report generation.

* **Tasks**:
  
  * Review and improve logging in engine/backtest.py to track signal_history creation.
  
  * Add validation checks in reporting/allocation_report.py to handle None or empty signal_history cases.
  
  * Fix date formatting in allocation reports to strip time components.
  
  * Update allocation report visualization to improve chart quality and readability.

### 4.3 Phase 3: Report Generation

* **Objective**: Implement Excel and PNG report generation per standards.

* **Tasks**:
  
  * Update reporting/performance_reporting.py to generate EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx.
  
  * Create visualization/performance_charts.py to generate monthly returns and cumulative returns PNGs.
  
  * Implement high DPI, proper formatting, and parameter display in all visualizations.
  
  * Add header rows to Excel files with dynamic parameter display.

### 4.4 Phase 4: Verification and Testing

* **Objective**: Ensure reports meet standards and resolve all issues.

* **Tasks**:
  
  * Enhance verify_v3_reporting.py to check:
    * File existence and size (e.g., Excel ≥50KB, PNGs ≥100KB).
    * Content structure (e.g., correct tabs, column headers, formatting).
    * Data integrity (e.g., rows sum to 100%, no missing trades).
  
  * Run run_v3_test_with_verification.bat to automate testing.
  
  * Generate a verification report with pass/fail status and error details.

## 5. Testing Strategy

### 5.1 Unit Tests

* Test parameter registration and retrieval in v3_register_parameters.py and performance_reporter_adapter.py.

* Test signal history generation in backtest.py.

* Test report generation functions in performance_reporting.py and allocation_report.py.

### 5.2 Integration Tests

* Run end-to-end backtest with run_ema_v3_gui_test.bat to verify parameter flow and report generation.

* Check output directory for all required files (Excel, PNGs, CSVs).

* Validate report content against sample tables and graphics in v3_performance_reporting_standards_a.md.

### 5.3 Verification Tests

* Use verify_v3_reporting.py to automate checks for:
  
  * File existence and minimum sizes.
  
  * Log file entries (e.g., "Starting V3 engine", "Completed successfully").
  
  * Excel tab structure and formatting.
  
  * PNG image dimensions and content (e.g., heatmap, dual-panel plot).

## 6. Success Criteria

* All reports (Excel, PNGs) are generated with correct filenames, formats, and content as per v3_performance_reporting_standards_a.md.

* Signal history is consistently populated and used in allocation reports.

* Parameters flow correctly from GUI to reports via the V3 registry and adapter.

* Verification script reports 100% pass rate for file existence, size, and content.

* No errors in debug logs; all expected log entries present.

## 7. Risks and Mitigation

* **Risk**: Signal history remains None or empty.
  * **Mitigation**: Add robust logging and validation in backtest.py; implement fallback logic in allocation_report.py.

* **Risk**: Parameter mismatches between GUI and reports.
  * **Mitigation**: Centralize parameter handling in v3_register_parameters.py and verify in adapter.

* **Risk**: Formatting errors in Excel/PNG outputs.
  * **Mitigation**: Use templates and unit tests to enforce formatting standards.

## 8. Development Guidance

* Test, package up and report, and only validate completion after user confirmation.

* Use specific, standards-referenced prompts for code generation, tests, and documentation.

* Break tasks into small, testable chunks, validating each before proceeding.
