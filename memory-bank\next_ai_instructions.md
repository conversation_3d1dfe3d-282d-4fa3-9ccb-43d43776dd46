# Next AI Agent Instructions - Optimization and Validation Phase

## CRITICAL PRIORITIES (Execute in Order)

### 1. COMPREHENSIVE EQUIVALENCE TESTING [HIGHEST PRIORITY]

**Objective**: Validate that optimized pipeline produces identical results to original

**Actions Required**:

- Create and validate new run, with new settings and data downloads.
  Run both original and optimized pipelines on identical test datasets
- Use full production code flow (no mock data per rules)
- Compare outputs at each major stage:
  - Signal generation results (CSV files in v4_trace_outputs/)
  - Final trading decisions and allocations
  - Performance metrics and statistics
- Document any deviations > 0.1% with root cause analysis
- If deviations found, identify and fix before proceeding to step 2

**Test Location**: Use `tests/v4` directory structure
**Key Files to Compare**: All output files in v4_trace_outputs/, final reports

### 2. Create full final report suite

- create process to follow report specifications and carefully monitor for compliance to specs

- # 3  Integrate full GUI controls

---

**Key Commands**:

- Production Pipeline: Check execution_reference.md for current command
- Test Location: tests/v4/
- Settings File: v4/settings/settings_CPS_v4.py

*Updated: 2025-01-27 - Optimization Validation Phase*
