#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CLI-Compatible Equivalence Testing for CPS v4 Pipeline Comparison

This script compares pre-generated output files from decoupled vs unified pipeline 
execution to verify equivalence. It provides comprehensive assertions for numerical 
equivalence with detailed difference reporting and fail-fast behavior.

Usage:
    python test_equivalence_cli.py --decoupled-signals <file> --decoupled-allocation <file> 
                                   --decoupled-trades <file> --unified-signals <file>
                                   --unified-allocation <file> --unified-trades <file>
                                   [--output-dir <dir>] [--timestamp <ts>]

Author: AI Assistant  
Date: 2025-01-21
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import utilities
try:
    from tests.v4.equivalence_utils import (
        compare_dataframes_detailed,
        save_comparison_report,
        log_test_milestone
    )
except ImportError:
    # Fallback to inline implementations if utilities aren't available
    def compare_dataframes_detailed(df1: pd.DataFrame, df2: pd.DataFrame, 
                                  tolerance: float = 1e-6, label: str = "") -> Tuple[bool, str]:
        """Compare two dataframes with detailed reporting."""
        try:
            # Shape comparison
            if df1.shape != df2.shape:
                return False, f"Shape mismatch: {df1.shape} vs {df2.shape}"
            
            # Index comparison
            if not df1.index.equals(df2.index):
                return False, f"Index mismatch"
            
            # Column comparison
            if not df1.columns.equals(df2.columns):
                return False, f"Column mismatch: {list(df1.columns)} vs {list(df2.columns)}"
            
            # Data comparison
            for col in df1.columns:
                if df1[col].dtype.kind in 'fc':  # float or complex
                    if not np.allclose(df1[col].values, df2[col].values, 
                                      atol=tolerance, rtol=tolerance, equal_nan=True):
                        max_diff = np.nanmax(np.abs(df1[col].values - df2[col].values))
                        return False, f"Column '{col}' numeric values differ (max diff: {max_diff})"
                else:
                    if not df1[col].equals(df2[col]):
                        return False, f"Column '{col}' non-numeric values differ"
            
            return True, "DataFrames are equivalent"
            
        except Exception as e:
            return False, f"Comparison error: {str(e)}"

    def save_comparison_report(report_content: str, output_file: Path) -> None:
        """Save comparison report to file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

    def log_test_milestone(logger: logging.Logger, message: str) -> None:
        """Log a test milestone."""
        logger.info(f"[MILESTONE] {message}")

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Test configuration
TOLERANCE = 1e-6  # Float comparison tolerance


def load_dataframe(file_path: str, file_type: str) -> pd.DataFrame:
    """Load a dataframe from file with proper error handling."""
    path = Path(file_path)
    
    if not path.exists():
        raise FileNotFoundError(f"{file_type} file not found: {file_path}")
    
    logger.info(f"Loading {file_type} from {path.name}")
    
    try:
        if path.suffix.lower() == '.parquet':
            df = pd.read_parquet(file_path)
        elif path.suffix.lower() == '.csv':
            # Try to read with Date as index (handle both 'Date' and 'date')
            try:
                df = pd.read_csv(file_path, index_col='Date', parse_dates=True)
            except KeyError:
                try:
                    df = pd.read_csv(file_path, index_col='date', parse_dates=True) 
                except KeyError:
                    # If no Date/date column, read normally
                    df = pd.read_csv(file_path)
        else:
            raise ValueError(f"Unsupported file format: {path.suffix}")
        
        logger.info(f"Loaded {file_type} with shape {df.shape}")
        return df
        
    except Exception as e:
        logger.error(f"Error loading {file_type} from {file_path}: {e}")
        raise


def compare_file_pair(decoupled_file: str, unified_file: str, file_type: str,
                     tolerance: float = TOLERANCE) -> Tuple[bool, str, Dict[str, Any]]:
    """Compare a pair of output files between decoupled and unified pipelines."""
    
    log_test_milestone(logger, f"Comparing {file_type} files")
    
    try:
        # Load both dataframes
        decoupled_df = load_dataframe(decoupled_file, f"decoupled {file_type}")
        unified_df = load_dataframe(unified_file, f"unified {file_type}")
        
        # Perform detailed comparison
        is_equivalent, comparison_message = compare_dataframes_detailed(
            decoupled_df, unified_df, tolerance=tolerance, label=file_type
        )
        
        # Collect comparison stats
        stats = {
            'decoupled_shape': decoupled_df.shape,
            'unified_shape': unified_df.shape,
            'columns_match': list(decoupled_df.columns) == list(unified_df.columns),
            'index_match': decoupled_df.index.equals(unified_df.index) if hasattr(decoupled_df.index, 'equals') else True,
            'is_equivalent': is_equivalent,
            'comparison_message': comparison_message
        }
        
        if is_equivalent:
            logger.info(f"✓ {file_type} files are equivalent")
        else:
            logger.error(f"✗ {file_type} files are NOT equivalent: {comparison_message}")
        
        return is_equivalent, comparison_message, stats
        
    except Exception as e:
        error_msg = f"Error comparing {file_type}: {str(e)}"
        logger.error(error_msg)
        return False, error_msg, {'error': str(e)}


def generate_equivalence_report(results: Dict[str, Tuple[bool, str, Dict]], 
                               output_dir: Path, timestamp: str) -> str:
    """Generate comprehensive equivalence test report."""
    
    report_lines = []
    report_lines.append("="*80)
    report_lines.append("CPS V4 PIPELINE EQUIVALENCE TEST REPORT")
    report_lines.append("="*80)
    report_lines.append(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report_lines.append(f"Test ID: {timestamp}")
    report_lines.append("")
    
    # Summary
    total_tests = len(results)
    passed_tests = sum(1 for is_equiv, _, _ in results.values() if is_equiv)
    failed_tests = total_tests - passed_tests
    
    report_lines.append("SUMMARY:")
    report_lines.append(f"- Total Comparisons: {total_tests}")
    report_lines.append(f"- Passed: {passed_tests}")
    report_lines.append(f"- Failed: {failed_tests}")
    report_lines.append(f"- Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "- Success Rate: N/A")
    report_lines.append("")
    
    # Detailed results
    report_lines.append("DETAILED RESULTS:")
    report_lines.append("-"*40)
    
    for file_type, (is_equiv, message, stats) in results.items():
        status = "PASS" if is_equiv else "FAIL"
        report_lines.append(f"{file_type}: {status}")
        
        if 'decoupled_shape' in stats and 'unified_shape' in stats:
            report_lines.append(f"  Decoupled Shape: {stats['decoupled_shape']}")
            report_lines.append(f"  Unified Shape: {stats['unified_shape']}")
        
        if 'columns_match' in stats:
            report_lines.append(f"  Columns Match: {stats['columns_match']}")
        
        if 'index_match' in stats:
            report_lines.append(f"  Index Match: {stats['index_match']}")
        
        report_lines.append(f"  Result: {message}")
        report_lines.append("")
    
    # Final status
    report_lines.append("="*80)
    if failed_tests == 0:
        report_lines.append("OVERALL RESULT: PASS - Pipelines are equivalent!")
    else:
        report_lines.append("OVERALL RESULT: FAIL - Pipelines are NOT equivalent!")
    report_lines.append("="*80)
    
    report_content = "\n".join(report_lines)
    
    # Save report to file
    report_file = output_dir / f"equivalence_report_{timestamp}.txt"
    save_comparison_report(report_content, report_file)
    logger.info(f"Equivalence report saved to: {report_file}")
    
    return report_content


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='CPS v4 Pipeline Equivalence Test - Compare output files from decoupled vs unified pipelines',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_equivalence_cli.py \\
    --decoupled-signals decoupled_signals_20250121_140000.parquet \\
    --decoupled-allocation decoupled_allocation_20250121_140000.csv \\
    --decoupled-trades decoupled_trades_20250121_140000.csv \\
    --unified-signals unified_signals_20250121_140000.parquet \\
    --unified-allocation unified_allocation_20250121_140000.csv \\
    --unified-trades unified_trades_20250121_140000.csv \\
    --output-dir equivalence_test_outputs \\
    --timestamp 20250121_140000
        """
    )
    
    # Required file arguments
    parser.add_argument('--decoupled-signals', required=True,
                       help='Path to decoupled signals output file (.parquet)')
    parser.add_argument('--decoupled-allocation', required=True, 
                       help='Path to decoupled allocation history file (.csv)')
    parser.add_argument('--decoupled-trades', required=True,
                       help='Path to decoupled trade log file (.csv)')
    parser.add_argument('--unified-signals', required=True,
                       help='Path to unified signals output file (.parquet)')
    parser.add_argument('--unified-allocation', required=True,
                       help='Path to unified allocation history file (.csv)')
    parser.add_argument('--unified-trades', required=True,
                       help='Path to unified trade log file (.csv)')
    
    # Optional arguments
    parser.add_argument('--output-dir', default='equivalence_test_outputs',
                       help='Directory to save comparison reports (default: equivalence_test_outputs)')
    parser.add_argument('--timestamp', default=datetime.now().strftime('%Y%m%d_%H%M%S'),
                       help='Timestamp for output files (default: current timestamp)')
    parser.add_argument('--tolerance', type=float, default=TOLERANCE,
                       help=f'Numerical comparison tolerance (default: {TOLERANCE})')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    return parser.parse_args()


def main() -> int:
    """Main entry point for equivalence testing."""
    args = parse_arguments()
    
    # Setup verbose logging if requested
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info("="*80)
    logger.info("CPS V4 PIPELINE EQUIVALENCE TEST")
    logger.info("="*80)
    logger.info(f"Test ID: {args.timestamp}")
    logger.info(f"Output Directory: {output_dir.absolute()}")
    logger.info(f"Tolerance: {args.tolerance}")
    logger.info("")
    
    # File pairs to compare
    file_comparisons = [
        ('signals', args.decoupled_signals, args.unified_signals),
        ('allocation_history', args.decoupled_allocation, args.unified_allocation), 
        ('trade_log', args.decoupled_trades, args.unified_trades)
    ]
    
    # Perform comparisons
    results = {}
    overall_success = True
    
    for file_type, decoupled_file, unified_file in file_comparisons:
        logger.info(f"Comparing {file_type} files...")
        logger.info(f"  Decoupled: {decoupled_file}")
        logger.info(f"  Unified: {unified_file}")
        
        try:
            is_equivalent, message, stats = compare_file_pair(
                decoupled_file, unified_file, file_type, tolerance=args.tolerance
            )
            results[file_type] = (is_equivalent, message, stats)
            
            if not is_equivalent:
                overall_success = False
                
        except Exception as e:
            logger.error(f"Error comparing {file_type}: {e}")
            results[file_type] = (False, f"Comparison error: {str(e)}", {'error': str(e)})
            overall_success = False
        
        logger.info("")
    
    # Generate comprehensive report
    logger.info("Generating equivalence test report...")
    report_content = generate_equivalence_report(results, output_dir, args.timestamp)
    
    # Print summary to console
    logger.info("")
    logger.info("="*80)
    logger.info("EQUIVALENCE TEST SUMMARY")
    logger.info("="*80)
    
    for file_type, (is_equiv, message, stats) in results.items():
        status = "✓ PASS" if is_equiv else "✗ FAIL"
        logger.info(f"{file_type}: {status}")
        if not is_equiv:
            logger.info(f"  Issue: {message}")
    
    logger.info("")
    if overall_success:
        logger.info("🎉 OVERALL RESULT: PASS - Pipelines are equivalent!")
        return 0
    else:
        logger.error("❌ OVERALL RESULT: FAIL - Pipelines are NOT equivalent!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
