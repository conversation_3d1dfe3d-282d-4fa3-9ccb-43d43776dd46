# Execution Reference - Exact Commands & Locations

## BAT FILES IN USE (Tested)

### Unified Production Pipeline (PRIMARY - OPTIMIZED)

```batch
run_main_v4_unified.bat
```

- **Location:** Root directory
- **Input:** Market data from configured sources
- **Output:** `v4_trace_outputs/` directory with timestamped files:
  - `signals_output_YYYYMMDD_HHMMSS.csv`
  - `allocation_history_YYYYMMDD_HHMMSS.csv`
  - `trade_log_YYYYMMDD_HHMMSS.csv`
- **Status:** ACTIVE - Primary production workflow

### Signal Generation Only (LEGACY - for testing)

```batch
run_main_v4_signalalgo.bat
```

- **Location:** Root directory  
- **Input:** Market data
- **Output:** 
  - `v4_trace_outputs/signals_output_YYYYMMDD_HHMMSS.csv`
- **Status:** Working (optimized for single timestamped output)

### Trading Phase Only (LEGACY - for testing)

```batch
run_trading_phase_standalone.bat
```

- **Location:** Root directory
- **Input:** Signal files from v4_trace_outputs/
- **Output:**
  - `v4_trace_outputs/allocation_history_YYYYMMDD_HHMMSS.csv`
  - `v4_trace_outputs/trade_log_YYYYMMDD_HHMMSS.csv`
- **Status:** Working (for equivalence testing)

## KEY DIRECTORIES

### Input Data

- **Location:** `data/` directory
- **Content:** Market data files

### Configuration

- **Location:** `CPS_v4/settings_CPS_v4.py`
- **Content:** All system parameters

### Output

- **Location:** `v4_trace_outputs/`
- **Content:** All V4 execution outputs

### Core Engine

- **Location:** `v4/engine/`
- **Content:** Core backtesting modules

### Models

- **Location:** `v4/models/`
- **Content:** Signal generation models

## OPTIMIZED UNIFIED SYSTEM FLOW

```mermaid
graph LR
    A[Market Data] --> B[Unified Pipeline]
    B --> C[signals_output_timestamp.csv]
    B --> D[allocation_history_timestamp.csv]
    B --> E[trade_log_timestamp.csv]

    style B fill:#d4f1f9
    style C fill:#fff2cc
    style D fill:#fff2cc
    style E fill:#fff2cc
```

## TESTING COMMANDS

### Check System Status (OPTIMIZED)

```batch
# Run unified pipeline (PRIMARY)
run_main_v4_unified.bat

# Check timestamped outputs
dir v4_trace_outputs\*_*.csv
```

### Verify Signal Generation

```batch
# Generate signals only
run_main_v4_signalalgo.bat

# Verify output
type v4_trace_outputs\signals_output_*.csv
```

## KNOWN WORKING PATHS

- Configuration: `CPS_v4\settings_CPS_v4.py`
- Main Engine: `v4\engine\backtest_v4.py`
- Signal Model: `v4\models\ema_allocation_model_v4.py`
- Output Directory: `v4_trace_outputs\`
