"""
Simplified test script for the v2 backtest engine.
This script focuses on testing the core functionality without complex reporting.
"""

import pandas as pd
import numpy as np
from datetime import date, datetime
import logging
import sys
import os
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import local modules
from config.paths import *
from data.data_loader import load_data_for_backtest
from models.ema_allocation_model import ema_allocation_model
from engine.backtest import BacktestEngine

def adapter_ema_allocation(price_data, **params):
    """
    Adapter function to convert from price_data to the format expected by ema_allocation_model.
    """
    # Extract parameters
    st_lookback = params.get('st_lookback', 10)
    mt_lookback = params.get('mt_lookback', 50)
    lt_lookback = params.get('lt_lookback', 150)
    
    # Call the existing model
    weights = ema_allocation_model(
        price_data=price_data,
        returns_data=None,  # Not needed for EMA model
        st_lookback=st_lookback,
        mt_lookback=mt_lookback,
        lt_lookback=lt_lookback
    )
    
    return weights

def run_simple_test():
    """Run a simplified test of the v2 backtest engine."""
    logger.info("Starting simplified test of v2 backtest engine")
    
    # Define test parameters
    test_params = {
        'data_params': {
            'tickers': ['SPY', 'TLT', 'GLD', 'QQQ', 'EFA'],
            'start_date': '2020-01-01',
            'end_date': '2022-12-31',
            'price_field': 'Close'
        },
        'backtest_params': {
            'strategy': 'ema',
            'rebalance_freq': 'weekly',
            'execution_delay': 1,
            'commission_rate': 0.001,
            'slippage_rate': 0.001,
            'initial_capital': 100000.0
        },
        'strategy_params': {
            'st_lookback': 10,
            'mt_lookback': 50,
            'lt_lookback': 150
        }
    }
    
    # Load data
    logger.info("Loading data...")
    try:
        # Load price data directly from CSV files
        data_dir = DATA_DIR
        price_data = pd.DataFrame()
        
        for ticker in test_params['data_params']['tickers']:
            try:
                file_path = os.path.join(data_dir, f"{ticker}.csv")
                if os.path.exists(file_path):
                    ticker_data = pd.read_csv(file_path, index_col=0, parse_dates=True)
                    price_data[ticker] = ticker_data['Close']
                else:
                    logger.warning(f"Data file for {ticker} not found")
            except Exception as e:
                logger.error(f"Error loading data for {ticker}: {e}")
        
        # Filter by date range
        start_date = pd.to_datetime(test_params['data_params']['start_date'])
        end_date = pd.to_datetime(test_params['data_params']['end_date'])
        price_data = price_data.loc[start_date:end_date]
        
        # Drop any rows with NaN values
        price_data = price_data.dropna()
        
        if price_data.empty:
            logger.error("No valid price data available")
            return
        
        logger.info(f"Loaded price data with {len(price_data)} rows and {len(price_data.columns)} columns")
        
    except Exception as e:
        logger.error(f"Error loading data: {e}")
        # Try using the data loader as fallback
        try:
            data = load_data_for_backtest(test_params)
            price_data = data['price_data']
            logger.info(f"Loaded price data using data_loader with {len(price_data)} rows")
        except Exception as e2:
            logger.error(f"Error using data_loader as fallback: {e2}")
            return
    
    # Create backtest engine
    engine = BacktestEngine(
        initial_capital=test_params['backtest_params']['initial_capital'],
        commission_rate=test_params['backtest_params']['commission_rate'],
        slippage_rate=test_params['backtest_params']['slippage_rate']
    )
    
    # Run backtest
    try:
        param_combinations = get_parameter_combinations(strategy_params)
        results = engine.run_backtest(
            price_data=price_data,
            signal_generator=adapter_ema_allocation,
            rebalance_freq=test_params['backtest_params']['rebalance_freq'],
            execution_delay=test_params['backtest_params']['execution_delay'],
            param_combinations=param_combinations
        )
        
        # Print summary
        print("\n" + "="*80)
        print(f"SIMPLIFIED V2 ENGINE TEST RESULTS")
        print("="*80)
        
        # Performance metrics
        print("\nPERFORMANCE METRICS:")
        print(f"Initial Capital: ${results['initial_capital']:,.2f}")
        print(f"Final Value: ${results['final_value']:,.2f}")
        print(f"Total Return: {results['total_return']:.2%}")
        print(f"CAGR: {results['performance']['cagr']:.2%}")
        print(f"Volatility: {results['performance']['volatility']:.2%}")
        print(f"Sharpe Ratio: {results['performance']['sharpe']:.2f}")
        print(f"Max Drawdown: {results['performance']['max_drawdown']:.2%}")
        
        # Save results to CSV
        timestamp = datetime.now().strftime('%Y-%m-%d_%H%M%S')
        output_dir = os.path.join(OUTPUT_DIR, "v2_tests")
        os.makedirs(output_dir, exist_ok=True)
        
        # Save returns
        if not results['strategy_returns'].empty:
            returns_file = os.path.join(output_dir, f"v2_returns_{timestamp}.csv")
            results['strategy_returns'].to_csv(returns_file)
            print(f"\nSaved returns to {returns_file}")
        
        # Save weights
        if not results['weights_history'].empty:
            weights_file = os.path.join(output_dir, f"v2_weights_{timestamp}.csv")
            results['weights_history'].to_csv(weights_file)
            print(f"Saved weights to {weights_file}")
        
        # Save trade log
        if 'trade_log' in results and not results['trade_log'].empty:
            trades_file = os.path.join(output_dir, f"v2_trades_{timestamp}.csv")
            results['trade_log'].to_csv(trades_file)
            print(f"Saved trade log to {trades_file}")
        
        print("="*80 + "\n")
        
        return results
        
    except Exception as e:
        logger.error(f"Error running backtest: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    run_simple_test()
