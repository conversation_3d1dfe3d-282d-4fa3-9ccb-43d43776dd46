===== V4 TRADING PHASE =====
2025-06-26 18:17:28,990 - INFO - Successfully loaded settings for ema_allocation_model_v4.
2025-06-26 18:17:29,015 - INFO - [MILESTONE] Starting Trading Phase
2025-06-26 18:17:29,019 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
2025-06-26 18:17:29,392 - INFO - Loaded price data with shape (1370, 5)
2025-06-26 18:17:29,393 - INFO - Date range: 2020-01-02 00:00:00 to 2025-06-13 00:00:00
2025-06-26 18:17:29,393 - INFO - Tickers: SPY, SHV, EFA, TLT, PFF
2025-06-26 18:17:29,455 - INFO - Loaded signals with shape (1370, 5)
2025-06-26 18:17:29,456 - INFO - Signals date range: 2020-01-02 00:00:00 to 2025-06-13 00:00:00
2025-06-26 18:17:29,524 - INFO - Initializing backtest engine
2025-06-26 18:17:29,525 - INFO - Initialized backtest engine with $1,000,000.00 capital
2025-06-26 18:17:29,525 - INFO - Commission rate: 0.10%
2025-06-26 18:17:29,525 - INFO - Slippage rate: 0.05%
2025-06-26 18:17:29,525 - INFO - Running backtest with pre-computed signals
2025-06-26 18:17:29,525 - INFO - Starting backtest with pre-computed signals
2025-06-26 18:17:29,525 - INFO - Initializing backtest components
2025-06-26 18:17:29,532 - WARNING - [2020-01-03] Position SPY not found
2025-06-26 18:17:29,532 - WARNING - [2020-01-03] Position SHV not found
2025-06-26 18:17:29,548 - WARNING - [2020-01-14] Position PFF not found
2025-06-26 18:17:29,561 - WARNING - [2020-01-22] Insufficient quantity to remove 7749 shares of SPY
2025-06-26 18:17:29,579 - WARNING - [2020-01-31] Position SPY not found
2025-06-26 18:17:29,587 - WARNING - [2020-02-05] Position PFF not found
2025-06-26 18:17:29,626 - WARNING - [2020-02-27] Position SPY not found
2025-06-26 18:17:29,774 - WARNING - [2020-05-28] Position SHV not found
2025-06-26 18:17:29,795 - WARNING - [2020-06-09] Position TLT not found
2025-06-26 18:17:29,832 - WARNING - [2020-06-30] Position EFA not found
2025-06-26 18:17:29,838 - WARNING - [2020-07-02] Position TLT not found
2025-06-26 18:17:29,853 - WARNING - [2020-07-13] Position EFA not found
2025-06-26 18:17:29,858 - WARNING - [2020-07-15] Position TLT not found
2025-06-26 18:17:29,889 - WARNING - [2020-07-31] Position EFA not found
2025-06-26 18:17:29,908 - WARNING - [2020-08-12] Position TLT not found
2025-06-26 18:17:29,975 - WARNING - [2020-09-24] Position EFA not found
2025-06-26 18:17:30,058 - WARNING - [2020-11-11] Position PFF not found
2025-06-26 18:17:30,072 - WARNING - [2020-11-19] Insufficient quantity to remove 247549 shares of SPY
2025-06-26 18:17:30,211 - WARNING - [2021-02-16] Insufficient quantity to remove 385121 shares of SPY
2025-06-26 18:17:30,420 - WARNING - [2021-06-22] Insufficient quantity to remove 2661774 shares of EFA
2025-06-26 18:17:30,458 - WARNING - [2021-07-16] Position EFA not found
2025-06-26 18:17:30,502 - WARNING - [2021-08-12] Position TLT not found
2025-06-26 18:17:30,513 - WARNING - [2021-08-19] Position EFA not found
2025-06-26 18:17:30,540 - WARNING - [2021-09-03] Position TLT not found
2025-06-26 18:17:30,565 - WARNING - [2021-09-20] Position EFA not found
2025-06-26 18:17:30,580 - WARNING - [2021-09-28] Position TLT not found
2025-06-26 18:17:30,586 - WARNING - [2021-09-30] Position EFA not found
2025-06-26 18:17:30,645 - WARNING - [2021-11-03] Position PFF not found
2025-06-26 18:17:30,685 - WARNING - [2021-11-26] Position EFA not found
2025-06-26 18:17:30,743 - WARNING - [2021-12-31] Position TLT not found
2025-06-26 18:17:30,762 - WARNING - [2022-01-12] Position PFF not found
2025-06-26 18:17:30,778 - WARNING - [2022-01-24] Position EFA not found
2025-06-26 18:17:30,824 - WARNING - [2022-02-22] Position SPY not found
2025-06-26 18:17:30,834 - WARNING - [2022-02-28] Position EFA not found
2025-06-26 18:17:30,846 - WARNING - [2022-03-07] Position SPY not found
2025-06-26 18:17:30,862 - WARNING - [2022-03-15] Position TLT not found
2025-06-26 18:17:30,885 - WARNING - [2022-03-29] Insufficient quantity to remove 196977421 shares of SHV
2025-06-26 18:17:30,968 - WARNING - [2022-05-16] Position SPY not found
2025-06-26 18:17:31,111 - WARNING - [2022-08-10] Position SHV not found
2025-06-26 18:17:31,142 - WARNING - [2022-08-30] Position PFF not found
2025-06-26 18:17:31,170 - WARNING - [2022-09-16] Position SPY not found
2025-06-26 18:17:31,239 - WARNING - [2022-10-24] Position PFF not found
2025-06-26 18:17:31,272 - WARNING - [2022-11-10] Position SPY not found
2025-06-26 18:17:31,293 - WARNING - [2022-11-22] Position SHV not found
2025-06-26 18:17:31,336 - WARNING - [2022-12-19] Position SPY not found
2025-06-26 18:17:31,341 - WARNING - [2022-12-21] Position TLT not found
2025-06-26 18:17:31,382 - WARNING - [2023-01-17] Position SHV not found
2025-06-26 18:17:31,431 - WARNING - [2023-02-14] Position PFF not found
2025-06-26 18:17:31,448 - WARNING - [2023-02-24] Position SPY not found
2025-06-26 18:17:31,476 - WARNING - [2023-03-13] Position PFF not found
2025-06-26 18:17:31,501 - WARNING - [2023-03-24] Position SHV not found
2025-06-26 18:17:31,508 - WARNING - [2023-03-29] Position TLT not found
2025-06-26 18:17:31,513 - WARNING - [2023-03-31] Position SHV not found
2025-06-26 18:17:31,749 - WARNING - [2023-08-17] Position EFA not found
2025-06-26 18:17:31,900 - WARNING - [2023-11-15] Insufficient quantity to remove 84896145987 shares of SHV
2025-06-26 18:17:31,916 - WARNING - [2023-11-24] Position SHV not found
2025-06-26 18:17:31,954 - WARNING - [2023-12-18] Position EFA not found
2025-06-26 18:17:31,989 - WARNING - [2024-01-10] Position TLT not found
2025-06-26 18:17:32,002 - WARNING - [2024-01-19] Position EFA not found
2025-06-26 18:17:32,055 - WARNING - [2024-02-22] Position PFF not found
2025-06-26 18:17:32,328 - WARNING - [2024-08-02] Position EFA not found
2025-06-26 18:17:32,333 - WARNING - [2024-08-06] Insufficient quantity to remove 182628550719 shares of SPY
2025-06-26 18:17:32,398 - WARNING - [2024-09-11] Position SPY not found
2025-06-26 18:17:32,423 - WARNING - [2024-09-25] Insufficient quantity to remove 1874024802821 shares of TLT
2025-06-26 18:17:32,425 - WARNING - [2024-09-26] Position TLT not found
2025-06-26 18:17:32,430 - WARNING - [2024-09-30] Insufficient quantity to remove 10316730104104 shares of PFF
2025-06-26 18:17:32,561 - WARNING - [2024-12-16] Position PFF not found
2025-06-26 18:17:32,643 - WARNING - [2025-02-05] Position SHV not found
2025-06-26 18:17:32,691 - WARNING - [2025-03-06] Position SPY not found
2025-06-26 18:17:32,696 - WARNING - [2025-03-10] Position SHV not found
2025-06-26 18:17:32,698 - WARNING - [2025-03-11] Position TLT not found
2025-06-26 18:17:32,713 - WARNING - [2025-03-19] Position SHV not found
2025-06-26 18:17:32,721 - WARNING - [2025-03-24] Position TLT not found
2025-06-26 18:17:32,739 - WARNING - [2025-04-02] Position SHV not found
2025-06-26 18:17:32,748 - WARNING - [2025-04-07] Position EFA not found
2025-06-26 18:17:32,781 - WARNING - [2025-04-16] Position TLT not found
2025-06-26 18:17:32,847 - WARNING - [2025-05-16] Position SHV not found
2025-06-26 18:17:32,896 - INFO - Calculating final results...
2025-06-26 18:17:32,901 - INFO - SUCCESS: weights_history appears valid.

Data sample:
                 SPY       SHV  EFA       TLT       PFF
Date                                                   
2020-01-02  0.599987  0.400013  NaN       NaN       NaN
2020-01-03       NaN       NaN  NaN  0.396944  0.264682
2020-01-06       NaN       NaN  NaN  0.600574  0.399426
2020-01-07       NaN       NaN  NaN  0.395992  0.263338
2020-01-08       NaN       NaN  NaN  0.600601  0.399399
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:656: FutureWarning: 'M' is deprecated and will be removed in a future version, please use 'ME' instead.
  monthly_returns = strategy_returns.resample('M').apply(lambda x: (1 + x).prod() - 1)
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\backtest_v4.py:662: FutureWarning: 'Y' is deprecated and will be removed in a future version, please use 'YE' instead.
  yearly_returns = strategy_returns.resample('Y').apply(lambda x: (1 + x).prod() - 1)
2025-06-26 18:17:32,950 - INFO - Successfully generated allocation_history with shape: (1370, 6)
2025-06-26 18:17:32,951 - INFO - Allocation history validation passed. Max deviation from 100%: 0.000%
2025-06-26 18:17:32,951 - INFO - Backtest with pre-computed signals completed
2025-06-26 18:17:32,966 - INFO - Saved allocation history to:
2025-06-26 18:17:32,966 - INFO -   - S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\allocation_history_20250626_181732.csv
2025-06-26 18:17:32,987 - INFO - Saved trade log to:
2025-06-26 18:17:32,987 - INFO -   - S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\trade_log_20250626_181732.csv
2025-06-26 18:17:32,987 - INFO - [MILESTONE] Trading phase complete
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\GitHub_Clone\ai-sdlc
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
MILESTONE: Final results calculation complete.
V4 trading phase completed with exit code 
