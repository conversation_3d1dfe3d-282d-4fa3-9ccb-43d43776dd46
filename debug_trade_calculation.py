#!/usr/bin/env python3
"""
Debug script to trace the exact trade calculation issue.
Focus on the first few days to understand why sell quantities are doubled.
"""

import pandas as pd
import sys
from pathlib import Path

# Add project root to path
_project_root = Path(__file__).parent
sys.path.insert(0, str(_project_root))

from v4.engine.portfolio_v4 import Portfolio
from v4.engine.allocation_v4 import compare_positions, generate_orders
from v4.engine.orders_v4 import Order

def debug_first_few_days():
    """Debug the first few days of trading to find the 2x error."""
    
    print("=== DEBUGGING TRADE CALCULATION ===")
    
    # Load the correct signals
    signals_file = _project_root / "v4_trace_outputs" / "signals_output_20250622_192844.csv"
    signals = pd.read_csv(signals_file, index_col='Date', parse_dates=True)
    
    # Load price data
    price_file = _project_root / "v4" / "data" / "tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx"
    prices_df = pd.read_excel(price_file, index_col=0, parse_dates=True)
    
    # Initialize portfolio with $1M
    portfolio = Portfolio()
    print(f"Initial portfolio cash: ${portfolio.cash:,.2f}")
    
    # Day 1: 2020-01-02 - Target: 60% SPY, 40% SHV
    date1 = pd.Timestamp('2020-01-02')
    target1 = {'SPY': 0.6, 'SHV': 0.4, 'EFA': 0.0, 'TLT': 0.0, 'PFF': 0.0}
    prices1 = prices_df.loc[date1].to_dict()
    
    print(f"\n=== DAY 1: {date1.date()} ===")
    print(f"Target allocation: {target1}")
    print(f"Prices: {prices1}")
    
    # Calculate orders for day 1
    current_positions1 = portfolio.get_positions()
    portfolio_value1 = portfolio.get_total_value(prices1)
    
    print(f"Portfolio value: ${portfolio_value1:,.2f}")
    print(f"Current positions: {current_positions1}")
    
    dollar_changes1 = compare_positions(current_positions1, target1, portfolio_value1, prices1)
    print(f"Dollar changes: {dollar_changes1}")
    
    orders1 = generate_orders(dollar_changes1, prices1, date1, portfolio.cash, current_positions1)
    print(f"Generated orders: {[str(o) for o in orders1]}")
    
    # Simulate executing orders (simplified)
    for order in orders1:
        if order.quantity > 0:  # Buy
            portfolio.add_position(order.symbol, order.quantity, order.price)
            portfolio.cash -= order.quantity * order.price
        else:  # Sell
            success, pnl = portfolio.remove_position(order.symbol, abs(order.quantity), order.price)
            portfolio.cash += abs(order.quantity) * order.price
    
    print(f"After day 1 - Cash: ${portfolio.cash:,.2f}")
    print(f"Positions: {portfolio.get_positions()}")
    
    # Day 2: 2020-01-03 - Target: 60% TLT, 40% PFF
    date2 = pd.Timestamp('2020-01-03')
    target2 = {'SPY': 0.0, 'SHV': 0.0, 'EFA': 0.0, 'TLT': 0.6, 'PFF': 0.4}
    prices2 = prices_df.loc[date2].to_dict()
    
    print(f"\n=== DAY 2: {date2.date()} ===")
    print(f"Target allocation: {target2}")
    print(f"Prices: {prices2}")
    
    # Mark to market first
    portfolio.mark_to_market(date2, prices2)
    
    # Calculate orders for day 2
    current_positions2 = portfolio.get_positions()
    portfolio_value2 = portfolio.get_total_value(prices2)
    
    print(f"Portfolio value: ${portfolio_value2:,.2f}")
    print(f"Current positions: {current_positions2}")
    
    # Calculate expected sell quantities
    if 'SPY' in current_positions2:
        spy_qty = current_positions2['SPY']['quantity']
        spy_value = spy_qty * prices2['SPY']
        print(f"SPY position: {spy_qty} shares @ ${prices2['SPY']:.2f} = ${spy_value:,.2f}")
    
    if 'SHV' in current_positions2:
        shv_qty = current_positions2['SHV']['quantity']
        shv_value = shv_qty * prices2['SHV']
        print(f"SHV position: {shv_qty} shares @ ${prices2['SHV']:.2f} = ${shv_value:,.2f}")
    
    dollar_changes2 = compare_positions(current_positions2, target2, portfolio_value2, prices2)
    print(f"Dollar changes: {dollar_changes2}")
    
    # Check if dollar changes are correct
    for symbol, change in dollar_changes2.items():
        if symbol in current_positions2:
            current_value = current_positions2[symbol]['quantity'] * prices2[symbol]
            target_value = target2.get(symbol, 0) * portfolio_value2
            expected_change = target_value - current_value
            print(f"{symbol}: Current=${current_value:,.2f}, Target=${target_value:,.2f}, Expected change=${expected_change:,.2f}, Actual change=${change:,.2f}")
            
            if abs(change - expected_change) > 0.01:
                print(f"  *** ERROR: Change mismatch for {symbol}! ***")
    
    orders2 = generate_orders(dollar_changes2, prices2, date2, portfolio.cash, current_positions2)
    print(f"Generated orders: {[str(o) for o in orders2]}")
    
    # Check if sell quantities match expected
    for order in orders2:
        if order.quantity < 0:  # Sell order
            symbol = order.symbol
            sell_qty = abs(order.quantity)
            if symbol in current_positions2:
                current_qty = current_positions2[symbol]['quantity']
                print(f"Sell order for {symbol}: {sell_qty} shares (current position: {current_qty} shares)")
                if sell_qty > current_qty:
                    print(f"  *** ERROR: Trying to sell more than we own! ***")

if __name__ == "__main__":
    debug_first_few_days()
