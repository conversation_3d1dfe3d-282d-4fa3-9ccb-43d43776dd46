# RETRY LOGIC RETAINED – allowed only for data-fetch layer (approved 2025-07-22)
"""
v4/data/data_loader.py

Wrapper for CPS v4 data loader to provide load_price_data interface for tests.
"""
from v4.engine.data_loader_v4 import load_data_for_backtest


def load_price_data(tickers=None, start_date=None, end_date=None, data_source=None):
    """
    Load price data using the CPS v4 engine loader.
    Parameters in this wrapper are currently ignored in favor of settings parameters.

    Returns:
        pandas.DataFrame: price data for backtesting.
    """
    data = load_data_for_backtest()
    # Return only price_data component
    return data.get('price_data')
