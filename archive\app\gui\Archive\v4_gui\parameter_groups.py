"""
Parameter Groups for V4 GUI

This module contains classes for organizing and managing parameter groups in the V4 GUI.
"""

from typing import Dict, Any, Optional
from PySide6.QtWidgets import (
    QGroupBox, QVBoxLayout, QHBoxLayout, QLabel, 
    QLineEdit, QCheckBox, QComboBox, QWidget
)
from PySide6.QtCore import Qt

class BaseParameterGroup(QGroupBox):
    """Base class for parameter groups."""
    
    def __init__(self, settings: Dict[str, Any], parent: Optional[QWidget] = None):
        """Initialize the parameter group.
        
        Args:
            settings: Dictionary containing parameter values
            parent: Parent widget
        """
        super().__init__(parent)
        self.settings = settings
        self.params = {}
        self._init_ui()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        self.setLayout(layout)
    
    def add_parameter(self, name: str, param_type: str, **kwargs) -> None:
        """Add a parameter to the group.
        
        Args:
            name: Parameter name
            param_type: Parameter type ('int', 'float', 'bool', 'str', 'choice')
            **kwargs: Additional parameter attributes
        """
        # Create a widget for the parameter
        param_widget = QWidget()
        param_layout = QHBoxLayout(param_widget)
        param_layout.setContentsMargins(0, 0, 0, 0)
        
        # Add label
        label = QLabel(f"{name.replace('_', ' ').title()}:")
        param_layout.addWidget(label, 1)
        
        # Add appropriate input widget based on type
        if param_type in ('int', 'float'):
            # For numeric parameters, add min/max/step fields
            value_widget = QLineEdit(str(kwargs.get('default', '')))
            param_layout.addWidget(value_widget, 1)
            
            min_widget = QLineEdit(str(kwargs.get('min', '')))
            min_widget.setPlaceholderText("Min")
            param_layout.addWidget(min_widget, 1)
            
            max_widget = QLineEdit(str(kwargs.get('max', '')))
            max_widget.setPlaceholderText("Max")
            param_layout.addWidget(max_widget, 1)
            
            step_widget = QLineEdit(str(kwargs.get('step', '1' if param_type == 'int' else '0.1')))
            step_widget.setPlaceholderText("Step")
            param_layout.addWidget(step_widget, 1)
            
            # Add optimization checkbox
            optimize = QCheckBox("Optimize")
            optimize.setChecked(False)
            param_layout.addWidget(optimize)
            
            self.params[name] = {
                'type': param_type,
                'widgets': {
                    'value': value_widget,
                    'min': min_widget,
                    'max': max_widget,
                    'step': step_widget,
                    'optimize': optimize
                }
            }
            
        elif param_type == 'bool':
            # For boolean parameters, add a checkbox
            checkbox = QCheckBox()
            checkbox.setChecked(kwargs.get('default', False))
            param_layout.addWidget(checkbox, 4)
            param_layout.addStretch(1)  # Add stretch to align with other parameters
            
            self.params[name] = {
                'type': 'bool',
                'widget': checkbox
            }
            
        elif param_type == 'choice':
            # For choice parameters, add a dropdown
            combo = QComboBox()
            choices = kwargs.get('choices', [])
            combo.addItems(choices)
            
            default = kwargs.get('default', choices[0] if choices else '')
            if default in choices:
                combo.setCurrentText(default)
                
            param_layout.addWidget(combo, 4)
            param_layout.addStretch(1)  # Add stretch to align with other parameters
            
            self.params[name] = {
                'type': 'choice',
                'widget': combo,
                'choices': choices
            }
        
        # Add the parameter widget to the layout
        self.layout().addWidget(param_widget)
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get all parameter values from the group.
        
        Returns:
            Dictionary containing parameter names and values
        """
        result = {}
        
        for name, param in self.params.items():
            param_type = param['type']
            
            if param_type in ('int', 'float'):
                # Get value from numeric input
                value_str = param['widgets']['value'].text()
                try:
                    value = int(value_str) if param_type == 'int' else float(value_str)
                    result[name] = value
                    
                    # Add optimization settings if needed
                    if param['widgets']['optimize'].isChecked():
                        min_val = float(param['widgets']['min'].text())
                        max_val = float(param['widgets']['max'].text())
                        step = float(param['widgets']['step'].text())
                        
                        # Convert to int if needed
                        if param_type == 'int':
                            min_val = int(min_val)
                            max_val = int(max_val)
                            step = int(step)
                        
                        result[f'{name}_min'] = min_val
                        result[f'{name}_max'] = max_val
                        result[f'{name}_step'] = step
                        
                except ValueError:
                    # Skip invalid values
                    continue
                    
            elif param_type == 'bool':
                result[name] = param['widget'].isChecked()
                
            elif param_type == 'choice':
                result[name] = param['widget'].currentText()
        
        return result


class CoreParameterGroup(BaseParameterGroup):
    """Core backtest parameters group."""
    
    def _init_ui(self) -> None:
        """Initialize the core parameters UI."""
        self.setTitle("Core Backtest Parameters")
        super()._init_ui()
        
        # Add core parameters
        self.add_parameter(
            'initial_capital',
            'float',
            default=self.settings.get('backtest', {}).get('initial_capital', 100000.0),
            min=10000.0,
            max=1000000.0,
            step=10000.0
        )
        
        self.add_parameter(
            'commission_rate',
            'float',
            default=self.settings.get('strategy', {}).get('commission_rate', 0.001),
            min=0.0,
            max=0.01,
            step=0.0005
        )
        
        self.add_parameter(
            'slippage_rate',
            'float',
            default=self.settings.get('strategy', {}).get('slippage_rate', 0.0005),
            min=0.0,
            max=0.01,
            step=0.0005
        )
        
        self.add_parameter(
            'rebalance_frequency',
            'choice',
            default=self.settings.get('backtest', {}).get('rebalance_freq', 'monthly'),
            choices=['daily', 'weekly', 'monthly', 'quarterly', 'yearly']
        )


class StrategyParameterGroup(BaseParameterGroup):
    """Strategy parameters group."""
    
    def _init_ui(self) -> None:
        """Initialize the strategy parameters UI."""
        self.setTitle("Strategy Parameters")
        super()._init_ui()
        
        # Add EMA strategy parameters
        self.add_parameter(
            'short_lookback',
            'int',
            default=self.settings.get('strategy', {}).get('short_lookback', 20),
            min=5,
            max=100,
            step=1
        )
        
        self.add_parameter(
            'medium_lookback',
            'int',
            default=self.settings.get('strategy', {}).get('medium_lookback', 50),
            min=10,
            max=200,
            step=5
        )
        
        self.add_parameter(
            'long_lookback',
            'int',
            default=self.settings.get('strategy', {}).get('long_lookback', 200),
            min=50,
            max=500,
            step=10
        )
        
        self.add_parameter(
            'top_n',
            'int',
            default=self.settings.get('strategy', {}).get('top_n', 2),
            min=1,
            max=10,
            step=1
        )
