===== V4 DECOUPLED PIPELINE =====
2025-07-22 12:02:33,661 - INFO - [MILESTONE] Starting V4 Decoupled Pipeline
2025-07-22 12:02:33,662 - INFO - [MILESTONE] Starting Signal Generation Phase
2025-07-22 12:02:34,284 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\AI_Library\my_quant_env\Lib\site-packages\win32
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[MILESTONE] 12:02:34 - --- Starting Signal Generation Phase ---
[MILESTONE] 12:02:34 - 
Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[INFO] 12:02:34 - Settings loaded successfully.
[MILESTONE] 12:02:34 - 
Step 2: Loading market data...
2025-07-22 12:02:34,313 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 12:02:35,088 - INFO - Fetching data for SPY (attempt 1)
2025-07-22 12:02:35,741 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:02:35,742 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 1/4): Empty data returned for ticker SPY
2025-07-22 12:02:35,742 - INFO - Retrying SPY data fetch in 2.47 seconds (attempt 2/4)
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\run_v4_decoupled_pipeline.py", line 86, in <module>
    success = run_decoupled_pipeline()
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\run_v4_decoupled_pipeline.py", line 51, in run_decoupled_pipeline
    signals_file = signal_module.run_signal_phase()
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\Algo_signal_phase.py", line 61, in run_signal_phase
    all_data = load_data_for_backtest(current_settings=settings)
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\data_loader_v4.py", line 182, in load_data_for_backtest
    raw_df = load_ticker_data(local_tickers, start_date_str, end_date_str, local_price_field, local_mode)
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\data_loader_v4.py", line 115, in load_ticker_data
    df = get_adjusted_close_data(tickers_list, start_date_obj, end_date_obj, price_field_str)
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\engine\data_loader_v4.py", line 62, in get_adjusted_close_data
    data = data_fetch_stock_data(ticker=ticker, period="max", interval="1d")
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library\data\market_data.py", line 258, in data_fetch_stock_data
    time.sleep(jittered_delay)
    ~~~~~~~~~~^^^^^^^^^^^^^^^^
KeyboardInterrupt
^CV4 decoupled pipeline completed with exit code 
