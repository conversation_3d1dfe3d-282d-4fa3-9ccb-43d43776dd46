# Project-Specific Warp Drive Setup for Backtest Financial Asset Allocation

## Create This Document in Warp Drive

**Title:** "BACKTEST PROJECT CONTEXT - CPS V4"

**Content to Copy:**

---

# AI Agent Guidelines for Backtest_FinAsset_Alloc_Template

This document provides guidelines for the AI agent working on this project.

## 1. Project Overview

- **Objective:** The primary goal is to develop a simple, flexible, and optimizable backtesting and trading pipeline (CPS V4).
- **Core Principle:** All logic, especially algorithm selection and parameters, must be driven by configuration files, not hardcoded in the source.

## 2. Key Commands

- **Run Production Pipeline:** `run_main_v4_prod2.bat`
- **Run Signal Generation Only:** `run_main_v4_signalalgo.bat`
- **Run Trading Phase Only:** `run_trading_phase_standalone.bat`
- **Run Tests:** Navigate to tests directory and run `pytest`
- **Run Linter/Formatter:** `ruff check .`

## 3. Coding Style & Conventions

- **Modularity:** Keep Python modules under 450 lines.
- **Parameter Access:** All parameters must be accessed directly from the `settings_CPS_v4.py` module. Do not use adapters or fallbacks.
- **Naming:** Follow standard Python PEP 8 naming conventions.
- **Comments:** Add comments to explain the *why* of complex logic, not the *what*.

## 4. Testing Strategy

- **Philosophy:** All testing and validation must use the full production code flow. Do not create synthetic test harnesses.
- **Test Location:** Unit tests for `v4/engine/` should be located in `tests/v4/engine/`.
- **Focus:** Tests should verify the data flow and accounting steps between modules, and validate the final CSV outputs.

## 5. Architectural Principles

- **Decoupling:** The Signal Generation and Trading phases are decoupled and should be run as separate, sequential processes.
- **Data Handoff:** The handoff between the signal and trading phases is done via `.parquet` files located in `v4_trace_outputs/`.
- **Immutability:** Treat data flowing between major components (like signals) as immutable once generated.

## 6. Specific "Do's and Don'ts"

- **DO:** Use the `read_file` tool to examine a file before attempting a `replace` operation.
- **DO:** Confirm the location of core engine files before writing tests.
- **DON'T:** Modify files in the `archive/` directory.
- **DON'T:** Introduce new third-party libraries without explicit permission.

## 7. Current Project Status (Updated 2025-06-28)

### ✅ COMPLETED (Phase 1 - 100%)

- Core engine refactoring and validation
- Signal generator factory implementation
- Decoupled signal and trading phases
- All V4 modules converted and validated
- T+0 execution implementation
- Comprehensive tracing utilities

### 🔄 IN PROGRESS (Phase 2 - 30%)

- **CRITICAL ISSUE:** Reporting system broken (v3)
- Performance reporting implementation
- Allocation reporting implementation
- Benchmark calculation fixes

### 📋 NEXT PRIORITIES

1. **[HIGH]** Fix allocation report improvements (generate_rebalance_report warnings)
2. **[HIGH]** Setup backtest graph outputs (cumulative returns, drawdowns, asset weights)
3. **[HIGH]** Integrate detailed EMA debug outputs into performance reports
4. **[MEDIUM]** Create rebalance period signal/asset allocation list report (XLSX)

## 8. Key File Locations

### Core Engine (v4/)

- `v4/engine/backtest_v4.py` - Main backtest engine
- `v4/models/ema_allocation_model_v4.py` - Signal generation
- `v4/engine/portfolio_v4.py` - Portfolio management
- `v4/engine/execution_v4.py` - Trade execution

### Configuration

- `CPS_v4/settings_CPS_v4.py` - Main settings module
- `config/` - Configuration files

### Documentation

- `docs/CPS_v4/` - V4 architecture documentation
- `memory-bank/` - Project memory and context
- `AIAgent_Guidelines.md` - This file (local copy)

### Output

- `v4_trace_outputs/` - All V4 execution outputs
- `output/` - Legacy output location

## 9. Memory Bank Integration

**Key Memory Bank Files to Reference:**

- `memory-bank/core_project_tasks_priority.md` - Current task priorities
- `memory-bank/progress.md` - What works and what's broken
- `memory-bank/productContext.md` - Product context and goals
- `memory-bank/v4_module_functions_list_AI.md` - V4 module reference

**How Signals Work:**

- Review `docs/CPS_v4/How_signals_work.md` for detailed signal generation process
- EMA-based momentum ranking with equal-weight allocation to top N assets
- Two-phase execution: Signal Generation → Trading Phase

## 10. Daily Workflow

### Session Start:

1. Check current task priorities in memory-bank
2. Review any recent status updates
3. Verify which phase of development we're in

### Session End:

1. Update progress.md with completed work
2. Update task priorities if needed
3. Document any new issues or learnings

---

**CRITICAL:** Always check the Global AI Development Rules in Warp Drive before starting any coding tasks.
