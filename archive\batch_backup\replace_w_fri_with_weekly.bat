@echo off
echo Starting search and replace operation...

:: Set the root directory
set ROOT_DIR=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template

:: Create a temporary directory for backups
if not exist "%ROOT_DIR%\temp_backups" mkdir "%ROOT_DIR%\temp_backups"

:: Find all Python files containing W-FRI and replace with weekly
for /r "%ROOT_DIR%" %%f in (*.py) do (
    findstr /m /c:"W-FRI" "%%f" >nul
    if not errorlevel 1 (
        echo Processing: %%f
        
        :: Create a backup
        copy "%%f" "%ROOT_DIR%\temp_backups\%%~nxf.bak"
        
        :: Replace W-FRI with weekly, preserving case
        powershell -Command "(Get-Content '%%f') -replace \"'W-FRI'\", \"'weekly'\" | Set-Content '%%f'"
        
        :: Also replace double-quoted version
        powershell -Command "(Get-Content '%%f') -replace '\"W-FRI\"', '\"weekly\"' | Set-Content '%%f'"
    )
)

echo.
echo Replacement complete!
echo Backups of modified files are stored in %ROOT_DIR%\temp_backups
echo.
pause
