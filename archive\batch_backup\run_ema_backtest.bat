@echo off
REM run_ema_backtest.bat
REM Activate the virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate

REM Change directory to the backtest directory
cd /d S:\Dropbox\"Scott Only Internal"\Quant_Python_24\Backtest_FinAsset_Alloc_Template

REM Set Python paths
set PYTHONPATH=%cd%;S:\Dropbox\"Scott Only Internal"\Quant_Python_24\"Custom Function Library";%PYTHONPATH%

REM Check Python version
python -c "import sys; print('Python version:', sys.version)"

REM Run the backtest script with specified arguments
python run_backtest.py --strategy ema --rebalance weekly --start-date 2020-01-01 --end-date 2025-04-04 --tickers SPY,SHV,EFA,TLT,PFF

pause
