"""
models/ema_allocation_model_v4.py
EMA-based allocation model for financial backtesting (CPS v4 compliant).
This strategy uses moving averages with different lookback periods to create allocation signals.

All detailed model logging is set to DEBUG level. These messages will only appear when BACKTEST_LOG_LEVEL is set to DEBUG or lower.

CPS_v4 Parameters Required (from settings['strategy']['ema_model']):
- st_lookback (int): Short-term EMA lookback period.
- mt_lookback (int): Medium-term EMA lookback period.
- lt_lookback (int): Long-term EMA lookback period.
- min_weight (float): Minimum weight per asset.
- max_weight (float): Maximum weight per asset.

If any of these parameters are missing, the module will raise a ValueError.
"""
# PROTECTED CODE: EMA allocation logic. Do not modify without explicit permission.

import numpy as np
import pandas as pd
import logging
import sys
from pathlib import Path
from v4.config.paths_v4 import *
from v4.config.allocation_rules_v4 import get_allocation_weights
from v4.settings.settings_CPS_v4 import load_settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Load Settings ---
try:
    settings = load_settings()
    if not settings:
        raise RuntimeError("load_settings() returned empty or None.")
    logger.info("Successfully loaded settings for ema_allocation_model_v4.")
except Exception as e:
    logger.critical(f"FATAL: Could not load settings in ema_allocation_model_v4. Error: {e}")
    # Exit or re-raise, as the module is not usable without settings
    raise RuntimeError("Failed to load settings.") from e

# Add Custom Function Library path
if CUSTOM_LIB_PATH not in sys.path:
    sys.path.append(CUSTOM_LIB_PATH)

# Import necessary functions from Custom Function Library
from technical_indicators.indicators import tech_exponential_moving_average

# Extract EMA strategy parameters respecting single-source-of-truth rules
settings = load_settings()
if not settings:
    # This case should ideally be prevented by load_settings() itself or caught at a higher level.
    # However, adding a safeguard here for robustness if ema_allocation_model_v4 is ever used standalone.
    critical_msg = "FATAL: load_settings() returned empty or None at the top of ema_allocation_model_v4.py."
    logger.critical(critical_msg)
    raise RuntimeError(critical_msg)

ema_config = settings.get('ema_model', {})
strategy_config = settings.get('strategy', {})
system_config = settings.get('system', {})


def _extract_value_from_complexn_dict(param_value_from_settings):
    """If param_value_from_settings is a ComplexN dict, return its 'default_value'.
    Otherwise, return param_value_from_settings as is.
    Raises ValueError if it's a dict but not a valid ComplexN structure (missing 'default_value').
    """
    if isinstance(param_value_from_settings, dict):
        if 'default_value' not in param_value_from_settings:
            # This implies a malformed ComplexN dictionary if parsing didn't catch it,
            # or a non-ComplexN dictionary was unexpectedly passed.
            err_msg = (
                f"Input dictionary '{param_value_from_settings}' for ComplexN extraction "
                f"is missing the required 'default_value' key."
            )
            logger.critical(err_msg)
            raise ValueError(err_msg)
        return param_value_from_settings['default_value']
    return param_value_from_settings


_PARAM_NOT_FOUND = object() # Sentinel for missing parameters

def _get_param(key):
    """Fetch parameter from [ema_model] first, fallback to [strategy], then [system].
    Raises KeyError if not found in any of the sections.
    """
    value = ema_config.get(key, _PARAM_NOT_FOUND)
    if value is _PARAM_NOT_FOUND:
        value = strategy_config.get(key, _PARAM_NOT_FOUND)
    if value is _PARAM_NOT_FOUND:
        value = system_config.get(key, _PARAM_NOT_FOUND)
    
    if value is _PARAM_NOT_FOUND:
        error_msg = f"Parameter '{key}' not found in '[ema_model]', '[strategy]', or '[system]' sections of the settings file."
        logger.critical(error_msg)
        raise KeyError(error_msg)
    return value # Return the raw value (could be simple type, None, or ComplexN dict)

# --- Define module-level parameters from settings with hard fail ---
def _initialize_module_parameter(param_name, expected_type):
    """Helper to initialize a module-level parameter with hard fail and type casting."""
    raw_value = _get_param(param_name)
    actual_value = _extract_value_from_complexn_dict(raw_value)
    
    if actual_value is None:
        error_msg = f"Module parameter '{param_name}' resolved to None from settings. This is not allowed."
        logger.critical(error_msg)
        raise ValueError(error_msg)
    try:
        casted_value = expected_type(actual_value)
        return casted_value
    except (ValueError, TypeError) as e:
        error_msg = f"Module parameter '{param_name}' with value '{actual_value}' could not be converted to {expected_type.__name__}. Error: {e}"
        logger.critical(error_msg)
        raise type(e)(error_msg) from e

st_lookback = _initialize_module_parameter('st_lookback', int)
mt_lookback = _initialize_module_parameter('mt_lookback', int)
lt_lookback = _initialize_module_parameter('lt_lookback', int)
min_weight = _initialize_module_parameter('min_weight', float)
max_weight = _initialize_module_parameter('max_weight', float)

def calculate_ema_metrics(price_data):
    """
    Calculate Short, Medium, and Long-term EMA metrics for price data.
    
    Args:
        price_data (DataFrame or tuple): Historical price data with close prices or precomputed EMAs

    Uses module-level st_lookback, mt_lookback, lt_lookback from CPS_v4 settings.
        
    Returns:
        tuple: Three DataFrames containing Short_EMA, Med_EMA, and Long_EMA for each asset
    """
    try:
        # Create empty dataframes for results
        short_ema = pd.DataFrame(index=price_data.index, columns=price_data.columns)
        med_ema = pd.DataFrame(index=price_data.index, columns=price_data.columns)
        long_ema = pd.DataFrame(index=price_data.index, columns=price_data.columns)

        # Calculate EMAs for each asset using the library function
        for column in price_data.columns:
            short_ema[column] = tech_exponential_moving_average(price_data[column], window=st_lookback)  # Module-level param
            med_ema[column] = tech_exponential_moving_average(price_data[column], window=mt_lookback)    # Module-level param
            long_ema[column] = tech_exponential_moving_average(price_data[column], window=lt_lookback)   # Module-level param

        return short_ema, med_ema, long_ema
    
    except Exception as e:
        logger.error(f"Error calculating EMA metrics: {e}")
        # Return empty dataframes in case of error
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

def calculate_ema_ratios(short_ema, med_ema, long_ema):
    """
    Calculate EMA ratios for signal generation.
    
    Calculates:
    - STMTEMAX = Short_EMA/Med_EMA
    - MTLTEMAX = Med_EMA/Long_EMA
    - EMAXAvg = (STMTEMAX + MTLTEMAX)/2
    
    Args:
        short_ema (DataFrame): Short-term EMA values for each asset
        med_ema (DataFrame): Medium-term EMA values for each asset
        long_ema (DataFrame): Long-term EMA values for each asset
        
    Returns:
        tuple: Three DataFrames containing STMTEMAX, MTLTEMAX, and EMAXAvg for each asset
    """
    try:
        # Create empty dataframes for results with same index and columns as inputs
        stmtemax_df = pd.DataFrame(index=short_ema.index, columns=short_ema.columns)
        mtltemax_df = pd.DataFrame(index=short_ema.index, columns=short_ema.columns)
        emaxavg_df = pd.DataFrame(index=short_ema.index, columns=short_ema.columns)
        
        # Calculate the ratios for the entire time series
        stmtemax_df = short_ema / med_ema
        mtltemax_df = med_ema / long_ema
        
        # Calculate the average for the entire time series
        emaxavg_df = (stmtemax_df + mtltemax_df) / 2
        
        # For the final day's values (for backward compatibility with existing code)
        stmtemax = stmtemax_df.iloc[-1]
        mtltemax = mtltemax_df.iloc[-1]
        emaxavg = emaxavg_df.iloc[-1]
        
        return stmtemax, mtltemax, emaxavg, stmtemax_df, mtltemax_df, emaxavg_df
    
    except Exception as e:
        logger.error(f"Error calculating EMA ratios: {e}")
        # Return empty series and dataframes in case of error
        empty_series = pd.Series()
        empty_df = pd.DataFrame()
        return empty_series, empty_series, empty_series, empty_df, empty_df, empty_df

# PROTECTED CODE: Do not modify this function without explicit permission.


def ema_allocation_model(price_data, returns_data=None, st_lookback=st_lookback, mt_lookback=mt_lookback, lt_lookback=lt_lookback, min_weight=min_weight, max_weight=max_weight, system_top_n=None, trace_mode=False, **params):
    """
    EMA-based allocation model.
    Uses Short, Medium, and Long-term EMAs to determine asset allocation.
    
    Ranking Rule:
    - Rank all assets by EMAXAvg (Rank 1 to X, where X is # of assets)
    
    Signal Allocation Rule:
    - Own top Y assets out of X assets (rank 1 or 2), and 0 allocation to all others
    - Rank 1 = 60% allocation
    - Rank 2 = 40% allocation
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame, optional): Historical returns data
        st_lookback (int): Short-term lookback period (default: 10)
        mt_lookback (int): Medium-term lookback period (default: 50)
        lt_lookback (int): Long-term lookback period (default: 150)
        min_weight (float): Minimum weight per asset
        max_weight (float): Maximum weight per asset
        **params: Additional parameters
        
    Note:
        The algorithm name from settings should be an unquoted string (e.g. 'ema')
        matching exactly with keys in ALGO_ALLOCATION_RULES
        
    Returns:
        dict: Asset weights dictionary
    """
    logger.debug(f"EMA Model Inputs (Date: {price_data.index[-1]}) -- ST: {st_lookback}, MT: {mt_lookback}, LT: {lt_lookback}, min_w: {min_weight}, max_w: {max_weight}, system_top_n_arg: {system_top_n}")
    logger.debug(f"Price data shape: {price_data.shape}, last date: {price_data.index[-1]}, first 3 columns: {list(price_data.columns[:3])}")
    
    # Use precomputed EMAs if available, else compute
    if 'precomputed_emas' in params:
        short_ema, med_ema, long_ema = params.pop('precomputed_emas')
    else:
        short_ema, med_ema, long_ema = calculate_ema_metrics(price_data)
    logger.debug(f"Short EMA shape: {short_ema.shape}, last date: {short_ema.index[-1]}, first 3 columns: {list(short_ema.columns[:3])}")
    logger.debug(f"Medium EMA shape: {med_ema.shape}, last date: {med_ema.index[-1]}, first 3 columns: {list(med_ema.columns[:3])}")
    logger.debug(f"Long EMA shape: {long_ema.shape}, last date: {long_ema.index[-1]}, first 3 columns: {list(long_ema.columns[:3])}")
    
    # Calculate EMA ratios - now returns both Series and DataFrames
    stmtemax, mtltemax, emaxavg, stmtemax_df, mtltemax_df, emaxavg_df = calculate_ema_ratios(short_ema, med_ema, long_ema)
    logger.debug(f"""Calculated EMAXAVG (Series for current date {price_data.index[-1]}):
{emaxavg}""")
    
    # Store the full history in params for debugging/analysis if needed
    params['ema_history'] = {
        'short_ema': short_ema,
        'med_ema': med_ema,
        'long_ema': long_ema,
        'stmtemax_df': stmtemax_df,
        'mtltemax_df': mtltemax_df,
        'emaxavg_df': emaxavg_df
    }
    
    # Rank assets by EMAXAvg (descending order)
    ranked_assets = emaxavg.sort_values(ascending=False)
    logger.debug(f"""Ranked assets by EMAXAVG (for current date {price_data.index[-1]}):
{ranked_assets}""")
    
    # Initialize weights dictionary
    weights = {asset: 0.0 for asset in price_data.columns}
    
    # Determine number of top assets to allocate (hard fail on missing setting)
    if system_top_n is None:
        raise ValueError("Required parameter 'system_top_n' was not provided or is None")
    top_n = int(system_top_n)
    
    # Get algorithm name from settings, extracting the default value from the dict
    signal_algo_setting = settings.get('strategy', {}).get('signal_algo', {})
    if isinstance(signal_algo_setting, dict):
        algo_name = signal_algo_setting.get('default', 'ema')
    else:
        # Fallback for old string-based setting format
        algo_name = signal_algo_setting or 'ema'
    logger.debug(f"Resolved algo_name: {algo_name}, Effective top_n: {top_n} (Date: {price_data.index[-1]})")
    
    # Get allocation rules for the specified algorithm
    rule_weights = get_allocation_weights(top_n, algorithm=algo_name)
    selected_assets = ranked_assets.index[:top_n]
    logger.debug(f"Selected top {top_n} assets (Date: {price_data.index[-1]}): {list(selected_assets)}")
    logger.debug(f"Allocation rule weights for {algo_name} (top_n={top_n}, Date: {price_data.index[-1]}): {rule_weights}")

    # Allocate weights only if rules match the number of selected assets
    if rule_weights and len(rule_weights) == len(selected_assets):
        for asset, w in zip(selected_assets, rule_weights):
            weights[asset] = w
        logger.debug(f"Weights assigned (before normalization, Date: {price_data.index[-1]}): {weights}")
    else:
        # Hard fail if rules don't match selected assets
        raise ValueError(
            f"Allocation failed: Rule mismatch for algorithm '{algo_name}'. "
            f"Retrieved {len(rule_weights)} weights for top_n={top_n}, but {len(selected_assets)} assets were selected. "
            f"Check allocation rules and asset data availability."
        )

    # Normalize weights and ensure allocation was successful
    total_weight = sum(weights.values())
    
    if total_weight <= 0:
        # This should not be reached if the logic above is correct, but serves as a final guard
        raise ValueError(
            "Allocation failed: Resulting weights are zero or negative. "
            f"The allocation rules for '{algo_name}' were not applied correctly."
        )

    # Normalize the valid weights to sum to 1.0
    for asset in weights:
        weights[asset] /= total_weight
    logger.debug(f"Final normalized weights (Date: {price_data.index[-1]}): {weights}")
    
    logger.debug("Applied ema allocation weights")

    if trace_mode:
        # 1. Ratios (EMAXAvg for the current date)
        # emaxavg is a Series: index=asset, values=EMAXAvg for the current date
        ratios_output = emaxavg.copy()
        ratios_output.name = "EMAXAvg_Ratio"

        # 2. Ranks (Ranked assets with EMAXAvg values and ordinal rank for the current date)
        # ranked_assets is a Series: index=Asset, values=EMAXAvg_Value, sorted descending
        ranks_df = ranked_assets.reset_index()
        ranks_df.columns = ['Asset', 'EMAXAvg_Value']
        ranks_df['Rank_Ordinal'] = range(1, len(ranks_df) + 1)
        # ranks_df = ranks_df.set_index('Asset') # Keep as default index for simpler CSV write if needed by caller

        # 3. Signal (Final allocation weights as a Series for the current date)
        signal_output = pd.Series(weights)
        signal_output.name = "AllocationSignal"
        
        logger.debug(f"Trace mode enabled. Returning weights, current ratios/ranks/signals, and full EMA/ratio history DataFrames.")
        # short_ema, med_ema, long_ema are full DataFrames from calculate_ema_metrics or params
        # stmtemax_df, mtltemax_df, emaxavg_df are full DataFrames from calculate_ema_ratios
        return (weights, ratios_output, ranks_df, signal_output, 
                short_ema, med_ema, long_ema, 
                stmtemax_df, mtltemax_df, emaxavg_df)
    else:
        return weights

def ema_allocation_model_updated(price_data, returns_data=None, trace_mode=False, **params):
    """
    EMA-based allocation model that returns a dictionary with pd.Timestamp keys.
    This ensures compatibility with the signal_history format expected by the test framework.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame, optional): Historical returns data (not used in this model)
        st_lookback (int): Short-term EMA lookback period -> Now from CPS_v4
        mt_lookback (int): Medium-term EMA lookback period -> Now from CPS_v4
        lt_lookback (int): Long-term EMA lookback period -> Now from CPS_v4
        **params: Additional parameters

    Uses module-level st_lookback, mt_lookback, lt_lookback from CPS_v4 settings.
        
    Returns:
        dict: Asset allocations with timestamp keys {pd.Timestamp: {symbol: weight}}
    """
    logger.debug(f"Running updated EMA allocation model with lookbacks: {st_lookback}, {mt_lookback}, {lt_lookback}")
    
    # Get the current date from price_data
    if isinstance(price_data.index, pd.DatetimeIndex):
        current_date = price_data.index[-1]
    else:
        current_date = pd.Timestamp.now().normalize()
    
    # Get weights using the original model
    # Retrieve system_top_n from module-level settings for passing to ema_allocation_model
    # Retrieve system_top_n using the helper function that checks nested settings
    # Retrieve system_top_n with hard fail and type casting
    raw_system_top_n = _get_param('system_top_n')
    system_top_n_val = _extract_value_from_complexn_dict(raw_system_top_n)
    
    if system_top_n_val is None:
        error_msg = "Parameter 'system_top_n' resolved to None from settings. This is not allowed."
        logger.critical(error_msg)
        raise ValueError(error_msg)
    try:
        current_system_top_n = int(system_top_n_val)
    except (ValueError, TypeError) as e:
        error_msg = f"Parameter 'system_top_n' with value '{system_top_n_val}' could not be converted to int. Error: {e}"
        logger.critical(error_msg)
        raise type(e)(error_msg) from e

    logger.debug(f"Passing system_top_n={current_system_top_n} to ema_allocation_model from ema_allocation_model_updated.")

    # Call ema_allocation_model with trace_mode
    params_for_model = params.copy()
    params_for_model.pop('system_top_n', None)
    model_output = ema_allocation_model(
        price_data=price_data,
        returns_data=returns_data,
        system_top_n=current_system_top_n,
        trace_mode=trace_mode,  # Pass trace_mode
        **params_for_model
    )
    
    if trace_mode:
        (weights, ratios_output, ranks_df, signal_output, 
         short_ema_df, med_ema_df, long_ema_df, 
         stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df) = model_output
        
        # Create historical ranks DataFrame
        historical_ranks_list = []
        
        # For each date in the historical EMAXAvg data
        for date in emaxavg_hist_df.index:
            # Get EMAXAvg values for this date
            date_emax = emaxavg_hist_df.loc[date]
            
            # Create a DataFrame with assets and their EMAXAvg values
            date_assets = pd.DataFrame({
                'Asset': date_emax.index,
                'EMAXAvg_Value': date_emax.values
            })
            
            # Sort by EMAXAvg in descending order and add rank
            date_assets = date_assets.sort_values('EMAXAvg_Value', ascending=False)
            date_assets['Rank_Ordinal'] = range(1, len(date_assets) + 1)
            date_assets['Date'] = date
            
            # Add to our list
            historical_ranks_list.append(date_assets)
        
        # Combine all dates into a single DataFrame
        if historical_ranks_list:
            historical_ranks_df = pd.concat(historical_ranks_list, ignore_index=True)
            # Reorder columns for consistency
            historical_ranks_df = historical_ranks_df[['Date', 'Asset', 'EMAXAvg_Value', 'Rank_Ordinal']]
            
            # Update the ranks_df to include all historical ranks
            ranks_df = historical_ranks_df
            
            logger.debug(f"Generated historical ranks for {len(historical_ranks_df['Date'].unique())} dates")
        else:
            logger.warning("No historical ranks were generated")
        
        logger.debug(f"Trace mode enabled in updated_model. Returning dict_weights, current ratios/ranks/signals, and historical EMA/ratio DataFrames.")
        return ({current_date: weights}, ratios_output, ranks_df, signal_output, 
                short_ema_df, med_ema_df, long_ema_df,
                stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df)
    else:
        weights = model_output # Original behavior, model_output is just weights
        # Return a dictionary with the current date as a Timestamp key
        return {current_date: weights}
