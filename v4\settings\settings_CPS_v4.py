#!/usr/bin/env python
# -*- coding: utf-8 -*-
# settings_CPS_v4.py
"""
Central Parameter System v4 - Settings Module

This module provides a simple, direct way to load and access configuration settings
from INI files. It handles type conversion, default values, and parameter overrides.

Usage:
    from CPS_v4.settings_CPS_v4 import load_settings
    
    # Load default settings
    settings = load_settings()
    
    # Load with custom settings file
    settings = load_settings(custom_file="path/to/custom_settings.ini")
    
    # Access settings
    create_excel = settings.get("report_create_excel", True)

Author: AI Assistant
Date: 2025-06-06
"""

import os
import sys
import ast
import configparser
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Tuple, Set

# Define paths relative to this module
MODULE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))
SETTINGS_FILE = MODULE_DIR / 'settings_parameters_v4.ini'

# =============================================================================
# OPERATIONAL MODE AND FLAGS
# =============================================================================
# These settings control the overall system behavior and can be overridden
# by configuration files or command-line parameters

# Run mode - controls which parts of the pipeline execute
# "UNIFIED" - Run both signal generation and trading in sequence (default)
# "SIGNAL_ONLY" - Only run signal generation phase
# "TRADING_ONLY" - Only run trading phase (assumes signals already exist)
RUN_MODE = "UNIFIED"

# Logging configuration
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_TO_FILE = True  # Whether to write logs to file in addition to console
LOG_TO_CONSOLE = True  # Whether to display logs on console

# Output retention settings
# Note: Uses CSV format only (Parquet eliminated for performance)
RETAIN_INTERMEDIATE_FILES = True  # Keep intermediate CSV files
RETAIN_DEBUG_OUTPUT = False  # Keep detailed debug output files
MAX_LOG_FILES = 10  # Maximum number of log files to retain (0 = unlimited)
MAX_OUTPUT_DAYS = 30  # Maximum age of output files to retain (0 = unlimited)


# Performance and resource settings
MAX_MEMORY_USAGE_GB = 8  # Maximum memory usage before triggering cleanup
ENABLE_PARALLEL_PROCESSING = True  # Use multiprocessing where available
MAX_WORKER_THREADS = 4  # Maximum number of worker threads (0 = auto-detect)

# Parameter types as defined in Parameter_Reference_CPS_v4.md
PARAM_TYPES = {
    'SimpleA': 'simple_alphanumeric',  # Simple alphanumeric value
    'SimpleN': 'simple_numeric',       # Simple numeric value 
    'ComplexN': 'complex_numeric',     # Complex numeric with optimization attributes
    'AlphaList': 'alpha_list'          # List of alphanumeric values
}

def load_settings(custom_file: Optional[str] = None) -> Dict[str, Any]:
    """
    Load settings from the settings_parameters_v4.ini file with proper type conversion.
    This is the single source of truth for all parameters.
    
    Args:
        custom_file: Optional path to override the default settings file
        
    Returns:
        Dictionary containing all settings with proper types including lists and parameter metadata
    """

    config = configparser.ConfigParser(interpolation=None)
    # Preserve the original case of option names so that pick-list references
    # like ``ETFpicklist`` match exactly with keys defined in the [Lists]
    # section.  ConfigParser lower-cases keys by default which broke our
    # AlphaList resolution.
    config.optionxform = str  # type: ignore[attr-defined]
    settings = {}
    
    # Determine which settings file to load
    settings_file = Path(custom_file) if custom_file else SETTINGS_FILE
    
    # Load the settings file
    if settings_file.exists():
        config.read(settings_file)
        settings = _parse_config(config)
    else:
        print(f"Error: Settings file not found: {settings_file}")
        print("The settings_parameters_v4.ini file is required for CPS_v4 to function.")
        return {}
    
    # Display info about the loaded settings
    print(f"Loaded settings from: {settings_file}")
    print(f"Found {sum(1 for section in settings if section != 'lists')} parameter sections")
    if 'lists' in settings:
        print(f"Found {len(settings['lists'])} named lists for parameter references")
    
    return settings

def _parse_config(config: configparser.ConfigParser) -> Dict[str, Any]:
    """
    Parse a ConfigParser object into a dictionary with nested structure
    and proper type conversion. Handles the four parameter types and named lists.
    
    The four parameter types are:
    - SimpleA: Simple alphanumeric value (string, boolean)
    - SimpleN: Simple numeric value (int, float)
    - ComplexN: Optimizable numeric value with min/max/step
    - AlphaList: List of values with a picklist reference
    
    Args:
        config: ConfigParser object with loaded settings
        
    Returns:
        Dictionary with proper structure and converted values
    """
    settings = {}
    
    # First, load the lists section if it exists to resolve references
    named_lists = {}
    if 'Lists' in config:
        settings['lists'] = {}
        for key, value in config['Lists'].items():
            converted_value = _convert_value(value)
            settings['lists'][key] = converted_value
            named_lists[key] = converted_value
    
    # Now process all other sections    
    for section in config.sections():
        if section == 'Lists':
            continue  # Already processed
        
        section_key = section.lower()
        if section_key not in settings:
            settings[section_key] = {}
        
        for key, value in config[section].items():
            # Check for parameter type based on value structure
            param_key = key.lower()
            param_value = value.strip()
            # Strip inline comments from parameter values
            if ';' in param_value:
                param_value = param_value.split(';',1)[0].strip()
                # Strip inline comments from parameter values
                if ';' in param_value:
                    param_value = param_value.split(';',1)[0].strip()
            
            # Process based on parameter format
            if param_value.startswith('(') and param_value.endswith(')'):
                # This is either a ComplexN or AlphaList parameter
                
                if 'optimize=' in param_value.lower():
                    # ComplexN parameter
                    settings[section_key][param_key] = _parse_complex_numeric(param_value)
                else:
                    # AlphaList parameter or tuple reference
                    settings[section_key][param_key] = _parse_alpha_list(param_value, named_lists)
            else:
                # This is either SimpleA or SimpleN parameter
                # Resolve bare named list references
                if param_value in named_lists:
                    settings[section_key][param_key] = named_lists[param_value]
                    continue
                converted = _convert_value(param_value)
                
                # Determine correct type
                if isinstance(converted, (int, float)):
                    # SimpleN parameter
                    settings[section_key][param_key] = converted
                else:
                    # SimpleA parameter
                    settings[section_key][param_key] = converted
    
    return settings

def _parse_complex_numeric(value_str: str) -> Dict[str, Any]:
    """
    Parse a ComplexN parameter value.
    
    Format: (optimize=True/False, default_value=N, min_value=N, max_value=N, increment=N)
    
    Args:
        value_str: The string representation of the ComplexN parameter
        
    Returns:
        Dictionary with ComplexN attributes
    """
    # Remove the outer parentheses
    value_str = value_str.strip()[1:-1]
    
    # Split by comma and process each key=value pair
    result = {}
    for pair in value_str.split(','):
        if '=' in pair:
            key, val = pair.split('=', 1)
            key = key.strip().lower()
            val = val.strip()
            
            # Convert the value based on the key
            if key == 'optimize':
                result[key] = val.lower() == 'true'
            elif key in ('default_value', 'min_value', 'max_value', 'increment'):
                try:
                    # Try to convert to float first, then to int if it's a whole number
                    float_val = float(val)
                    result[key] = int(float_val) if float_val.is_integer() else float_val
                except ValueError:
                    result[key] = 0  # Default to 0 if conversion fails
    
    # Ensure all required keys exist
    for required_key in ('optimize', 'default_value', 'min_value', 'max_value', 'increment'):
        if required_key not in result:
            if required_key == 'optimize':
                result[required_key] = False
            else:
                result[required_key] = 0
    
    return result

def _parse_alpha_list(value_str: str, named_lists: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse an AlphaList parameter value that references named lists.
    
    Format: (default_list, picklist_variable)
    
    Args:
        value_str: The string representation of the AlphaList parameter
        named_lists: Dictionary of named lists from the [Lists] section
        
    Returns:
        Dictionary with 'default' and 'picklist' keys
    """
    # Remove the outer parentheses
    value_str = value_str.strip()[1:-1]
    
    parts = [p.strip() for p in value_str.split(',')]
    result = {}
    
    if len(parts) >= 1:
        default_name = parts[0]
        # Check if default_name exists in named_lists, otherwise treat as literal
        if default_name in named_lists:
            result['default'] = named_lists[default_name]
        else:
            # Try to convert as a literal
            try:
                result['default'] = _convert_value(default_name)
            except:
                result['default'] = default_name
    
    if len(parts) >= 2:
        picklist_name = parts[1]
        if picklist_name in named_lists:
            result['picklist'] = named_lists[picklist_name]
        else:
            # Handle comma-separated list in picklist name
            picklist_names = [n.strip() for n in picklist_name.split(',')]
            pickist_values = []
            
            for name in picklist_names:
                if name in named_lists:
                    pickist_values.append(named_lists[name])
                    
            result['picklist'] = pickist_values if pickist_values else picklist_name
    
    return result

def _convert_value(value: str) -> Any:
    """
    Convert a string value to the appropriate Python type.
    
    Args:
        value: String value from the INI file
        
    Returns:
        Converted value with the appropriate type (boolean, int, float, tuple, list, or string)
    """
    if not value or not isinstance(value, str):  # Handle None or non-string values
        return value
        
    value = value.strip()
    # Strip inline comments starting with ';'
    if ';' in value:
        value = value.split(';', 1)[0].strip()
    
    # Handle empty values
    if not value:
        return None
    
    # Handle boolean values
    if value.lower() in ('true', 'yes', 'on'):
        return True
    if value.lower() in ('false', 'no', 'off'):
        return False
    
    # Try to convert to numeric types
    try:
        # Try to convert to int
        int_val = int(value)
        return int_val
    except ValueError:
        try:
            # Try to convert to float
            float_val = float(value)
            return float_val
        except ValueError:
            # Not numeric, continue with other conversions
            pass
    
    # Handle tuples, lists, and dictionaries (using a more robust approach)
    if (value.startswith("(") and value.endswith(")")) or \
       (value.startswith("[") and value.endswith("]")) or \
       (value.startswith("{") and value.endswith("}")):
        try:
            # First, try direct literal evaluation
            return ast.literal_eval(value)
        except (SyntaxError, ValueError):
            # If that fails, try to handle special cases like string tuples with quotes
            try:
                # For cases like ('SPY', 'SHV', 'EFA')
                if value.startswith("(") and value.endswith(")"):
                    inner = value[1:-1].strip()
                    if inner:
                        items = []
                        # Parse each item, respecting quotes
                        for item in inner.split(","):
                            item = item.strip()
                            # Remove quotes if present
                            if (item.startswith("'") and item.endswith("'")) or \
                               (item.startswith('"') and item.endswith('"')):
                                items.append(item[1:-1])
                            else:
                                # Try conversion again
                                try:
                                    items.append(_convert_value(item))
                                except:
                                    items.append(item)
                        return tuple(items)
                return value  # Return as string if parsing fails
            except:
                return value  # Return as string if all parsing fails
    
    # Handle comma-separated values not in a container
    if ',' in value:
        parts = [part.strip() for part in value.split(',')]
        # Don't automatically convert to list - this could be a simple string with commas
        return parts
    
    # Default: return as string
    return value

# Import utility functions from settings_utils to maintain line limit
try:
    from .settings_utils import (
        create_settings_template, create_default_settings, 
        get_parameter_type, print_parameters_summary, _deep_update
    )
except ImportError:
    # Fallback if import fails
    def create_settings_template(path: str = None) -> None:
        pass
    def create_default_settings(settings_file_path: Path = None) -> None:
        pass
    def get_parameter_type(param_value: Any) -> str:
        return 'SimpleA'
    def print_parameters_summary(settings: Dict[str, Any]) -> None:
        pass
    def _deep_update(target: Dict, source: Dict) -> None:
        target.update(source)

# Main test block
if __name__ == '__main__':
    # Check if settings file exists, create template if it doesn't
    if not SETTINGS_FILE.exists():
        create_settings_template()
    
    # Load settings
    settings = load_settings()
    
    # Print basic summary
    print("\nLoaded Settings Sections:")
    sections = [section for section in settings.keys() if section != 'lists']
    print(f"Found {len(sections)} parameter sections")
    
    if 'lists' in settings:
        print(f"Found {len(settings['lists'])} named lists")
