# Full V3 Process Flow Diagram

```mermaid
flowchart TD
    %% Start of Flow
    Start[Start] --> GUI[v3_gui_core.py\nMainWindowV3]
    
    %% GUI Layer
    GUI --> |Initialize GUI| ParamWidgets[v3_parameter_widgets.py\nParameter Widgets]
    GUI --> |Handle Actions| GUIActions[v3_gui_actions.py\nGUI Actions]
    ParamWidgets --> |Manage Controls| GuiParamMgr[v3_engine/gui_parameter_manager.py\nGuiParameterManager]
    
    %% Parameter System
    GuiParamMgr --> |Sync Parameters| ParamReg[v3_engine/parameter_registry.py\nParameterRegistry]
    ParamReg --> |Register/Lookup| StratParamSet[v3_engine/strategy_parameter_set.py\nStrategyParameterSet]
    ParamReg --> |Define Types| Params[v3_engine/parameters.py\nNumeric/Categorical Parameters]
    
    %% Optimization
    Params --> |Optimize| ParamOpt[v3_engine/parameter_optimizer.py\nParameterOptimizer]
    StratParamSet --> |Apply to Strategy| Strategy[Strategy Classes\n e.g., EMA]
    ParamOpt --> |Generate Combinations| Strategy
    
    %% Data Handling
    Strategy --> |Load Data| DataLoader[data/data_loader.py\nDataLoader]
    
    %% Backtest Execution
    Strategy --> |Execute Backtest| Backtest[Backtest Engine]
    
    %% Reporting Layer
    Backtest --> |Generate Reports| PerfReport[reporting/performance_reporting.py\nPerformance Reporting]
    Backtest --> |Generate Reports| AllocReport[reporting/allocation_report.py\nAllocation Report]
    Backtest --> |Generate Visuals| PerfCharts[visualization/performance_charts.py\nPerformance Charts]
    PerfReport --> |Integrate Data| PerfReportAdapter[v3_engine/performance_reporter_adapter.py\nPerformance Reporter Adapter]
    
    %% Utilities
    Backtest --> |Use Utilities| DateUtils[utils/date_utils.py\nDate Utilities]
    Backtest --> |Log Trades| TradeLog[utils/trade_log.py\nTrade Log]
    
    %% End of Flow
    PerfReport --> End[End]
    AllocReport --> End
    PerfCharts --> End
    
    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef data fill:#F5F5F5,stroke:#616161,color:#212121;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef util fill:#EFEBE9,stroke:#5D4037,color:#3E2723;
    
    class GUI,ParamWidgets,GUIActions,GuiParamMgr gui;
    class ParamReg,Params,ParamOpt,StratParamSet param;
    class Strategy,Backtest strategy;
    class DataLoader data;
    class PerfReport,AllocReport,PerfCharts,PerfReportAdapter report;
    class DateUtils,TradeLog util;
```
