## Integration Goals and Checklist

### Objective
- Develop a simple, flexible, and optimizable backtesting and trading pipeline (CPS V4).
- All logic, especially algorithm selection and parameters, must be driven by configuration files.

### Rule Compliance
- **Module Size:** Keep Python modules under 450 lines.
- **Config-Driven:** Parameters accessed directly from `settings_CPS_v4.py`.
- **Naming & Comments:** Follow PEP 8 naming conventions and add comments for complex logic.

### Backward-Compatibility
- Ensure no existing functionality is removed without approval.

### Testing Philosophy
- Use the full production code flow for all testing.
- Place unit tests for `v4` in `tests/v4`.
- Decouple Signal Generation and Trading phases.

## Action Items
1. Ratify and confirm integration goals.
2. Ensure rule compliance for module size and config-driven coding.
3. Validate backward-compatibility measures.
4. Review testing strategy and philosophy.

