@echo off
REM ============================================================================
REM  Equivalence Test Runner for CPS v4 Pipeline Comparison
REM  Tests equivalence between decoupled and unified pipeline execution
REM ============================================================================
REM
REM  This script:
REM  1. Runs decoupled pipeline (signal generation + trading phase)
REM  2. Runs unified pipeline (combined signal + trading)
REM  3. Compares output files for numerical equivalence
REM  4. Generates detailed equivalence report with logs
REM
REM ============================================================================

SETLOCAL ENABLEDELAYEDEXPANSION

REM --- Setup environment using master config ---
CALL "%~dp0..\..\master_config.bat"
IF ERRORLEVEL 1 (
    echo ERROR: Failed to set up environment
    exit /b 1
)

REM --- Paths ---
SET "SCRIPT_DIR=%~dp0"
SET "PROJECT_ROOT=%~dp0..\.."
SET "OUTPUT_DIR=%PROJECT_ROOT%\v4_trace_outputs"
SET "TEST_OUTPUT_DIR=%SCRIPT_DIR%equivalence_test_outputs"

REM --- Ensure output directories exist ---
IF NOT EXIST "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)
IF NOT EXIST "%TEST_OUTPUT_DIR%" (
    mkdir "%TEST_OUTPUT_DIR%"
)

REM --- Timestamp (YYYYMMDD_HHMMSS) ---
SET "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
SET "TIMESTAMP=%TIMESTAMP: =0%"

REM --- Log files ---
SET "EQUIVALENCE_LOG=%TEST_OUTPUT_DIR%\equivalence_test_%TIMESTAMP%.log"
SET "DECOUPLED_LOG=%TEST_OUTPUT_DIR%\decoupled_%TIMESTAMP%.log"
SET "UNIFIED_LOG=%TEST_OUTPUT_DIR%\unified_%TIMESTAMP%.log"
SET "COMPARISON_LOG=%TEST_OUTPUT_DIR%\comparison_%TIMESTAMP%.log"

echo ============================================================================
echo  EQUIVALENCE TEST - CPS V4 PIPELINE COMPARISON
echo  Test ID: %TIMESTAMP%
echo ============================================================================
echo.
echo Logs will be saved to:
echo - Main log: %EQUIVALENCE_LOG%
echo - Decoupled log: %DECOUPLED_LOG% 
echo - Unified log: %UNIFIED_LOG%
echo - Comparison log: %COMPARISON_LOG%
echo.

REM --- Initialize main log ---
(
    echo ===== EQUIVALENCE TEST - CPS V4 PIPELINE COMPARISON =====
    echo Test Start Time: %DATE% %TIME%
    echo Test ID: %TIMESTAMP%
    echo.
    echo Environment:
    echo - VENV_PATH: %VENV_PATH%
    echo - PROJECT_ROOT: %PROJECT_ROOT%
    echo - OUTPUT_DIR: %OUTPUT_DIR%
    echo - TEST_OUTPUT_DIR: %TEST_OUTPUT_DIR%
    echo.
) > "%EQUIVALENCE_LOG%"

echo [%TIME%] Starting equivalence test... >> "%EQUIVALENCE_LOG%"

REM --- Clear existing output files to ensure clean test ---
echo [%TIME%] Clearing existing output files for clean test...
echo [%TIME%] Clearing existing output files for clean test... >> "%EQUIVALENCE_LOG%"
DEL /Q "%OUTPUT_DIR%\signals_output.parquet" 2>nul
DEL /Q "%OUTPUT_DIR%\allocation_history_*.csv" 2>nul
DEL /Q "%OUTPUT_DIR%\trade_log_*.csv" 2>nul

REM ============================================================================
REM  PHASE 1: Run Decoupled Pipeline
REM ============================================================================
echo.
echo ============================================================================
echo  PHASE 1: Running Decoupled Pipeline
echo ============================================================================
echo [%TIME%] PHASE 1: Starting decoupled pipeline execution >> "%EQUIVALENCE_LOG%"

REM Change to project root for execution
pushd "%PROJECT_ROOT%"

echo [%TIME%] Executing: run_main_v4_signalalgo.bat
CALL "%PROJECT_ROOT%\run_main_v4_signalalgo.bat" > "%DECOUPLED_LOG%" 2>&1
SET "DECOUPLED_EXIT=%ERRORLEVEL%"

popd

echo [%TIME%] Decoupled pipeline completed with exit code: %DECOUPLED_EXIT% >> "%EQUIVALENCE_LOG%"

IF %DECOUPLED_EXIT% NEQ 0 (
    echo ERROR: Decoupled pipeline failed with exit code %DECOUPLED_EXIT%
    echo [%TIME%] ERROR: Decoupled pipeline failed - aborting test >> "%EQUIVALENCE_LOG%"
    echo Full decoupled log saved to: %DECOUPLED_LOG%
    exit /b %DECOUPLED_EXIT%
)

REM --- Backup decoupled outputs with unique naming ---
echo [%TIME%] Backing up decoupled pipeline outputs... >> "%EQUIVALENCE_LOG%"

SET "DECOUPLED_SIGNALS=%TEST_OUTPUT_DIR%\decoupled_signals_%TIMESTAMP%.parquet"
SET "DECOUPLED_ALLOCATION=%TEST_OUTPUT_DIR%\decoupled_allocation_%TIMESTAMP%.csv"
SET "DECOUPLED_TRADES=%TEST_OUTPUT_DIR%\decoupled_trades_%TIMESTAMP%.csv"

REM Find and copy the most recent output files
FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\signals_output.parquet" /B 2^>nul') DO (
    COPY "%OUTPUT_DIR%\%%F" "%DECOUPLED_SIGNALS%" >> "%EQUIVALENCE_LOG%"
)

FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\allocation_history_*.csv" /B /O:-D 2^>nul') DO (
    COPY "%OUTPUT_DIR%\%%F" "%DECOUPLED_ALLOCATION%" >> "%EQUIVALENCE_LOG%"
    GOTO :found_allocation
)
:found_allocation

FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\trade_log_*.csv" /B /O:-D 2^>nul') DO (
    COPY "%OUTPUT_DIR%\%%F" "%DECOUPLED_TRADES%" >> "%EQUIVALENCE_LOG%"
    GOTO :found_trades
)
:found_trades

echo [%TIME%] Decoupled outputs backed up successfully >> "%EQUIVALENCE_LOG%"

REM ============================================================================
REM  PHASE 2: Run Unified Pipeline  
REM ============================================================================
echo.
echo ============================================================================
echo  PHASE 2: Running Unified Pipeline
echo ============================================================================
echo [%TIME%] PHASE 2: Starting unified pipeline execution >> "%EQUIVALENCE_LOG%"

REM Clear output directory again for clean unified run
DEL /Q "%OUTPUT_DIR%\signals_output.parquet" 2>nul
DEL /Q "%OUTPUT_DIR%\allocation_history_*.csv" 2>nul
DEL /Q "%OUTPUT_DIR%\trade_log_*.csv" 2>nul
DEL /Q "%OUTPUT_DIR%\unified_*.csv" 2>nul

REM Change to project root for execution
pushd "%PROJECT_ROOT%"

echo [%TIME%] Executing: run_main_v4_unified.bat
CALL "%PROJECT_ROOT%\run_main_v4_unified.bat" > "%UNIFIED_LOG%" 2>&1
SET "UNIFIED_EXIT=%ERRORLEVEL%"

popd

echo [%TIME%] Unified pipeline completed with exit code: %UNIFIED_EXIT% >> "%EQUIVALENCE_LOG%"

IF %UNIFIED_EXIT% NEQ 0 (
    echo ERROR: Unified pipeline failed with exit code %UNIFIED_EXIT%
    echo [%TIME%] ERROR: Unified pipeline failed - aborting test >> "%EQUIVALENCE_LOG%"
    echo Full unified log saved to: %UNIFIED_LOG%
    exit /b %UNIFIED_EXIT%
)

REM --- Backup unified outputs ---
echo [%TIME%] Backing up unified pipeline outputs... >> "%EQUIVALENCE_LOG%"

SET "UNIFIED_SIGNALS=%TEST_OUTPUT_DIR%\unified_signals_%TIMESTAMP%.parquet"
SET "UNIFIED_ALLOCATION=%TEST_OUTPUT_DIR%\unified_allocation_%TIMESTAMP%.csv"  
SET "UNIFIED_TRADES=%TEST_OUTPUT_DIR%\unified_trades_%TIMESTAMP%.csv"

REM Find and copy unified output files (may have different naming convention)
FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\signals_output.parquet" /B 2^>nul') DO (
    COPY "%OUTPUT_DIR%\%%F" "%UNIFIED_SIGNALS%" >> "%EQUIVALENCE_LOG%"
)

REM Look for unified allocation files first, then fall back to standard naming
SET "FOUND_UNIFIED_ALLOCATION="
FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\unified_allocation_*.csv" /B /O:-D 2^>nul') DO (
    COPY "%OUTPUT_DIR%\%%F" "%UNIFIED_ALLOCATION%" >> "%EQUIVALENCE_LOG%"
    SET "FOUND_UNIFIED_ALLOCATION=1"
    GOTO :found_unified_allocation
)
IF NOT DEFINED FOUND_UNIFIED_ALLOCATION (
    FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\allocation_history_*.csv" /B /O:-D 2^>nul') DO (
        COPY "%OUTPUT_DIR%\%%F" "%UNIFIED_ALLOCATION%" >> "%EQUIVALENCE_LOG%"
        GOTO :found_unified_allocation
    )
)
:found_unified_allocation

SET "FOUND_UNIFIED_TRADES="
FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\unified_trade_*.csv" /B /O:-D 2^>nul') DO (
    COPY "%OUTPUT_DIR%\%%F" "%UNIFIED_TRADES%" >> "%EQUIVALENCE_LOG%"
    SET "FOUND_UNIFIED_TRADES=1"
    GOTO :found_unified_trades
)
IF NOT DEFINED FOUND_UNIFIED_TRADES (
    FOR /F "tokens=*" %%F IN ('DIR "%OUTPUT_DIR%\trade_log_*.csv" /B /O:-D 2^>nul') DO (
        COPY "%OUTPUT_DIR%\%%F" "%UNIFIED_TRADES%" >> "%EQUIVALENCE_LOG%"
        GOTO :found_unified_trades
    )
)
:found_unified_trades

echo [%TIME%] Unified outputs backed up successfully >> "%EQUIVALENCE_LOG%"

REM ============================================================================
REM  PHASE 3: Run Equivalence Comparison
REM ============================================================================
echo.
echo ============================================================================
echo  PHASE 3: Running Equivalence Comparison
echo ============================================================================
echo [%TIME%] PHASE 3: Starting equivalence comparison >> "%EQUIVALENCE_LOG%"

REM Run Python comparison script
python "%SCRIPT_DIR%test_equivalence_cli.py" ^
    --decoupled-signals "%DECOUPLED_SIGNALS%" ^
    --decoupled-allocation "%DECOUPLED_ALLOCATION%" ^
    --decoupled-trades "%DECOUPLED_TRADES%" ^
    --unified-signals "%UNIFIED_SIGNALS%" ^
    --unified-allocation "%UNIFIED_ALLOCATION%" ^
    --unified-trades "%UNIFIED_TRADES%" ^
    --output-dir "%TEST_OUTPUT_DIR%" ^
    --timestamp "%TIMESTAMP%" > "%COMPARISON_LOG%" 2>&1

SET "COMPARISON_EXIT=%ERRORLEVEL%"

echo [%TIME%] Equivalence comparison completed with exit code: %COMPARISON_EXIT% >> "%EQUIVALENCE_LOG%"

REM ============================================================================
REM  PHASE 4: Generate Summary Report
REM ============================================================================
echo.
echo ============================================================================
echo  PHASE 4: Test Summary
echo ============================================================================

(
    echo.
    echo ===== EQUIVALENCE TEST SUMMARY =====
    echo Test End Time: %DATE% %TIME%
    echo.
    echo RESULTS:
    echo - Decoupled Pipeline: %DECOUPLED_EXIT% (0=success)
    echo - Unified Pipeline: %UNIFIED_EXIT% (0=success)  
    echo - Equivalence Check: %COMPARISON_EXIT% (0=equivalent)
    echo.
    echo OUTPUT FILES:
    echo - Decoupled Signals: %DECOUPLED_SIGNALS%
    echo - Decoupled Allocation: %DECOUPLED_ALLOCATION%
    echo - Decoupled Trades: %DECOUPLED_TRADES%
    echo - Unified Signals: %UNIFIED_SIGNALS%
    echo - Unified Allocation: %UNIFIED_ALLOCATION%
    echo - Unified Trades: %UNIFIED_TRADES%
    echo.
    echo LOGS:
    echo - Main Log: %EQUIVALENCE_LOG%
    echo - Decoupled Log: %DECOUPLED_LOG%
    echo - Unified Log: %UNIFIED_LOG%
    echo - Comparison Log: %COMPARISON_LOG%
    echo.
) >> "%EQUIVALENCE_LOG%"

REM --- Display results ---
IF %COMPARISON_EXIT% EQU 0 (
    echo ✓ SUCCESS: Pipelines are equivalent!
    echo [%TIME%] SUCCESS: Equivalence test passed >> "%EQUIVALENCE_LOG%"
) ELSE (
    echo ✗ FAILURE: Pipelines are not equivalent!
    echo [%TIME%] FAILURE: Equivalence test failed >> "%EQUIVALENCE_LOG%"
    echo.
    echo Check the comparison log for detailed differences: %COMPARISON_LOG%
)

echo.
echo Full test report: %EQUIVALENCE_LOG%
echo.

REM --- Final exit code ---
IF %DECOUPLED_EXIT% NEQ 0 (
    SET "FINAL_EXIT=%DECOUPLED_EXIT%"
) ELSE IF %UNIFIED_EXIT% NEQ 0 (
    SET "FINAL_EXIT=%UNIFIED_EXIT%"
) ELSE (
    SET "FINAL_EXIT=%COMPARISON_EXIT%"
)

echo [%TIME%] Test completed with final exit code: %FINAL_EXIT% >> "%EQUIVALENCE_LOG%"

ENDLOCAL
exit /b %FINAL_EXIT%
