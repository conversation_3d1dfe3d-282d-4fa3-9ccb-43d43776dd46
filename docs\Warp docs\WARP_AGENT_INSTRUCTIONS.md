# Warp Agent Instruction Sets - Quick Reference

## How to Set Up in Warp Drive

### Step 1: Create Warp Drive Notebook

1. Open Warp → Warp Drive → Create Notebook
2. Title: "AI Agent Instructions"
3. Add these entries as separate notebook items:

### Step 2: Quick Access Setup

- Pin the notebook to favorites
- Use War<PERSON>'s quick search to find entries
- Copy/paste directly into AI chat

## Instruction Set 1: SESSION START

```
START SESSION

Please read these files in order to understand current context:
1. memory-bank/current_session_context.md (current priorities & status)
2. memory-bank/execution_reference.md (exact commands & locations)  
3. memory-bank/codebase_map.md (existing functions to avoid duplication)

Then provide:
- Summary of current phase and progress %
- Top 3 priority tasks for this session
- Any critical blockers from previous session
- Recommended first action

Be concise and action-oriented.
```

## Instruction Set 2: MID UPDATE

```
MID UPDATE

Update memory-bank/current_session_context.md with current progress:
- Mark completed tasks with ✅
- Add any new issues to BLOCKERS section
- Add discoveries to LEARNED THIS SESSION
- Update NEXT SESSION GOALS if priorities changed

Be specific about what was accomplished and what was learned.
```

## Instruction Set 3: END SESSION

```
END SESSION

Perform these documentation actions:
1. Create memory-bank/session_updates/[today's date]_completed.md with accomplishments
2. Update memory-bank/current_session_context.md - remove completed, add new priorities
3. Update memory-bank/codebase_map.md if new functions/modules were discovered
4. Set 3 clear, specific goals for next session

Focus on concrete outcomes and next steps.
```

## Instruction Set 4: QUICK STATUS

```
QUICK STATUS

Read memory-bank/current_session_context.md and provide:
- Current phase and % complete
- Top priority task right now
- Any critical blockers
- Recommended immediate next action (be specific)

Keep response under 100 words.
```

## Instruction Set 5: FUNCTION CHECK

```
FUNCTION CHECK

Before creating any new function, check memory-bank/codebase_map.md for existing implementations.

Report:
- Does this function already exist?
- What similar functionality is available?
- Where should new function be placed if needed?
- Any naming conflicts to avoid?

Prevent code duplication.
```

## Instruction Set 6: DOCUMENTATION UPDATE

```
DOCUMENTATION UPDATE

I discovered new functionality. Please update:
1. memory-bank/codebase_map.md - add new functions/modules/connections
2. memory-bank/execution_reference.md - add new commands/paths if applicable
3. Add discovery to current_session_context.md LEARNED section

Keep documentation current and prevent knowledge loss.
```

## Usage in Warp

### Method 1: Notebook Entries

1. Save each instruction set as a Warp Drive Notebook entry
2. Quick search and copy/paste when needed

### Method 2: Prompts

1. Create Warp Prompts with these instruction sets
2. Use `/prompt [name]` to quickly insert

### Method 3: Text Snippets

1. Save as text snippets in Warp
2. Type trigger phrase to expand

## Benefits

- ✅ Consistent AI interactions across sessions
- ✅ No need to retype complex instructions
- ✅ Standardized documentation workflow
- ✅ Quick context switching
- ✅ Prevents forgetting important steps
