===== V4 DECOUPLED PIPELINE =====
2025-07-22 12:06:15,552 - INFO - [MILESTONE] Starting V4 Decoupled Pipeline
2025-07-22 12:06:15,552 - INFO - [MILESTONE] Starting Signal Generation Phase
2025-07-22 12:06:16,214 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\AI_Library\my_quant_env\Lib\site-packages\win32
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[MILESTONE] 12:06:16 - --- Starting Signal Generation Phase ---
[MILESTONE] 12:06:16 - 
Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[INFO] 12:06:16 - Settings loaded successfully.
[MILESTONE] 12:06:16 - 
Step 2: Loading market data...
2025-07-22 12:06:16,244 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 12:06:17,028 - INFO - Fetching data for SPY (attempt 1)
Exception ignored from cffi callback <function buffer_callback at 0x00000227DBC79800>:
Traceback (most recent call last):
  File "F:\AI_Library\my_quant_env\Lib\site-packages\curl_cffi\curl.py", line 67, in buffer_callback
    @ffi.def_extern()
KeyboardInterrupt: 
2025-07-22 12:06:24,848 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:06:24,848 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 1/4): Empty data returned for ticker SPY
2025-07-22 12:06:24,849 - INFO - Retrying SPY data fetch in 2.17 seconds (attempt 2/4)
2025-07-22 12:06:27,018 - INFO - Fetching data for SPY (attempt 2)
Exception ignored from cffi callback <function buffer_callback at 0x00000227DBC79800>:
Traceback (most recent call last):
  File "F:\AI_Library\my_quant_env\Lib\site-packages\curl_cffi\curl.py", line 67, in buffer_callback
    @ffi.def_extern()
KeyboardInterrupt: 
2025-07-22 12:06:31,211 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:06:31,212 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 2/4): Empty data returned for ticker SPY
2025-07-22 12:06:31,212 - INFO - Retrying SPY data fetch in 4.23 seconds (attempt 3/4)
2025-07-22 12:06:35,442 - INFO - Fetching data for SPY (attempt 3)
2025-07-22 12:06:39,317 - INFO - Successfully fetched 8174 rows for SPY
2025-07-22 12:06:39,329 - INFO - Fetching data for QQQ (attempt 1)
2025-07-22 12:06:47,182 - ERROR - $QQQ: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:06:47,183 - WARNING - Non-retryable error fetching QQQ, but will retry anyway (attempt 1/4): Empty data returned for ticker QQQ
2025-07-22 12:06:47,183 - INFO - Retrying QQQ data fetch in 2.86 seconds (attempt 2/4)
2025-07-22 12:06:50,047 - INFO - Fetching data for QQQ (attempt 2)
2025-07-22 12:06:54,435 - INFO - Successfully fetched 6632 rows for QQQ
2025-07-22 12:06:54,445 - INFO - Fetching data for IWM (attempt 1)
Exception ignored from cffi callback <function buffer_callback at 0x00000227DBC79800>:
Traceback (most recent call last):
  File "F:\AI_Library\my_quant_env\Lib\site-packages\curl_cffi\curl.py", line 67, in buffer_callback
    @ffi.def_extern()
KeyboardInterrupt: 
2025-07-22 12:06:56,597 - ERROR - $IWM: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:06:56,598 - WARNING - Non-retryable error fetching IWM, but will retry anyway (attempt 1/4): Empty data returned for ticker IWM
2025-07-22 12:06:56,598 - INFO - Retrying IWM data fetch in 2.37 seconds (attempt 2/4)
2025-07-22 12:06:58,972 - INFO - Fetching data for IWM (attempt 2)
2025-07-22 12:07:00,355 - INFO - Successfully fetched 6325 rows for IWM
2025-07-22 12:07:00,366 - INFO - Fetching data for GLD (attempt 1)
2025-07-22 12:07:04,249 - ERROR - $GLD: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:07:04,250 - WARNING - Non-retryable error fetching GLD, but will retry anyway (attempt 1/4): Empty data returned for ticker GLD
2025-07-22 12:07:04,250 - INFO - Retrying GLD data fetch in 2.97 seconds (attempt 2/4)
2025-07-22 12:07:07,222 - INFO - Fetching data for GLD (attempt 2)
2025-07-22 12:07:08,252 - INFO - Successfully fetched 5200 rows for GLD
2025-07-22 12:07:08,260 - INFO - Fetching data for TLT (attempt 1)
2025-07-22 12:07:08,874 - INFO - Successfully fetched 5782 rows for TLT
2025-07-22 12:07:09,359 - INFO - [TickerData] Saved data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
[INFO] 12:07:09 - Price data loaded for 5 assets.
[MILESTONE] 12:07:09 - 
Step 3: Generating signals...
[INFO] 12:07:09 -    - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'system_top_n': {'optimize': False, 'default_value': 2, 'min_value': 1, 'max_value': 5, 'increment': 1}, 'execution_delay': 1, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
2025-07-22 12:07:09,365 - INFO - Running EMA model with tracing enabled
2025-07-22 12:07:12,833 - INFO - Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
2025-07-22 12:07:12,873 - INFO - Saved Short-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_short_20250722_120712.csv
2025-07-22 12:07:12,902 - INFO - Saved Medium-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_medium_20250722_120712.csv
2025-07-22 12:07:12,935 - INFO - Saved Long-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_long_20250722_120712.csv
2025-07-22 12:07:13,042 - INFO - Saved Asset Ranking History (Matrix Format) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_20250722_120712.csv
2025-07-22 12:07:13,153 - INFO - Saved Combined EMA Averages History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\02_ema_average_history_20250722_120712.csv
2025-07-22 12:07:13,240 - INFO - Saved Raw Algorithm Calculation History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_history_20250722_120712.csv
2025-07-22 12:07:13,240 - INFO - Successfully saved detailed signal history data to CSV files including individual EMA files
2025-07-22 12:07:13,241 - INFO - Initialized signal_history DataFrame with shape (1394, 5)
2025-07-22 12:07:13,241 - INFO - Signal history date range: 2020-01-02 00:00:00 to 2025-07-21 00:00:00
2025-07-22 12:07:13,242 - INFO - Ranks DataFrame shape: (6970, 4)
2025-07-22 12:07:13,242 - INFO - Ranks date range: 0 to 6969
2025-07-22 12:07:13,242 - INFO - Initial ranks_df info:
2025-07-22 12:07:13,242 - INFO - Index type: <class 'pandas.core.indexes.range.RangeIndex'>
2025-07-22 12:07:13,242 - INFO - Columns: ['Date', 'Asset', 'EMAXAvg_Value', 'Rank_Ordinal']
2025-07-22 12:07:13,259 - INFO - Sample data:
        Date Asset  EMAXAvg_Value  Rank_Ordinal
0 2020-01-02   SPY            1.0             1
1 2020-01-02   QQQ            1.0             2
2025-07-22 12:07:13,285 - INFO - Pivoted ranks DataFrame shape: (1394, 5)
2025-07-22 12:07:13,286 - INFO - Pivoted columns: ['GLD', 'IWM', 'QQQ', 'SPY', 'TLT']
2025-07-22 12:07:13,290 - INFO - 2020-01-02 00:00:00: Allocated 60% to SPY, 40% to QQQ
2025-07-22 12:07:13,370 - INFO - 2020-03-16 00:00:00: Allocated 60% to TLT, 40% to GLD
2025-07-22 12:07:13,443 - INFO - 2020-05-27 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:07:13,513 - INFO - 2020-08-06 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:07:13,584 - INFO - 2020-10-16 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:07:13,660 - INFO - 2020-12-29 00:00:00: Allocated 60% to IWM, 40% to QQQ
2025-07-22 12:07:13,727 - INFO - 2021-03-12 00:00:00: Allocated 60% to IWM, 40% to SPY
2025-07-22 12:07:13,803 - INFO - 2021-05-24 00:00:00: Allocated 60% to SPY, 40% to IWM
2025-07-22 12:07:13,869 - INFO - 2021-08-04 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:13,936 - INFO - 2021-10-14 00:00:00: Allocated 60% to SPY, 40% to QQQ
2025-07-22 12:07:14,010 - INFO - 2021-12-27 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:14,079 - INFO - 2022-03-09 00:00:00: Allocated 60% to GLD, 40% to TLT
2025-07-22 12:07:14,159 - INFO - 2022-05-19 00:00:00: Allocated 60% to GLD, 40% to SPY
2025-07-22 12:07:14,244 - INFO - 2022-08-02 00:00:00: Allocated 60% to SPY, 40% to IWM
2025-07-22 12:07:14,322 - INFO - 2022-10-12 00:00:00: Allocated 60% to GLD, 40% to IWM
2025-07-22 12:07:14,390 - INFO - 2022-12-22 00:00:00: Allocated 60% to GLD, 40% to TLT
2025-07-22 12:07:14,461 - INFO - 2023-03-08 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:07:14,530 - INFO - 2023-05-18 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:07:14,607 - INFO - 2023-08-01 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:14,689 - INFO - 2023-10-11 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:14,764 - INFO - 2023-12-21 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:07:14,837 - INFO - 2024-03-06 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:14,923 - INFO - 2024-05-16 00:00:00: Allocated 60% to GLD, 40% to SPY
2025-07-22 12:07:14,995 - INFO - 2024-07-30 00:00:00: Allocated 60% to IWM, 40% to SPY
2025-07-22 12:07:15,065 - INFO - 2024-10-09 00:00:00: Allocated 60% to GLD, 40% to SPY
2025-07-22 12:07:15,135 - INFO - 2024-12-19 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:15,231 - INFO - 2025-03-06 00:00:00: Allocated 60% to GLD, 40% to TLT
2025-07-22 12:07:15,323 - INFO - 2025-05-16 00:00:00: Allocated 60% to GLD, 40% to QQQ
2025-07-22 12:07:15,392 - INFO - 2025-07-21 00:00:00: Allocated 60% to QQQ, 40% to SPY
2025-07-22 12:07:15,392 - INFO - Signal generation complete. Made 1394 allocations.
2025-07-22 12:07:15,392 - INFO - Final signal_history shape: (1394, 5)
2025-07-22 12:07:15,394 - INFO - Non-zero allocations: 2788
INFO: Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
TRACE: Saved Short-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_short_20250722_120712.csv
TRACE: Saved Medium-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_medium_20250722_120712.csv
TRACE: Saved Long-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_long_20250722_120712.csv
TRACE: Saved Asset Ranking History (Matrix Format) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_20250722_120712.csv
TRACE: Saved Combined EMA Averages History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\02_ema_average_history_20250722_120712.csv
TRACE: Saved Raw Algorithm Calculation History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_history_20250722_120712.csv
[INFO] 12:07:15 - Signals saved to timestamped CSV: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250722_120715.csv
[MILESTONE] 12:07:15 - Signals saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250722_120715.csv
[MILESTONE] 12:07:15 - Signal generation complete.
2025-07-22 12:07:15,413 - INFO - Signal phase completed successfully, signals saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250722_120715.csv
2025-07-22 12:07:15,413 - INFO - [MILESTONE] Starting Trading Phase
2025-07-22 12:07:15,474 - INFO - [PHASE_START] 12:07:15 - Starting Trading Phase
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[PHASE_START] 12:07:15 - Starting Trading Phase
2025-07-22 12:07:15,484 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 12:07:15,486 - INFO - Fetching data for SPY (attempt 1)
2025-07-22 12:07:28,381 - INFO - Successfully fetched 8174 rows for SPY
2025-07-22 12:07:28,392 - INFO - Fetching data for QQQ (attempt 1)
