# Trade Frequency Controls and Filters Reference

**Document:** trade_filter.md  
**Date:** 2025-06-29  
**Purpose:** Comprehensive reference for all discovered trade-frequency controls and filters in CPS v4  

## Overview

This document summarizes all discovered filters, controls, and constraints that affect trade frequency and execution in the CPS v4 backtesting system. Each control is documented with its parameter source, current implementation status, and effectiveness.

## Discovered Trade-Frequency Controls

### 1. Min-Cash Checks

**Location:** `v4/engine/execution_v4.py`, `v4/engine/portfolio_v4.py`  
**Type:** Validation Filter  
**Implementation Status:** ✅ Working

**Description:**
- The execution engine validates available cash before executing buy orders
- Cash freed by sell trades is calculated and made available for subsequent buy trades within the same rebalance period
- Portfolio tracks cash position and prevents overspending

**Parameter Source:**
- `backtest.initial_capital` in `settings_parameters_v4.ini`
- Cash tracking is internal to portfolio state management

**Why It's Working:**
- Cash constraints are enforced by design to prevent overspending
- Sell orders execute first to free up cash for buy orders
- Built-in validation prevents impossible trade scenarios

**Deviation Threshold:**
- No explicit deviation threshold for cash
- Hard constraint: cannot spend more cash than available
- Uses cash buffer factor for safety margin

### 2. Holiday Skip / Business Day Filtering

**Location:** `v4/utils/date_utils_v4.py`  
**Type:** Date Filter  
**Implementation Status:** ✅ Working

**Description:**
- Business day functions automatically skip weekends and holidays
- Rebalance dates are filtered to only include valid trading days
- Date range functions respect business day calendars

**Parameter Source:**
- Built into pandas business day functionality (`'B'` frequency)
- Used in `get_business_days()` and `map_rebalance_frequency()` functions

**Why It's Working:**
- Leverages pandas built-in business day calendar
- Automatically excludes non-trading days from rebalance schedules
- Consistent across all date-related operations

**Deviation Threshold:** N/A (binary filter)

### 3. Lot-Size Rounding

**Location:** `v4/engine/allocation_v4.py` (line 204)  
**Type:** Quantity Filter  
**Implementation Status:** ✅ Working

**Description:**
- All share quantities are rounded to whole numbers before order creation
- Prevents fractional share trading
- Applied to both buy and sell orders

**Parameter Source:**
- Hard-coded `round(quantity)` in allocation logic
- No configurable parameter

**Why It's Working:**
- Ensures valid trade order quantities
- Eliminates fractional share complications
- Standard market practice for most brokers

**Deviation Threshold:** Rounds to nearest whole share

### 4. Rebalancing Frequency Control

**Location:** `v4/settings/settings_parameters_v4.ini`  
**Type:** Temporal Filter  
**Implementation Status:** ✅ Working

**Description:**
- Controls how often portfolio rebalancing occurs
- Supports daily, weekly, monthly, quarterly, yearly frequencies
- Directly impacts trade frequency

**Parameter Source:**
```ini
[Backtest]
rebalance_freq = (Rebalance_Monthly, RebalanceFreqs)
benchmark_rebalance_freq = (Rebalance_Yearly, RebalanceFreqs)
```

**Why It's Working:**
- Uses pandas frequency mapping for consistent date generation
- Configurable through settings without code changes
- Well-tested frequency options available

**Deviation Threshold:** Exact frequency matching (no tolerance)

### 5. Execution Delay Control

**Location:** `v4/settings/settings_parameters_v4.ini`  
**Type:** Temporal Filter  
**Implementation Status:** ✅ Working

**Description:**
- Introduces delay between signal generation and order execution
- Simulates real-world implementation lag
- Prevents look-ahead bias

**Parameter Source:**
```ini
[Strategy]
execution_delay = 0
```

**Why It's Working:**
- Configurable delay in days
- Properly implemented in backtest engine
- Realistic simulation of trading constraints

**Deviation Threshold:** Exact day delay (no variance)

### 6. Allocation Weight Constraints

**Location:** `v4/settings/settings_parameters_v4.ini`, `v4/config/allocation_rules_v4.py`  
**Type:** Weight Filter  
**Implementation Status:** ✅ Working

**Description:**
- Minimum and maximum allocation weights per asset
- Algorithm-specific allocation rules
- Prevents extreme position concentrations

**Parameter Source:**
```ini
[Core]
max_allocation = (optimize=False, default_value=0.25, min_value=0.1, max_value=0.5, increment=0.05)
min_allocation = (optimize=False, default_value=0.0, min_value=0.0, max_value=0.1, increment=0.01)

[Strategy]
min_weight = 0.0
max_weight = 1.0
```

**Why It's Working:**
- Enforced during allocation calculation
- Prevents unrealistic position sizes
- Configurable through settings

**Deviation Threshold:** 
- Allocation validation uses 0.5% tolerance for sum-to-100% checks
- Hard constraints on min/max weights

### 7. Cash Buffer Factor

**Location:** `v4/engine/allocation_v4.py`  
**Type:** Safety Filter  
**Implementation Status:** ✅ Working

**Description:**
- Applies safety buffer to available cash for buy orders
- Prevents using 100% of available cash
- Accounts for commission and slippage uncertainty

**Parameter Source:**
- Hard-coded `cash_buffer_factor` in allocation logic
- Currently not configurable through settings

**Why It's Working:**
- Prevents cash overdraft situations
- Accounts for transaction costs
- Provides safety margin for market volatility

**Deviation Threshold:** Buffer factor (currently hard-coded)

### 8. Commission and Slippage Controls

**Location:** `v4/engine/execution_v4.py`  
**Type:** Cost Filter  
**Implementation Status:** ✅ Working

**Description:**
- Applies commission and slippage to all trades
- Affects order sizing and execution costs
- Realistic transaction cost modeling

**Parameter Source:**
```ini
[Backtest]
commission_rate = 0.001
slippage_rate = 0.0005
```

**Why It's Working:**
- Integrated into execution engine
- Affects trade profitability calculations
- Configurable through settings

**Deviation Threshold:** Fixed percentage rates

## Missing or Weak Controls

### 1. Configurable Cash Buffer
**Issue:** Cash buffer factor is hard-coded, should be parameterized  
**Recommendation:** Add `cash_buffer_factor` to settings

### 2. Position Size Limits
**Issue:** No absolute position size limits (only percentage-based)  
**Recommendation:** Add dollar-based position limits

### 3. Turnover Limits
**Issue:** No explicit turnover constraints  
**Recommendation:** Add maximum turnover thresholds per period

### 4. Trade Frequency Deviation Threshold
**Issue:** No dynamic deviation threshold for trade frequency adjustments  
**Recommendation:** Add configurable threshold for when to skip rebalancing due to small changes

## Implementation Quality Assessment

| Control Type | Implementation | Configurability | Effectiveness |
|-------------|----------------|-----------------|---------------|
| Min-Cash Checks | ✅ Excellent | ✅ Good | ✅ High |
| Holiday Skip | ✅ Excellent | ✅ Good | ✅ High |
| Lot-Size Rounding | ✅ Good | ❌ None | ✅ High |
| Rebalance Frequency | ✅ Excellent | ✅ Excellent | ✅ High |
| Execution Delay | ✅ Excellent | ✅ Excellent | ✅ High |
| Weight Constraints | ✅ Good | ✅ Good | ✅ Medium |
| Cash Buffer | ✅ Good | ❌ None | ✅ Medium |
| Transaction Costs | ✅ Excellent | ✅ Excellent | ✅ High |

## Recommendations for Enhancement

1. **Parameterize Hard-Coded Values:** Move cash buffer factor and other hard-coded thresholds to settings
2. **Add Dynamic Thresholds:** Implement deviation-based rebalancing triggers
3. **Enhanced Position Controls:** Add absolute position size limits alongside percentage limits
4. **Turnover Management:** Implement turnover-based trade frequency controls
5. **Advanced Date Filtering:** Add custom holiday calendars for different markets

## Related Files

- `v4/engine/execution_v4.py` - Order execution and validation
- `v4/engine/allocation_v4.py` - Position sizing and allocation logic
- `v4/engine/portfolio_v4.py` - Cash and position management
- `v4/utils/date_utils_v4.py` - Date filtering and business day logic
- `v4/settings/settings_parameters_v4.ini` - Configuration parameters
- `v4/config/allocation_rules_v4.py` - Allocation weight rules

---
*This document serves as the authoritative reference for trade frequency controls in CPS v4. Update as new controls are discovered or implemented.*
