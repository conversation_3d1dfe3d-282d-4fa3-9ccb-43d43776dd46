#!/usr/bin/env python3
"""
Debug script to test yfinance data download and filtering
"""
import sys
from pathlib import Path
sys.path.insert(0, r'S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library')
from data_retrieval.data_retrieval import fetch_historical_data_yf
import pandas as pd

# Add the v4 utils to path
sys.path.insert(0, 'v4')
from utils.date_utils_v4 import standardize_date, standardize_dataframe_index, filter_dataframe_by_dates

def test_yfinance_download():
    print("=== Testing yfinance download ===")
    
    # Test the function
    raw_data = fetch_historical_data_yf(ticker='SPY', years=10)
    print(f'Raw data shape: {raw_data.shape}')
    print(f'Raw data columns: {list(raw_data.columns)}')
    print(f'Date column type: {type(raw_data["Date"].iloc[0])}')
    print(f'Date range: {raw_data["Date"].min()} to {raw_data["Date"].max()}')
    print('First few rows:')
    print(raw_data.head())
    
    print("\n=== Testing conversion to index ===")
    # Test the conversion
    data = raw_data.set_index('Date')
    print(f'Index type: {type(data.index)}')
    print(f'Index dtype: {data.index.dtype}')
    print(f'Has timezone: {hasattr(data.index, "tz") and data.index.tz is not None}')
    print(f'Index range: {data.index.min()} to {data.index.max()}')
    
    print("\n=== Testing standardization ===")
    # Test standardization
    data_std = standardize_dataframe_index(data)
    print(f'Standardized index type: {type(data_std.index)}')
    print(f'Standardized index dtype: {data_std.index.dtype}')
    print(f'Standardized index range: {data_std.index.min()} to {data_std.index.max()}')
    
    print("\n=== Testing date filtering ===")
    # Test filtering
    start_date = standardize_date('2020-01-01')
    end_date = standardize_date('2025-07-21')
    print(f'Filter start_date: {start_date} (type: {type(start_date)})')
    print(f'Filter end_date: {end_date} (type: {type(end_date)})')
    
    filtered_data = filter_dataframe_by_dates(data_std, start_date, end_date)
    print(f'Filtered data shape: {filtered_data.shape}')
    print(f'Filtered date range: {filtered_data.index.min()} to {filtered_data.index.max()}')
    
    if filtered_data.empty:
        print("ERROR: Filtered data is empty!")
        print("Checking date overlap...")
        print(f"Data starts: {data_std.index.min()}")
        print(f"Data ends: {data_std.index.max()}")
        print(f"Filter starts: {start_date}")
        print(f"Filter ends: {end_date}")
        
        # Check if any dates overlap
        overlap_start = data_std.index >= start_date
        overlap_end = data_std.index <= end_date
        print(f"Dates >= start_date: {overlap_start.sum()}")
        print(f"Dates <= end_date: {overlap_end.sum()}")
        print(f"Dates in range: {(overlap_start & overlap_end).sum()}")
    else:
        print("SUCCESS: Filtered data has content")
        print(filtered_data.head())

if __name__ == "__main__":
    test_yfinance_download()
