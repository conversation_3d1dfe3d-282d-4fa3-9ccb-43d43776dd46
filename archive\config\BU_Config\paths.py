"""
Centralized path configuration for the backtesting framework.
All paths should be defined here and imported elsewhere.
"""

import os
import sys
from pathlib import Path
import importlib.util

# Base directories
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
DATA_DIR = PROJECT_ROOT / "data"
OUTPUT_DIR = PROJECT_ROOT / "output"

# External paths
VENV_PATH = Path(r"F:\AI_Library\my_quant_env")  # TODO: Consider making this configurable
CUSTOM_LIB_PATH = Path(r"S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library").absolute()

# Ensure output directory exists
OUTPUT_DIR.mkdir(exist_ok=True)

# Add project root to sys.path (highest priority)
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

# Add Custom Function Library to sys.path
if str(CUSTOM_LIB_PATH) not in sys.path:
    # Use str() directly without escape sequence modifications
    sys.path.insert(1, str(CUSTOM_LIB_PATH))

# Add all subdirectories of the Custom Function Library to sys.path
# This is necessary because the library doesn't have proper package structure
for subdir in CUSTOM_LIB_PATH.iterdir():
    if subdir.is_dir() and subdir.name not in ['__pycache__']:
        if str(subdir) not in sys.path:
            sys.path.append(str(subdir))

# Print Python path for debugging
print("Python version:", sys.version)
print("Python paths:")
for p in sys.path[:10]:  # Show first 10 paths
    print(f"  {p}")

# Derived paths
LOG_DIR = PROJECT_ROOT / "logs"
REPORT_DIR = OUTPUT_DIR / "reports"
CHART_DIR = OUTPUT_DIR / "charts"
OUTPUT_PATH = OUTPUT_DIR  # Add this for backward compatibility

# Ensure all directories exist
LOG_DIR.mkdir(exist_ok=True)
REPORT_DIR.mkdir(exist_ok=True)
CHART_DIR.mkdir(exist_ok=True)
