# CPS V4 Pipeline Equivalence Testing

This directory contains comprehensive equivalence testing for the CPS V4 backtest pipeline, ensuring that the unified pipeline produces exactly the same results as the decoupled (two-step) pipeline.

## Overview

The equivalence testing system validates that:

1. **Shape Assertion**: Row and column counts match exactly between outputs
2. **Numeric Equality**: All floating-point values match within 1×10⁻⁶ tolerance  
3. **Non-Numeric Equality**: All string/categorical values match exactly
4. **Fail-Fast Reporting**: Detailed error reporting with exact diffs when assertions fail

## Key Output Files Tested

- `signals_output.parquet` - Signal generation output
- `allocation_history_*.csv` - Portfolio allocation history  
- `trade_log_*.csv` - Trading transaction log
- `Signalwtg_output.parquet` - Signal weighting output (if present)

## Files in This Directory

### Core Testing Files
- **`test_equivalence_assertions.py`** - Main pytest test module with comprehensive assertions
- **`equivalence_utils.py`** - Utility functions for DataFrame comparison and reporting
- **`run_equivalence_test.py`** - Standalone test runner script

### Production Modules (for decoupled testing)
- **`production_modules/run_v4_pipeline.py`** - Decoupled pipeline orchestrator
- **`production_modules/run_signal_phase.py`** - Signal generation phase
- **`production_modules/run_trading_phase.py`** - Trading phase runner

### Test Configuration
- **`test_settings_small_range.ini`** - Test configuration with short date range (2020-01-01 to 2020-03-31)

## Usage

### Running with Pytest

```bash
# Run the equivalence test with pytest
cd tests/v4
python -m pytest test_equivalence_assertions.py -v

# Run with detailed logging
python -m pytest test_equivalence_assertions.py -v -s --log-cli-level=INFO
```

### Running Standalone

```bash
# Run the test directly
cd tests/v4
python run_equivalence_test.py
```

### Running from Project Root

```bash
# From project root directory
python -m pytest tests/v4/test_equivalence_assertions.py -v
```

## Test Process

The equivalence test follows this sequence:

### Step 1: Run Decoupled Pipeline
1. Execute the two-step decoupled pipeline using `run_v4_pipeline.py`
2. Capture timing information
3. Identify and preserve output files by renaming them with `decoupled_` prefix

### Step 2: Run Unified Pipeline  
1. Execute the single-step unified pipeline using `run_unified_pipeline.py`
2. Capture timing information  
3. Identify output files for comparison

### Step 3: Perform Equivalence Assertions
For each output file pair:
1. **Load DataFrames** using appropriate loader (parquet/CSV)
2. **Shape Assertion**: `assert df_unified.shape == df_decoupled.shape`
3. **Column/Index Verification**: Ensure names and order match
4. **Numeric Comparison**: `np.allclose(unified_values, decoupled_values, atol=1e-6)`
5. **Non-Numeric Comparison**: Exact equality using `df.equals()`
6. **Detailed Error Reporting**: Pinpoint exact differences on failure

### Step 4: Generate Test Report
1. Create comprehensive equivalence report
2. Include timing comparisons and performance metrics
3. Save detailed report to timestamped file
4. Log summary to console

## Assertion Details

### Shape Assertion
```python
assert df_unified.shape == df_decoupled.shape
```
Catches row/column count mismatches, truncation, or extra data.

### Numeric Equality Assertion
```python
np.allclose(df_unified[col], df_decoupled[col], atol=1e-6, rtol=1e-6)
```
Tolerates microscopic floating-point rounding differences while catching real value changes.

### Non-Numeric Equality Assertion  
```python
(df_unified[non_numeric_cols] == df_decoupled[non_numeric_cols]).all().all()
```
Exact string/categorical matching with no tolerance.

## Error Reporting

When assertions fail, the test provides detailed information:

- **Shape Mismatches**: Exact row/column count differences
- **Numeric Differences**: First differing value, position, and magnitude  
- **Non-Numeric Differences**: First differing string value and location
- **NaN Pattern Mismatches**: Count and location of missing values

Example error output:
```
[signals_output.parquet] Numeric mismatch in column 'SPY' at position 42: 
Unified=0.350000 vs Decoupled=0.349999 (diff=1e-06, tolerance=1e-06)
```

## Configuration

### Test Settings
The test uses `test_settings_small_range.ini` with:
- Date range: 2020-01-01 to 2020-03-31 (3 months)
- ETF list: SPY, SHV, EFA, TLT, PFF
- EMA strategy with standard parameters
- Fast execution for automated testing

### Tolerance Configuration
```python
TOLERANCE = 1e-6  # Floating-point comparison tolerance
```

This tolerance allows for:
- ✅ Normal floating-point rounding differences
- ❌ Real algorithmic or calculation differences

## Integration with CI/CD

The equivalence test is designed for automated testing:

```yaml
# Example GitHub Actions workflow
- name: Run Equivalence Tests
  run: |
    cd tests/v4
    python run_equivalence_test.py
```

Exit codes:
- `0` - All equivalence tests passed
- `1` - Test failures or errors

## Troubleshooting

### Common Issues

**Import Errors**
- Ensure all required modules are in Python path
- Check virtual environment activation

**Missing Output Files**
- Verify both pipelines completed successfully
- Check file permissions in `v4_trace_outputs/` directory

**Performance Differences** 
- Review test report for timing comparisons
- Large timing differences may indicate issues

**Tolerance Failures**
- Review numeric differences in detail
- Consider if tolerance needs adjustment for specific calculations
- Investigate root cause of numeric divergence

### Debugging

Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Run individual DataFrame comparisons:
```python
from equivalence_utils import DataFrameComparator
comparator = DataFrameComparator(tolerance=1e-6)
comparator.compare_dataframes(df1, df2, "unified", "decoupled", "test_file.csv")
```

## Test Output

### Console Output
The test provides structured logging with clear progress indicators:
```
============================================================
STEP 1: Running Decoupled Pipeline  
============================================================
INFO - Decoupled pipeline completed in 0:00:15.234567
INFO - Preserved decoupled signals_parquet as decoupled_signals_output.parquet

============================================================
STEP 3: Performing Equivalence Assertions
============================================================  
INFO - [signals_output.parquet] Shape assertion passed: (63, 5)
INFO - [signals_output.parquet] DataFrame equivalence verified ✓
INFO - ✓ signals_parquet comparison passed
```

### Report Files
Detailed reports are saved to:
```
v4_trace_outputs/equivalence_test_report_YYYYMMDD_HHMMSS.txt
```

## Maintenance

### Adding New Output Files
To test additional output files:

1. Update `get_latest_output_files()` in `test_equivalence_assertions.py`
2. Add appropriate file patterns to cleanup fixtures
3. Ensure both pipelines generate the new files consistently

### Adjusting Tolerance
If legitimate floating-point precision differences are detected:

1. Review the specific calculations causing differences
2. Consider adjusting `TOLERANCE` constant if justified
3. Document the rationale for tolerance changes

### Performance Monitoring
Monitor test execution times and pipeline performance ratios to detect regressions.

## References

- **Main Pipeline Code**: `v4/run_unified_pipeline.py`
- **Production Modules**: `tests/v4/production_modules/`
- **Settings Documentation**: `v4/settings/settings_CPS_v4.py`
- **CPS V4 Documentation**: `docs/CPS_v4/`
