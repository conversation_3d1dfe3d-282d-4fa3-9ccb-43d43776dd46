@echo off
REM tests/v4/run_integration_tests.bat
REM Batch file to run end-to-end integration tests for v4 pipeline

echo =======================================================
echo V4 Pipeline Integration Tests
echo =======================================================

REM Set up environment
if exist "%QUANT_ENV_PATH%" (
    call "%QUANT_ENV_PATH%\Scripts\activate.bat"
    echo Using virtual environment: %QUANT_ENV_PATH%
) else (
    echo Warning: QUANT_ENV_PATH not found, using system Python
)

echo.
echo Current directory: %cd%
echo Python executable: 
python --version

echo.
echo =======================================================
echo Running Integration Tests
echo =======================================================

REM Run pytest with verbose output
python -m pytest tests/v4/test_integration_pipeline.py -v --tb=short

echo.
echo =======================================================
echo Integration Test Run Complete
echo =======================================================

pause
