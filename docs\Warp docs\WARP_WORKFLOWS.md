# Warp Workflow Templates - Co<PERSON> & Paste

## SESSION START (Copy this to Warp)

```
START SESSION

Please read these files in order to understand current context:
1. memory-bank/current_session_context.md
2. memory-bank/execution_reference.md  
3. memory-bank/codebase_map.md

Then summarize:
- Current priorities (top 3 tasks)
- Any blockers from previous session
- Session goals for today
```

## MID-SESSION UPDATE (Copy this to Warp)

```
MID UPDATE

Update memory-bank/current_session_context.md with:
- <PERSON> completed tasks with ✅
- Add any new issues to BLOCKERS section
- Add discoveries to LEARNED THIS SESSION
- Update NEXT SESSION GOALS if priorities changed

Be specific about what was accomplished.
```

## SESSION END (Copy this to Warp)

```
END SESSION

Perform these actions:
1. Create memory-bank/session_updates/2025-XX-XX_completed.md with today's accomplishments
2. Update memory-bank/current_session_context.md - remove completed items, add new priorities
3. Update memory-bank/codebase_map.md if new functions/modules were discovered
4. Set clear goals for next session

Focus on what's DONE vs what's still TODO.
```

## QUICK STATUS CHECK (Copy this to Warp)

```
QUICK STATUS

Read memory-bank/current_session_context.md and provide:
- Current phase and % complete
- Top priority task right now
- Any critical blockers
- Recommended next action
```

## DOCUMENTATION UPDATE (Copy this to Warp)

```
DOCUMENT UPDATE

I discovered new functionality. Please update:
1. memory-bank/codebase_map.md - add new functions/modules
2. memory-bank/execution_reference.md - add new commands/paths if applicable
3. Add discovery to current_session_context.md LEARNED section

Details: [describe what was discovered]
```

## BLOCKER ESCALATION (Copy this to Warp)

```
BLOCKER

I'm stuck on: [describe issue]

Please:
1. Add this to BLOCKERS in current_session_context.md
2. Check codebase_map.md for existing solutions
3. Check execution_reference.md for correct commands
4. Suggest alternative approaches or next steps
```

---

## Benefits of These Templates

- **Consistent workflow** across all AI sessions
- **No memory required** - just copy/paste the appropriate template
- **Automatic documentation** - AI maintains files without prompting
- **Quick context switching** - any template gets AI up to speed fast
- **Prevents duplication** - templates enforce checking existing work first

git reset --soft HEAD~{{num_commits}} && git commit
