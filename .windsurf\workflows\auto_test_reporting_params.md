---
description: Automated testing workflow for V4 reporting
---

# Automated Testing Workflow for Reporting + Parameters

## Step 1: review memories in plan

- S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\CPS_V4_Master_Plan.md

Also - review internal cascade plan  - confirm access

- Very brief recap of each of these to verify you read them

## Step 2: Review md docs

 "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\memory-bank\v4_module_functions_list_AI.md" + in
 "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4" +
 trade_portfolio_engine_flow.md
 +
How_signals_work.md
  
## Step 3.1

In root we are using
  run_trading_phase_standalone.bat

- output here
\v4_trace_outputs

## Step 3.2

  Record the timestamp of the bat runs, as below:
    The execution timestamp is 2025-06-20 13:30:15 and the process ID is 273.
  And then review only recent (within 1 minute or less after the run time) the *filter*.txt and csv  results

  Search filter txt output from runs, include any occurance of "error" or "Error" or "fatal" etc (all cap variations).  
= lessons learned = some files (csv and certainly XLSX ) created the AI gets very stuck - 10+ shots - to even read the output, and understand and verify.   How do we build more automated tests and flows
   Output and fix very slow, 1 at a time;  

## Step 3.3: Focus on Tracing and Fixing the passing of signals into the separate trading portfolio stage engine

- Do not ask permission - run, evaluate, fix, run

// turbo-all
 and run the bat tests in non-blocking mode

3. If failures detected:
   - Automatically fix issues
   - Repeat test execution
4. If all tests pass:
   - Notify user of success for review
    - update documentation AFTER affirmed by user of success
