"""
Asset allocation model for financial backtesting.
This serves as a template that can be easily customized with different allocation strategies.
"""

import numpy as np
import pandas as pd
import logging
import sys
from pathlib import Path
from config.paths import *
from config.config import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import allocation strategies from Custom Function Library
from portfolio.minV_portfolio_optimization import (
    port_min_variance_portfolio,
    port_equal_weight_portfolio
)
from portfolio.advanced_portfolio_optimization import (
    port_risk_parity_portfolio,
    port_hierarchical_risk_parity
)

# Import local allocation models
from models.ema_allocation_model import ema_allocation_model


def equal_weight_allocation(price_data, returns_data=None, **params):
    """
    Equal weight allocation model. Simplest allocation strategy that 
    assigns equal weight to each asset.
    Uses the Custom Function Library's port_equal_weight_portfolio function.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame, optional): Historical returns data
        **params: Additional parameters
        
    Returns:
        dict: Asset weights dictionary
    """
    try:
        # Use the library function for equal weight portfolio
        weights_df = port_equal_weight_portfolio(
            returns=returns_data if returns_data is not None else price_data.pct_change().fillna(0)
        )
        weights = weights_df.iloc[-1].to_dict()
        return weights
    except Exception as e:
        logger.error(f"Error in equal_weight_allocation using library function: {e}")
        logger.info("Falling back to simple equal weight implementation")
        
        # Simple fallback implementation
        n_assets = len(price_data.columns)
        equal_weight = 1.0 / n_assets
        weights = {asset: equal_weight for asset in price_data.columns}
        return weights


def simple_momentum_allocation(price_data, returns_data=None, 
                              lookback_period=252, top_n=None, 
                              min_weight=0.0, max_weight=1.0, **params):
    """
    Simple momentum-based allocation model.
    Allocates based on recent price performance.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame, optional): Historical returns data
        lookback_period (int): Lookback period in days
        top_n (int, optional): Number of top performers to include
        min_weight (float): Minimum weight per asset
        max_weight (float): Maximum weight per asset
        **params: Additional parameters
        
    Returns:
        dict: Asset weights dictionary
    """
    # Use technical indicators from Custom Function Library if available
    try:
        from technical_indicators.indicators import calculate_momentum
        momentum = calculate_momentum(price_data, lookback_period)
        momentum = momentum.iloc[-1]  # Get the latest momentum values
    except ImportError:
        # Calculate momentum (price change over lookback period) if library function not available
        latest_prices = price_data.iloc[-1]
        past_prices = price_data.iloc[-min(lookback_period, len(price_data))]
        momentum = (latest_prices / past_prices) - 1
    
    # Sort assets by momentum
    sorted_assets = momentum.sort_values(ascending=False)
    
    # Select top N assets if specified
    if top_n is not None and top_n < len(sorted_assets):
        selected_assets = sorted_assets.index[:top_n]
        # Equal weights for selected assets
        equal_weight = 1.0 / top_n
        weights = {asset: equal_weight if asset in selected_assets else 0.0 
                  for asset in price_data.columns}
    else:
        # Weight proportional to momentum (only positive momentum)
        positive_momentum = sorted_assets[sorted_assets > 0]
        if len(positive_momentum) > 0:
            # Normalize weights
            total = positive_momentum.sum()
            weights = {asset: 0.0 for asset in price_data.columns}
            for asset in positive_momentum.index:
                weights[asset] = positive_momentum[asset] / total
        else:
            # If no positive momentum, equal weight
            weights = equal_weight_allocation(price_data)
    
    # Apply min/max weight constraints
    for asset in weights:
        weights[asset] = max(min(weights[asset], max_weight), min_weight)
    
    # Renormalize after applying constraints
    total = sum(weights.values())
    if total > 0:
        weights = {asset: weight / total for asset, weight in weights.items()}
    
    return weights


def minimum_variance_allocation(price_data, returns_data, 
                               lookback_period=252, 
                               min_weight=0.0, max_weight=1.0, **params):
    """
    Minimum variance allocation model.
    Uses the Custom Function Library's port_min_variance_portfolio function.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame): Historical returns data
        lookback_period (int): Lookback period in days
        min_weight (float): Minimum weight per asset
        max_weight (float): Maximum weight per asset
        **params: Additional parameters
        
    Returns:
        dict: Asset weights dictionary
    """
    try:
        # Get recent returns for covariance calculation
        recent_returns = returns_data.iloc[-min(lookback_period, len(returns_data)):]
        
        # Use the library function for minimum variance portfolio
        weights_df = port_min_variance_portfolio(
            returns=recent_returns,
            constraints={
                'min_weight': min_weight,
                'max_weight': max_weight
            }
        )
        weights = weights_df.iloc[-1].to_dict()
        
    except Exception as e:
        logger.error(f"Error in minimum_variance_allocation: {e}")
        # Fallback to equal weight if an error occurs
        weights = equal_weight_allocation(price_data)
    
    return weights


def risk_parity_allocation(price_data, returns_data, 
                          lookback_period=252, 
                          min_weight=0.0, max_weight=1.0, **params):
    """
    Risk parity allocation model.
    Uses the Custom Function Library's port_risk_parity_portfolio function.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame): Historical returns data
        lookback_period (int): Lookback period in days
        min_weight (float): Minimum weight per asset
        max_weight (float): Maximum weight per asset
        **params: Additional parameters
        
    Returns:
        dict: Asset weights dictionary
    """
    try:
        # Get recent returns for risk calculation
        recent_returns = returns_data.iloc[-min(lookback_period, len(returns_data)):]
        
        # Use the library function for risk parity portfolio
        weights_df = port_risk_parity_portfolio(
            returns=recent_returns,
            constraints={
                'min_weight': min_weight,
                'max_weight': max_weight
            }
        )
        weights = weights_df.iloc[-1].to_dict()
        
    except Exception as e:
        logger.error(f"Error in risk_parity_allocation: {e}")
        # Fallback to equal weight if an error occurs
        weights = equal_weight_allocation(price_data)
    
    return weights


def hierarchical_risk_parity_allocation(price_data, returns_data, 
                                       lookback_period=252, 
                                       min_weight=0.0, max_weight=1.0, **params):
    """
    Hierarchical risk parity allocation model.
    Uses the Custom Function Library's port_hierarchical_risk_parity function.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame): Historical returns data
        lookback_period (int): Lookback period in days
        min_weight (float): Minimum weight per asset
        max_weight (float): Maximum weight per asset
        **params: Additional parameters
        
    Returns:
        dict: Asset weights dictionary
    """
    try:
        # Get recent returns for risk calculation
        recent_returns = returns_data.iloc[-min(lookback_period, len(returns_data)):]
        
        # Use the library function for hierarchical risk parity portfolio
        weights_df = port_hierarchical_risk_parity(
            returns=recent_returns,
            constraints={
                'min_weight': min_weight,
                'max_weight': max_weight
            }
        )
        weights = weights_df.iloc[-1].to_dict()
        
    except Exception as e:
        logger.error(f"Error in hierarchical_risk_parity_allocation: {e}")
        # Fallback to equal weight if an error occurs
        weights = equal_weight_allocation(price_data)
    
    return weights


def custom_allocation_template(price_data, returns_data, **params):
    """
    Template for custom allocation strategies.
    To be replaced with a specific formula in future steps.
    
    Args:
        price_data (DataFrame): Historical price data
        returns_data (DataFrame): Historical returns data
        **params: Additional parameters
        
    Returns:
        dict: Asset weights dictionary
    """
    # This will be replaced with your custom formula
    # By default, use equal weight allocation
    return equal_weight_allocation(price_data, returns_data, **params)


# Dictionary mapping strategy names to functions
ALLOCATION_STRATEGIES = {
    'equal_weight': equal_weight_allocation,
    'momentum': simple_momentum_allocation,
    'min_variance': minimum_variance_allocation,
    'risk_parity': risk_parity_allocation,
    'hierarchical_risk_parity': hierarchical_risk_parity_allocation,
    'custom': custom_allocation_template,
    'ema': ema_allocation_model
}


def get_allocation_model(strategy_name):
    """
    Get the allocation model function by name.
    
    Args:
        strategy_name (str): Name of the allocation strategy
        
    Returns:
        function: The allocation model function
    """
    if strategy_name in ALLOCATION_STRATEGIES:
        return ALLOCATION_STRATEGIES[strategy_name]
    else:
        logger.warning(f"Strategy '{strategy_name}' not found, using equal_weight as default")
        return equal_weight_allocation
