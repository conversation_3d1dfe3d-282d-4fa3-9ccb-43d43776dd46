"""
Adapter module for parameter optimization functions.
This module imports functions from the Custom Function Library's parameter_optimization module
and re-exports them to avoid naming conflicts.
"""

import sys
import logging
from config.paths import CUSTOM_LIB_PATH

# Configure logging
logger = logging.getLogger(__name__)

# Import functions from the Custom Function Library
try:
    # Ensure the Custom Function Library is in the path
    if str(CUSTOM_LIB_PATH) not in sys.path:
        sys.path.insert(0, str(CUSTOM_LIB_PATH))
    
    # Import the functions
    from config.parameter_optimization import (
        define_parameter,
        validate_parameter,
        get_parameter_range,
        get_parameter_combinations,
        filter_parameter_combinations,
        add_derived_parameters,
        optimize_parameters
    )
    
    logger.info("Successfully imported parameter optimization functions from Custom Function Library")
    
except ImportError as e:
    logger.error(f"Failed to import parameter optimization functions: {e}")
    
    # Define fallback functions if needed
    def define_parameter(optimize, default_value, min_value, max_value, increment):
        """Fallback implementation of define_parameter."""
        optimize_flag = 'Y' if optimize else 'N'
        return (optimize_flag, default_value, min_value, max_value, increment)
    
    def validate_parameter(param, param_name):
        """Fallback implementation of validate_parameter."""
        return True
    
    # Other fallback functions can be defined here if needed
    
    logger.warning("Using fallback parameter optimization functions")
