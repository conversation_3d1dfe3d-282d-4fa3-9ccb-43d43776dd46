# Unified Pipeline Project - Issues and Required Corrections

## Date: 2025-06-30

## CRITICAL ISSUES IDENTIFIED

### 1. Retry Mechanisms Added Against User Directives
**Problem**: The unified pipeline implementation includes retry wrappers and fallback mechanisms that the user explicitly rejected.

**User's Clear Requirements**:
- NO retry mechanisms - want failures to be immediately visible for tracing
- NO fallbacks - want to diagnose and fix root causes
- NO "transient connectivity failures" handling - not applicable to this setup

**Files with problematic retry logic**:
- `v4/run_unified_pipeline.py` (lines 280-299)
- `v4/utils/retry_wrapper.py` (if exists)
- Settings in `v4/settings/settings_parameters_v4.ini` (retry configuration)

### 2. Over-Engineering Without User Approval
**Problem**: Added complex error handling and retry systems without discussing with user first.

## CURRENT PROJECT STATE

### Completed Components
1. **Unified Pipeline Core** - `v4/run_unified_pipeline.py`
   - Combines signal generation and trading phases
   - Direct DataFrame handoff between phases
   - Comprehensive logging with milestones

2. **Settings System** - `v4/settings/settings_CPS_v4.py`
   - Parameter loading from INI files
   - Type conversion and validation
   - Support for ComplexN, SimpleA, SimpleN, AlphaList parameter types

3. **Execution Scripts**
   - `run_main_v4_unified.bat` - Main unified pipeline launcher
   - `run_main_v4_signalalgo.bat` - Separate runs for comparison

### Production Configuration
- **Lookback Period**: 60 days (from settings)
- **Date Range**: 2020-01-01 to 2025-06-10
- **Assets**: Group1ETFBase ('SPY', 'SHV', 'EFA', 'TLT', 'PFF')
- **Strategy**: EMA Crossover (12/26 period defaults)

### Output Structure
- **v4_trace_outputs/**: Main output directory
- **Full logs**: Complete execution logs with timestamps
- **Filtered logs**: Milestone and error extraction
- **Data files**: Signals, allocation history, trade logs

## REQUIRED CORRECTIONS

### 1. Remove Retry Mechanisms (IMMEDIATE)
```python
# REMOVE from run_unified_pipeline.py lines 280-299
# Replace with direct execution:
trading_results = modify_run_trading_to_accept_dataframe(
    signals_df=signals_df,
    signals_file=signals_file
)
```

### 2. Update Settings (IMMEDIATE)
```ini
# REMOVE from settings_parameters_v4.ini:
enable_retry_wrapper = False  # Set to False or remove entirely
max_retries = 0
retry_delay_seconds = 0
```

### 3. Simplify Error Handling
- Keep basic exception catching for logging
- Remove all retry/recovery logic
- Ensure immediate failure propagation

## STEP 10 VALIDATION PLAN (CORRECTED)

### Objective
Execute unified pipeline over standard production lookback period and verify:
1. **Runtime within expected bounds** (estimate 30-120 seconds based on existing logs)
2. **Output parity with separate runs** (row-wise diff ≤1e-6)
3. **Logs show correct milestone order**

### Expected Runtime Bounds
Based on existing v4_trace_outputs logs, typical runtimes appear to be:
- Signal generation: ~10-30 seconds
- Trading phase: ~20-60 seconds
- Total unified: ~30-120 seconds

### Validation Steps
1. **Run unified pipeline**: `run_main_v4_unified.bat`
2. **Run separate processes**: `run_main_v4_signalalgo.bat` 
3. **Compare outputs**: 
   - Allocation history files
   - Trade log files
   - Signal outputs
4. **Verify milestone order** in filtered logs:
   - START → SIGNAL_START → POST_SIGNAL → TRADING_START → POST_TRADING → COMPLETE

### Expected Outputs
- `v4_trace_outputs/unified_*.txt` - Full and filtered logs
- `v4_trace_outputs/allocation_history_*.csv`
- `v4_trace_outputs/trade_log_*.csv`
- `v4_trace_outputs/signals_output_*.parquet`

## KEY FILES AND LOCATIONS

### Core Engine Files
- `v4/run_unified_pipeline.py` - Main unified pipeline
- `v4/Algo_signal_phase.py` - Signal generation
- `v4/run_trading_phase.py` - Trading execution
- `v4/engine/backtest_v4.py` - Backtesting engine

### Configuration
- `v4/settings/settings_parameters_v4.ini` - All parameters
- `v4/settings/settings_CPS_v4.py` - Settings loader

### Execution Scripts
- `run_main_v4_unified.bat` - Unified pipeline
- `run_main_v4_signalalgo.bat` - Decoupled pipeline
- `master_config.bat` - Environment setup

### Output Directories
- `v4_trace_outputs/` - All pipeline outputs
- `output/` - Legacy v3 outputs (for reference)

## ARCHITECTURAL DECISIONS MADE

### 1. Direct DataFrame Handoff
- Signal phase saves to both file and returns DataFrame
- Trading phase accepts DataFrame directly (no file I/O between phases)
- Maintains file outputs for debugging/validation

### 2. Shared Logging
- Single logger instance across both phases
- Milestone markers for pipeline tracking
- Both console and file output

### 3. Configuration-Driven
- All parameters from INI file
- No hardcoded values in pipeline logic
- Support for optimization parameters (ComplexN type)

## BLOCKERS TO ADDRESS

1. **IMMEDIATE**: Remove retry mechanisms from unified pipeline
2. **IMMEDIATE**: Update settings to disable retry configurations
3. **Before Step 10**: Verify corrected pipeline executes without retry logic
4. **Step 10**: Execute validation with corrected pipeline

## NEXT SESSION PRIORITIES

1. Remove retry mechanisms and test execution
2. Complete Step 10 validation with simplified error handling
3. Document any performance or output differences found
4. Finalize unified pipeline as production-ready

## LESSONS LEARNED

- Always confirm architectural decisions with user before implementation
- User's explicit requirements take precedence over "best practices"
- Fail-fast approach preferred over automatic recovery in this environment
- Keep implementations simple and traceable
