"""
Strategy registry for multi-strategy backtesting.
"""

# Registry mapping strategy name to function
_REGISTRY = {}

def register_strategy(name):
    """
    Decorator to register a strategy function under the given name.
    """
    def deco(fn):
        _REGISTRY[name] = fn
        return fn
    return deco


def get_strategies():
    """
    Return a copy of the strategy registry.
    """
    return _REGISTRY.copy()
