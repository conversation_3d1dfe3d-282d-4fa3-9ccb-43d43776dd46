import os
import sys
import configparser
from datetime import datetime

def get_config():
    """Read configuration from config.ini"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
    config.read(config_path)
    print(f"Reading config from: {config_path}")
    return config['PATHS']['project_root']

def get_valid_files():
    """Strict path validation with no variations"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
    config.read(config_path)
    base = config['PATHS']['project_root']
    
    required_files = [
        # Look for files in various potential locations
        # First check root memory-bank directory
        (os.path.join(base, "memory-bank", "v3_module+functions_list_AI.md"), "documentation,module_reference"),
        (os.path.join(base, "memory-bank", "reporting_system_AI.md"), "system_architecture,documentation,reporting"),
        (os.path.join(base, "memory-bank", "systemFiles+Flow_AI.md"), "system_architecture,documentation,system_flow"),
        (os.path.join(base, "memory-bank", "parameter_management_AI.md"), "documentation,parameter_system"),
        
        # Then check subdirectories
        (os.path.join(base, "memory-bank", "architecture", "reporting_system_AI.md"), "system_architecture,documentation,reporting"),
        (os.path.join(base, "memory-bank", "architecture", "systemFiles+Flow_AI.md"), "system_architecture,documentation,system_flow"),
        (os.path.join(base, "memory-bank", "architecture", "v3_module+functions_list_AI.md"), "documentation,module_reference"),
        (os.path.join(base, "memory-bank", "parameters", "parameter_management_AI.md"), "documentation,parameter_system"),
        
        # Check docs directory
        (os.path.join(base, "docs", "v3_module+functions_list.md"), "documentation,module_reference"),
        (os.path.join(base, "docs", "v3_performance_reporting_standards_a.md"), "documentation,reporting_standards"),
        (os.path.join(base, "docs", "v3_parameter_system_reference.md"), "documentation,parameter_system"),
        # Add any additional documentation files here
    ]
    
    valid_files = []
    for file_path, tags in required_files:
        if os.path.exists(file_path):
            print(f"Found valid file: {file_path}")
            valid_files.append((file_path, tags))
        else:
            print(f"File not found: {file_path}")
    
    return valid_files

def read_file_content(file_path):
    """Simply read the content of a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}")
        return None

def verify_file_exists(path):
    """Robust file verification with detailed diagnostics"""
    try:
        if not os.path.exists(path):
            print(f"Path does not exist: {path}")
            return False
        if not os.path.isfile(path):
            print(f"Path is not a file: {path}")
            return False
        try:
            with open(path, 'r', encoding='utf-8') as test:
                test.read(1)  # Test file read
            return True
        except PermissionError:
            print(f"Permission denied: {path}")
            return False
        except Exception as e:
            print(f"Access error: {str(e)} - {path}")
            return False
    except Exception as e:
        print(f"Verification failed: {str(e)} - {path}")
        return False

def verify_files_exist():
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
    config.read(config_path)
    base = config['PATHS']['project_root']
    
    # Check if any of these essential directories exist
    directories = [
        os.path.join(base, "memory-bank"),
        os.path.join(base, "docs")
    ]
    
    dir_exists = False
    for directory in directories:
        if os.path.exists(directory) and os.path.isdir(directory):
            print(f"Found directory: {directory}")
            dir_exists = True
        else:
            print(f"Directory not found: {directory}")
    
    return dir_exists  # Continue if at least one directory exists

def find_all_markdown_files():
    """Find all markdown files in the memory-bank and docs directories"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
    config.read(config_path)
    base = config['PATHS']['project_root']
    
    memory_bank_dir = os.path.join(base, "memory-bank")
    docs_dir = os.path.join(base, "docs")
    
    markdown_files = []
    
    # Walk through memory-bank directory recursively
    if os.path.exists(memory_bank_dir) and os.path.isdir(memory_bank_dir):
        for root, dirs, files in os.walk(memory_bank_dir):
            for file in files:
                if file.endswith(".md"):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, base)
                    # Create tags based on directory structure
                    dir_parts = os.path.dirname(rel_path).replace("\\", "/").split("/")
                    tags = ",".join([p for p in dir_parts if p])
                    if not tags:
                        tags = "documentation"
                    print(f"Found markdown file: {file_path}")
                    markdown_files.append((file_path, tags))
    
    # Walk through docs directory
    if os.path.exists(docs_dir) and os.path.isdir(docs_dir):
        for root, dirs, files in os.walk(docs_dir):
            for file in files:
                if file.endswith(".md"):
                    file_path = os.path.join(root, file)
                    # Skip ingest scripts directory
                    if "ingest_scripts" not in file_path:
                        print(f"Found markdown file: {file_path}")
                        markdown_files.append((file_path, "documentation"))
    
    return markdown_files

def generate_combined_markdown(valid_files):
    """Generate a single markdown file that combines all documentation"""
    output_dir = os.path.join(os.path.dirname(__file__), 'combined_docs')
    os.makedirs(output_dir, exist_ok=True)
    
    combined_file = os.path.join(output_dir, f"combined_documentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
    
    with open(combined_file, 'w', encoding='utf-8') as f:
        f.write("# Combined Project Documentation\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("## Table of Contents\n\n")
        
        # First write the table of contents
        for i, (file_path, tags) in enumerate(valid_files, 1):
            title = os.path.basename(file_path).replace('.md', '')
            f.write(f"{i}. [{title}](#{title.lower().replace(' ', '-')})\n")
        
        f.write("\n---\n\n")
        
        # Then write each document's content
        for file_path, tags in valid_files:
            title = os.path.basename(file_path).replace('.md', '')
            content = read_file_content(file_path)
            
            if content:
                f.write(f"## {title}\n\n")
                f.write(f"*Source: `{file_path}`*\n\n")
                f.write(f"*Tags: {tags}*\n\n")
                f.write(f"{content}\n\n")
                f.write("---\n\n")
                print(f"Added content from: {file_path}")
    
    print(f"\nCombined documentation generated at: {combined_file}")
    return combined_file

if __name__ == "__main__":
    if not verify_files_exist():
        print("Aborting: Missing required files")
        sys.exit(1)
    
    # First try the specifically defined files
    valid_files = get_valid_files()
    
    # If no specific files found, try scanning for all markdown files
    if not valid_files:
        print("No specific files found. Scanning for all markdown files...")
        valid_files = find_all_markdown_files()
    
    if not valid_files:
        print("No valid files found to process")
    else:
        print(f"\nFound {len(valid_files)} documentation files to process")
        combined_file = generate_combined_markdown(valid_files)
        print(f"\n[✓] Documentation combined successfully into: {combined_file}")
