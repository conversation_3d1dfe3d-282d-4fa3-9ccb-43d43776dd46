"""
run_v4_decoupled_pipeline.py
Thin orchestration layer for the decoupled signal and trading phases.

This script provides a simple connection between Algo_signal_phase.py and run_trading_phase.py,
executing them in sequence and handling basic error reporting.
"""

import os
import sys
import time
import logging
from pathlib import Path
from datetime import datetime
import importlib.util

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

def import_module_from_file(module_path):
    """Import a module from file path."""
    module_name = Path(module_path).stem
    spec = importlib.util.spec_from_file_location(module_name, module_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module

def run_decoupled_pipeline():
    """Run the decoupled signal and trading phases in sequence."""
    start_time = time.time()
    logger.info("[MILESTONE] Starting V4 Decoupled Pipeline")
    signals_file = None
    
    # Step 1: Run Signal Phase
    try:
        logger.info("[MILESTONE] Starting Signal Generation Phase")
        signal_module_path = _project_root / "v4" / "Algo_signal_phase.py"
        signal_module = import_module_from_file(str(signal_module_path))
        
        # Run signal phase and get signal file path
        signals_file = signal_module.run_signal_phase()
        
        if not signals_file or not Path(signals_file).exists():
            logger.error("Signal phase completed but no valid signal file was produced")
            return False
            
        logger.info(f"Signal phase completed successfully, signals saved to: {signals_file}")
    except Exception as e:
        logger.error(f"Signal phase failed with error: {e}", exc_info=True)
        return False
    
    # Step 2: Run Trading Phase
    try:
        logger.info("[MILESTONE] Starting Trading Phase")
        trading_module_path = _project_root / "v4" / "run_trading_phase.py"
        trading_module = import_module_from_file(str(trading_module_path))
        
        # Run trading phase with the signals file
        trading_results = trading_module.run_trading_phase(signals_file=signals_file)
        
        if not trading_results:
            logger.error("Trading phase completed but no results were returned")
            return False
            
        logger.info("Trading phase completed successfully")
    except Exception as e:
        logger.error(f"Trading phase failed with error: {e}", exc_info=True)
        return False
    
    # Report pipeline completion
    elapsed_time = time.time() - start_time
    logger.info(f"[MILESTONE] V4 Decoupled Pipeline completed in {elapsed_time:.2f} seconds")
    return True

if __name__ == "__main__":
    success = run_decoupled_pipeline()
    sys.exit(0 if success else 1)
