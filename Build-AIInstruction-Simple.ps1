# Build-AIInstruction - Generate AI instruction block and execute next priority
function Build-AIInstruction {
    cps
    Write-Host "🎯 AI: Generate instruction block and execute next priority:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "STEP 1 - Generate Instruction Block:" -ForegroundColor Yellow
    Write-Host "1. Read memory-bank/current_session_context.md" -ForegroundColor White
    Write-Host "2. Extract TOP 3 PRIORITIES + any BLOCKERS" -ForegroundColor White
    Write-Host "3. Create markdown block with:" -ForegroundColor White
    Write-Host "   - Priority title + brief context line" -ForegroundColor Gray
    Write-Host "   - Key file references (code & docs from memory-bank/codebase_map.md)" -ForegroundColor Gray
    Write-Host "   - Suggested execution commands (from memory-bank/execution_reference.md)" -ForegroundColor Gray
    Write-Host "4. Write block to console AND save to memory-bank/next_ai_instructions.md" -ForegroundColor White
    Write-Host ""
    Write-Host "STEP 2 - Update Status & Execute:" -ForegroundColor Yellow
    Write-Host "1. Mark any completed tasks in memory-bank/current_session_context.md with ✅" -ForegroundColor White
    Write-Host "2. Update CURRENT PRIORITIES section - remove completed items" -ForegroundColor White
    Write-Host "3. Add any new discoveries to LEARNED THIS SESSION" -ForegroundColor White
    Write-Host "4. IMMEDIATELY start working on Priority #1 from the instruction block" -ForegroundColor White
    Write-Host ""
    Write-Host "Execute this instruction block generation and priority execution NOW." -ForegroundColor Red
}
