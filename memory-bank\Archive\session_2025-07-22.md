## New Functional Requirements

### 2% Deviation Rule
- **Definition:** Implement a rule to filter out trades where the deviation from the previous signal exceeds 2%. This helps in maintaining stability and consistency in the trading strategy.
- **Parameterization:** Ensure that this threshold is defined as a parameter within `settings_CPS_v4.py` and can be adjusted easily.

### Parameterization Expectations
- **Centralized Configuration:** All thresholds and key parameters, including the 2% deviation rule, must be centrally defined in `settings_CPS_v4.py`.
- **No Hardcoding:** Adhere to the project's core principle of using configuration files for all logic and parameters, avoiding hardcoded values within source code.

### Business-Calendar Validation Logic
- **Logic:** Derive open/close days solely from price-data availability to ensure that trading only occurs on valid business days, consistent with actual market operation.
- **Implementation:** Utilize the pandas business day functionality or an equivalent method to automatically skip weekends and holidays.

### Consistency with RULE Documents
- Ensure adherence to the architectural principles and coding style outlined in the AI Agent Guidelines. Parameters should be accessed directly from configuration files, and the logic should be modular and decoupled for flexibility.
- Follow existing coding and project guidelines to maintain consistency across the development process.

# Current Session Context - Updated 2025-07-22

## ✅ COMPLETED TASKS
- [x] Complete inventory of CPS V4 components (Step 1 from broader plan)
- [x] Update codebase_map.md with full V4 architecture
- [x] Document all modules, functions, methods, and data flow patterns
- [x] Create PowerShell profile for enhanced CPS V4 workflow
- [x] Establish session persistence documentation
- [x] **UNIFIED PIPELINE OPTIMIZATION** - Eliminated duplicate signal files
- [x] **Signal File Consolidation** - Only timestamped outputs generated
- [x] **Fallback Logic Enhancement** - Auto-finds most recent signal files
- [x] **Documentation Updates** - Memory bank reflects optimized workflow
- [x] **Production Testing** - Verified optimized unified pipeline works

## 🎯 CURRENT PRIORITIES (Top 3)
1. **Production Workflow Validated** - Unified pipeline is PRIMARY workflow ✅
2. **Testing Framework** - Equivalence testing maintains compatibility ✅ 
3. **Performance Optimization** - Signal processing streamlined ✅

## 🚫 BLOCKERS
- None currently identified - optimization completed successfully

## 📚 LEARNED THIS SESSION
- **UNIFIED PIPELINE OPTIMIZATION**: Successfully eliminated duplicate signal file generation
- **PRODUCTION FOCUS**: Unified pipeline (`run_main_v4_unified.bat`) is now primary workflow
- **TIMESTAMPED APPROACH**: Single timestamped output provides better traceability
- **FALLBACK RESILIENCE**: Auto-detection of most recent signal files improves reliability
- **DOCUMENTATION SYNC**: All memory bank files updated to reflect optimized state

## 🎯 NEXT SESSION GOALS
1. **Advanced Testing**: Run comprehensive equivalence tests with optimized pipeline
2. **Performance Analysis**: Compare execution times pre/post optimization
3. **Parameter Optimization**: Implement 2% trade deviation threshold if needed
4. **Business Calendar Logic**: Add market day validation if required

## 📝 NOTES
- Project directory: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template`
- Main entry point: `main_v4_production_run.py`
- Settings: `v4/settings/settings_CPS_v4.py`
- Documentation: All in `memory-bank/` directory

## 🔧 TECHNICAL STATUS
- ✅ Codebase fully mapped and documented
- ✅ PowerShell profile installed and configured
- ✅ Session management functions created
- ✅ **UNIFIED PIPELINE OPTIMIZED** - Production workflow streamlined
- ✅ **SIGNAL FILE CONSOLIDATION** - Single timestamped outputs only
- ✅ **FALLBACK LOGIC ENHANCED** - Auto-detection of recent files
- 🔄 Ready for advanced testing and parameter optimization

## 📊 PROJECT HEALTH
- **Architecture**: Fully documented and optimized ✅
- **Production Pipeline**: Streamlined and tested ✅
- **Tooling**: Enhanced with PowerShell profile ✅
- **Documentation**: Complete and up-to-date ✅
- **Workflow**: Optimized for primary use case ✅
- **Testing Framework**: Maintains backward compatibility ✅
