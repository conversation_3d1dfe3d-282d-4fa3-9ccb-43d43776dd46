Stop reading that md file; you have been stuck on it 3 times
continue with rest of request
Review @s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template/v4_trace_outputs\trade_log_20250626_190425.csv 
I believe the rebalancing through trades are happening more often then the rules intended.   I had a rule, something like that the current allocation needs to be different than the signal by more than 2% for trades to take place.   So, each day (where their is price data in the file = single source of truth for market open) then test if any % allocation deviations vs signal % allocation greater than 2%, then do a full rebalance to the objective allocations mix.   Otherwise, filter no trades that day.
Search rules, and find where in the trade step something like that is coded; it does not seem to be correctly implemented; please investigate.