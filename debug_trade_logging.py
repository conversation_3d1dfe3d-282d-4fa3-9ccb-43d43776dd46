#!/usr/bin/env python3
"""
Debug script to understand why trades are not being logged.
"""

import pandas as pd
import sys
from pathlib import Path

# Add project root to path
_project_root = Path(__file__).parent
sys.path.insert(0, str(_project_root))

from v4.engine.portfolio_v4 import Portfolio
from v4.engine.orders_v4 import TradeLog

def debug_trade_logging():
    """Debug the trade logging mechanism."""
    
    print("=== DEBUGGING TRADE LOGGING ===")
    
    # Create a simple trade log and test it
    trade_log = TradeLog()
    print(f"Initial trade log size: {len(trade_log.trades)}")
    
    # Convert to DataFrame
    df = trade_log.to_dataframe()
    print(f"Empty trade log DataFrame shape: {df.shape}")
    print(f"Empty trade log DataFrame columns: {list(df.columns)}")
    print(f"Empty trade log DataFrame:\n{df}")
    
    # Test if we can create a simple trade manually
    from v4.engine.orders_v4 import Order, Trade
    from datetime import date
    
    # Create a test order
    test_order = Order('SPY', 100, date(2020, 1, 2))
    print(f"Created test order: {test_order}")
    
    # Create a test trade
    test_trade = Trade(
        order=test_order,
        execution_date=date(2020, 1, 2),
        execution_price=300.0,
        commission=1.0,
        amount=30001.0
    )
    print(f"Created test trade: {test_trade}")
    
    # Add to trade log
    trade_log.add_trade(test_trade)
    print(f"Trade log size after adding test trade: {len(trade_log.trades)}")
    
    # Convert to DataFrame again
    df2 = trade_log.to_dataframe()
    print(f"Trade log DataFrame after adding trade shape: {df2.shape}")
    print(f"Trade log DataFrame after adding trade:\n{df2}")
    
    # Test saving to CSV
    output_file = _project_root / "debug_trade_log_test.csv"
    df2.to_csv(output_file)
    print(f"Saved test trade log to: {output_file}")
    
    # Read it back
    df3 = pd.read_csv(output_file)
    print(f"Read back trade log:\n{df3}")

if __name__ == "__main__":
    debug_trade_logging()
