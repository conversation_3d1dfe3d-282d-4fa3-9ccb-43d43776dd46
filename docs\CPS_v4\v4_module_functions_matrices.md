# V4 Function & Module Matrices

## Module-Function Matrix

| Module | File Location | Key Functions/Classes | Brief Purpose |
|--------|---------------|----------------------|---------------|
| **Settings** | `v4/settings/settings_CPS_v4.py` | `load_settings()`, `_parse_config()`, `_parse_complex_numeric()`, `_parse_alpha_list()`, `_convert_value()` | Central parameter loading and type conversion from INI files |
| **Data Loader** | `v4/engine/data_loader_v4.py` | `load_data_for_backtest()`, `validate_data()`, `download_price_data()` | Historical price data loading and validation |
| **Signal Generator** | `v4/engine/signal_generator_v4.py` | `SignalGenerator`, `EMASignalGenerator`, `EqualWeightSignalGenerator`, `generate_signals()` | Abstract signal generation interface and implementations |
| **EMA Model** | `v4/models/ema_allocation_model_v4.py` | `calculate_ema_metrics()`, `calculate_ema_ratios()`, `_extract_value_from_complexn_dict()` | EMA-based allocation calculations and trend analysis |
| **Signal Bridge** | `v4/models/ema_signal_bridge.py` | `run_ema_model_with_tracing()`, `generate_allocation_weights()` | Bridge between signal generation and EMA model |
| **Backtest Engine** | `v4/engine/backtest_v4.py` | `BacktestEngine`, `run_backtest()`, `run_backtest_with_signals()` | Main backtest orchestration and execution |
| **Portfolio** | `v4/engine/portfolio_v4.py` | `Portfolio`, `PortfolioSnapshot`, `add_position()`, `remove_position()`, `get_weights()` | Portfolio state management and position tracking |
| **Execution** | `v4/engine/execution_v4.py` | `ExecutionEngine`, `PercentSlippageModel`, `PercentCommissionModel`, `execute_orders()` | Order execution with slippage and commission models |
| **Orders** | `v4/engine/orders_v4.py` | `Order`, `Trade`, `TradeLog`, `create_buy_order()`, `create_sell_order()` | Trade order creation and logging |
| **Allocation** | `v4/engine/allocation_v4.py` | `calculate_rebalance_orders()`, `normalize_weights()`, `validate_allocation()` | Portfolio rebalancing and allocation calculations |
| **Benchmark** | `v4/engine/benchmark_v4.py` | `calculate_benchmark_returns()`, `create_benchmark_portfolio()` | Benchmark calculation and comparison |
| **Results Calculator** | `v4/engine/results_calculator.py` | `calculate_results()`, `calculate_performance_metrics()`, `calculate_drawdowns()` | Performance metrics and result aggregation |
| **Trade Filter** | `v4/utils/trade_filter.py` | `filter_trades()`, `fetch_current_allocation()`, `compute_absolute_diff()`, `is_business_day()` | Trade filtering based on deviation thresholds |
| **Allocation Reporting** | `v4/reporting/allocation_report_v4.py` | `generate_rebalance_report()`, `_write_default_parameters()` | Excel and image allocation reports |
| **Performance Reporting** | `v4_reporting/v4_performance_report.py` | `generate_performance_report()`, `create_performance_tables()` | Comprehensive performance report generation |
| **Smart Logging** | `v4/utils/smart_logging.py` | `create_smart_logger()`, `milestone()`, `rebalance_event()` | Advanced logging with date-based filtering |
| **Tracing Utils** | `v4/utils/tracing_utils.py` | `setup_trace_directory()`, `save_df_to_trace_dir()`, `reset_trace_directory_for_run()` | Development tracing and debugging output |
| **Date Utils** | `v4/utils/date_utils_v4.py` | `parse_date()`, `map_rebalance_frequency()`, `validate_date_range()` | Date handling and frequency conversion |
| **Configuration** | `v4/config/paths_v4.py` | Path constants and directory definitions | System path configuration |
| **Allocation Rules** | `v4/config/allocation_rules_v4.py` | `get_allocation_weights()`, `apply_weight_constraints()` | Allocation rule enforcement |
| **GUI Main** | `v4/app/gui/main_v4.py` | `main()`, `create_gui()` | GUI application entry point |
| **GUI Actions** | `v4/app/gui/gui_actions_v4.py` | Button handlers and user interaction logic | GUI event handling |
| **GUI Core** | `v4/app/gui/gui_core_v4.py` | Core GUI components and layouts | GUI structure and components |
| **Main Entry** | `main_v4_production_run.py` | `main()`, `log_message()` | Production pipeline entry point |
| **Unified Pipeline** | `v4/run_unified_pipeline.py` | `setup_logger()`, `modify_run_trading_to_accept_dataframe()` | Integrated signal+trading pipeline |
| **Signal Phase** | `v4/Algo_signal_phase.py` | `run_signal_phase()`, Signal generation standalone module | Signal generation phase runner |
| **Trading Phase** | `v4/run_trading_phase.py` | `run_trading_phase()`, `validate_signals()` | Trading phase with pre-computed signals |

## Function-Parameter Matrix

| Function/Class | Key Parameters | Default/Origin | Purpose |
|----------------|----------------|----------------|---------|
| `load_settings()` | `custom_file` | `None` (uses default INI) | Override settings file path |
| `load_data_for_backtest()` | `current_settings` | From CPS_v4 | Settings dict for data loading |
| | `tickers` | `Group1ETFBase` from Lists | Asset symbols to load |
| | `start_date` | `2020-01-01` from data_params | Data start date |
| | `end_date` | `2025-07-21` from data_params | Data end date |
| `BacktestEngine.__init__()` | `config` | `None` | Configuration object (unused) |
| | `initial_capital` | `1000000` from backtest | Starting portfolio value |
| | `commission_rate` | `0.001` from backtest | Trading commission rate |
| | `slippage_rate` | `0.0005` from backtest | Price slippage rate |
| `run_backtest()` | `price_data` | Required DataFrame | Historical price data |
| | `signal_generator` | Required object | Signal generation function |
| | `rebalance_freq` | `monthly` from backtest | Rebalancing frequency |
| | `execution_delay` | `1` from strategy | Days delay for execution |
| `EMASignalGenerator.generate_signals()` | `st_lookback` | `15` from strategy | Short-term EMA period |
| | `mt_lookback` | `70` from strategy | Medium-term EMA period |
| | `lt_lookback` | `100` from strategy | Long-term EMA period |
| | `system_top_n` | `2` from system | Top N assets to select |
| `calculate_ema_metrics()` | `price_data` | Required DataFrame | Price data for EMA calculation |
| | `st_lookback` | `15` from ema_model/strategy | Module-level parameter |
| | `mt_lookback` | `70` from ema_model/strategy | Module-level parameter |
| | `lt_lookback` | `100` from ema_model/strategy | Module-level parameter |
| `Portfolio.__init__()` | `initial_capital` | `1000000` from backtest | Starting cash balance |
| `ExecutionEngine.__init__()` | `commission_model` | `PercentCommissionModel()` | Commission calculation model |
| | `slippage_model` | `PercentSlippageModel()` | Slippage calculation model |
| `PercentCommissionModel.__init__()` | `commission_rate` | `0.001` from backtest/strategy | Module-level rate |
| `PercentSlippageModel.__init__()` | `slippage_rate` | `0.0005` from backtest/strategy | Module-level rate |
| `filter_trades()` | `current_allocation` | Required Series | Current portfolio weights |
| | `proposed_allocation` | Required Series | Target portfolio weights |
| | `deviation_threshold` | `0.02` from backtest | Rebalancing trigger threshold |
| | `trade_date` | Optional date | Date for business calendar check |
| `generate_rebalance_report()` | `signal_df` | Required DataFrame | Signal history data |
| | `allocation_df` | Required DataFrame | Allocation history data |
| | `out_dir` | Required Path | Output directory |
| | `freq` | Required string | Rebalancing frequency |
| | `strategy_name` | Required string | Strategy identifier |
| `create_smart_logger()` | `__name__` | Required string | Logger name |
| | `log_level` | `INFO` from system | Logging level |
| | `recent_days` | `7` (hardcoded) | Days for detailed logging |
| `setup_trace_directory()` | `sub_dir_prefix` | `""` | Directory prefix for traces |
| | `base_dir` | `v4_trace_outputs` | Base tracing directory |
| `generate_performance_report()` | `backtest_results` | Required dict | Backtest results data |
| | `settings` | Required dict | Configuration settings |
| | `strategy_name` | Required string | Strategy name for report |
| | `create_excel` | `false` from Report | Whether to generate Excel |
| | `export_simple_validation_files` | `true` from Report | Whether to export CSV validation |
| `run_unified_pipeline()` | `integration_flag` | `True` from system | Use unified vs decoupled mode |
| | `retain_csv_signal_output` | `True` from system | Save signal CSV files |
| `main()` (main_v4_production_run) | `strategy_name` | `EMA_Crossover` from strategy | Strategy to execute |
| | `log_level` | `INFO` from system | Console logging level |

## Parameter Classification by Type

### SimpleA (Alphanumeric)
- `risk_free_ticker`: `^IRX`
- `start_date`: `2020-01-01`
- `strategy_name`: `EMA_Crossover`
- `log_level`: `INFO`

### SimpleN (Numeric)
- `initial_capital`: `1000000`
- `commission_rate`: `0.001`
- `slippage_rate`: `0.0005`
- `deviation_threshold`: `0.02`

### ComplexN (Optimizable Numeric)
- `st_lookback`: `(optimize=False, default_value=15, min_value=5, max_value=30, increment=1)`
- `mt_lookback`: `(optimize=False, default_value=70, min_value=30, max_value=100, increment=5)`
- `system_lookback`: `(optimize=True, default_value=60, min_value=20, max_value=120, increment=5)`

### AlphaList (List References)
- `tickers`: `(Group1ETFBase, ETFpicklist)`
- `benchmark_ticker`: `(Benchmark_SP500, BenchmarkTickers)`
- `signal_algo`: `(Strategy_EMA, StrategyAlgos)`

## Module Dependencies

```mermaid
graph TD
    A[Settings CPS_v4] --> B[Data Loader]
    A --> C[Signal Generator]
    A --> D[Backtest Engine]
    B --> D
    C --> D
    D --> E[Portfolio]
    D --> F[Execution Engine]
    D --> G[Results Calculator]
    E --> F
    F --> H[Trade Log]
    G --> I[Reporting]
    J[Trade Filter] --> F
    K[Tracing Utils] --> D
    L[Smart Logging] --> D
```

## Key Design Patterns

1. **Central Parameter System**: All parameters sourced from single INI file via `settings_CPS_v4.py`
2. **Module-Level Parameter Loading**: Core modules load parameters at import time
3. **Type-Safe Parameter Extraction**: ComplexN parameters extract `default_value` with validation  
4. **Signal-Trading Decoupling**: Clear handoff via CSV files or in-memory DataFrames
5. **Smart Logging**: Date-aware logging reduces noise for historical processing
6. **Trade Filtering**: Threshold-based rebalancing prevents excessive turnover
7. **Tracing Support**: Development-time detailed output for debugging
8. **Facade Pattern**: Unified pipeline orchestrates signal + trading phases

---
*Generated: $(date)*
*Based on CPS v4 codebase analysis*
