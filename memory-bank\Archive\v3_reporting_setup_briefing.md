# V3 Reporting Setup Briefing

This file provides quick links to essential documents for understanding the status and requirements of the V3 reporting system integration.

## Core Documentation

* [V3 Parameter Flow](../docs/v3_parameter_flow.md) - Path of parameters from GUI → Engine → Reports
* [V3 Module Mapping](../docs/v3_module_mapping.md) - Role of performance_reporter_adapter.py
* [Parameter Classification](../docs/v3_parameter_classification.md) - Reporting parameters status
* [Config Parameter Mapping](../docs/v3_config_parameter_mapping.md) - Matrix of parameter conversions
* [Performance Reporting Standards](../docs/performance_reporting_standards.md) - Required XLSX report format

## Memory Bank References

* full_v3_process_flow_b.md
  [Active Context](./activeContext.md) - Current work focus
* [Progress](./progress.md) - Overall project status
* [Project Brief](./projectbrief.md) - High-level goals
* [Core Tasks](./core_project_tasks_priority.md) - Reporting tasks
* [System Patterns](./systemPatterns.md) - Module architecture
* [Tech Context](./techContext.md) - Technical setup
* [Status Reporting](./Status_Reporting.md) - Detailed analysis
