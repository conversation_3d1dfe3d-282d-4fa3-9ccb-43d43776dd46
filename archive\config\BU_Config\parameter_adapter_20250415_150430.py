"""
Adapter module for parameter optimization functions.
This module imports and re-exports functions from the Custom Function Library,
fixing any path issues.
"""

import sys
from pathlib import Path
from config.paths import CUSTOM_LIB_PATH

# Make sure the Custom Function Library is in the path
if str(CUSTOM_LIB_PATH) not in sys.path:
    sys.path.append(str(CUSTOM_LIB_PATH))

# Import the functions we need
try:
    from config.parameter_optimization import (
        define_parameter,
        validate_parameter,
        get_parameter_range,
        get_parameter_combinations,
        filter_parameter_combinations,
        add_derived_parameters,
        optimize_parameters
    )
    
    # Re-export the functions
    __all__ = [
        'define_parameter',
        'validate_parameter',
        'get_parameter_range',
        'get_parameter_combinations',
        'filter_parameter_combinations',
        'add_derived_parameters',
        'optimize_parameters'
    ]
    
except ImportError as e:
    # Define fallback versions of the functions if needed
    def define_parameter(optimize, default_value, min_value, max_value, increment):
        """Fallback version of define_parameter."""
        optimize_flag = 'Y' if optimize else 'N'
        return (optimize_flag, default_value, min_value, max_value, increment)
    
    def validate_parameter(param, param_name):
        """Fallback version of validate_parameter."""
        return True
    
    # Log the error
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"Error importing parameter optimization functions: {e}")
    logger.info("Using fallback parameter functions")
