"""
Parameter optimization framework for backtesting strategies.
Leverages the Custom Function Library's optimization capabilities.
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
import itertools
from functools import partial

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add Custom Function Library path
from config.paths import *
from config.config import config

# Import optimization functions from Custom Function Library
try:
    # Import from config.parameter_optimization instead of portfolio.advanced_portfolio_optimization
    from config.parameter_optimization import define_parameter, validate_parameter
    from config.parameter_optimization import get_parameter_range, get_parameter_combinations
    from config.parameter_optimization import optimize_parameters
    custom_lib_optimization = True
    logger.info("Successfully imported optimization functions from Custom Function Library")
except ImportError:
    logger.warning("Could not import optimization functions from Custom Function Library")
    custom_lib_optimization = False


class ParameterOptimizer:
    """
    Class for optimizing strategy parameters using various methods.
    """
    
    def __init__(self, backtest_func, evaluation_metric='sharpe_ratio'):
        """
        Initialize parameter optimizer.
        
        Args:
            backtest_func (callable): Function to run backtest with parameters
            evaluation_metric (str): Metric to optimize for
        """
        self.backtest_func = backtest_func
        self.evaluation_metric = evaluation_metric
        self.best_params = None
        self.best_score = -np.inf if 'sharpe' in evaluation_metric else np.inf
        self.results = []
        
    def _evaluate_params(self, params):
        """
        Evaluate a set of parameters using the backtest function.
        
        Args:
            params (dict): Parameters to evaluate
            
        Returns:
            float: Evaluation score
        """
        try:
            # Run backtest with parameters
            backtest_result = self.backtest_func(**params)
            
            # Extract the metric to optimize
            if isinstance(backtest_result, dict) and 'performance' in backtest_result:
                performance = backtest_result['performance']
                
                if isinstance(performance, dict):
                    if self.evaluation_metric in performance:
                        score = performance[self.evaluation_metric]
                    else:
                        logger.warning(f"Evaluation metric '{self.evaluation_metric}' not found in performance metrics")
                        score = -np.inf
                else:
                    # Assume performance is a pandas Series or DataFrame
                    score = performance.get(self.evaluation_metric, -np.inf)
            else:
                # Assume backtest_result is the score itself
                score = backtest_result
                
            # For metrics where lower is better (like drawdown), negate the score
            if any(term in self.evaluation_metric.lower() for term in ['drawdown', 'volatility', 'risk', 'var']):
                score = -score
                
            return score, backtest_result
            
        except Exception as e:
            logger.error(f"Error evaluating parameters: {e}")
            return -np.inf, None
    
    def grid_search(self, param_grid, verbose=True):
        """
        Perform grid search over parameter space.
        Uses the Custom Function Library's grid search if available.
        
        Args:
            param_grid (dict): Dictionary of parameter names and possible values
            verbose (bool): Whether to print progress
            
        Returns:
            dict: Best parameters and results
        """
        if custom_lib_optimization:
            logger.info("Using grid search from Custom Function Library")
            
            # Convert param_grid to parameter tuples format expected by the library
            parameters = {}
            for param_name, values in param_grid.items():
                if len(values) > 0:
                    min_val = min(values)
                    max_val = max(values)
                    # For discrete values, use the difference between consecutive values as increment
                    # or 1 if there's only one value
                    if len(values) > 1:
                        increment = min(abs(values[i] - values[i-1]) for i in range(1, len(values)))
                    else:
                        increment = 1
                    parameters[param_name] = ('Y', values[0], min_val, max_val, increment)
            
            # Define objective function
            def objective(params):
                score, _ = self._evaluate_params(params)
                return score
            
            # Run grid search using the Custom Function Library's optimize_parameters
            best_params, results = optimize_parameters(
                parameters=parameters,
                evaluation_func=objective,
                optimize_flag=True,
                max_combinations=None
            )
            
            # Format results
            formatted_results = []
            for result in results:
                params = result['parameters']
                score = result['score']
                
                # Get the original backtest result
                _, backtest_result = self._evaluate_params(params)
                
                formatted_results.append({
                    'params': params,
                    'score': score,
                    'backtest_result': backtest_result
                })
            
            # Sort results by score in descending order
            formatted_results.sort(key=lambda x: x['score'], reverse=True)
            
            # Update best parameters and score
            self.best_params = best_params
            self.best_score = formatted_results[0]['score'] if formatted_results else -np.inf
            self.results = formatted_results
            
            return {
                'best_params': self.best_params,
                'best_score': self.best_score,
                'results': self.results
            }
            
        else:
            logger.info("Using built-in grid search implementation")
            
            # Generate all parameter combinations
            param_names = sorted(param_grid.keys())
            param_values = [param_grid[name] for name in param_names]
            param_combinations = list(itertools.product(*param_values))
            
            results = []
            
            # Evaluate each parameter combination
            total_combinations = len(param_combinations)
            for i, combination in enumerate(param_combinations):
                if verbose:
                    logger.info(f"Evaluating parameter set {i+1}/{total_combinations}")
                
                # Create parameter dictionary
                params = dict(zip(param_names, combination))
                
                # Evaluate parameters
                score, backtest_result = self._evaluate_params(params)
                
                # Store results
                result = {
                    'params': params,
                    'score': score,
                    'backtest_result': backtest_result
                }
                results.append(result)
                
                # Update best parameters
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params
                    
                    if verbose:
                        logger.info(f"New best score: {score}, params: {params}")
            
            # Sort results by score
            results.sort(key=lambda x: x['score'], reverse=True)
            self.results = results
            
            return {
                'best_params': self.best_params,
                'results': results
            }
    
    def random_search(self, param_distributions, n_iter=10, verbose=True):
        """
        Perform random search over parameter space.
        Uses the Custom Function Library's random search if available.
        
        Args:
            param_distributions (dict): Dictionary of parameter names and distributions
            n_iter (int): Number of iterations
            verbose (bool): Whether to print progress
            
        Returns:
            dict: Best parameters and results
        """
        if custom_lib_optimization:
            logger.info("Using random search from Custom Function Library")
            
            # Convert param_distributions to parameter tuples format expected by the library
            parameters = {}
            for param_name, dist in param_distributions.items():
                if hasattr(dist, 'rvs'):
                    # Sample a few values to estimate min and max
                    samples = [dist.rvs() for _ in range(10)]
                    min_val = min(samples)
                    max_val = max(samples)
                    default_val = dist.mean() if hasattr(dist, 'mean') else samples[0]
                    # Use range / 10 as increment
                    increment = (max_val - min_val) / 10
                    parameters[param_name] = ('Y', default_val, min_val, max_val, increment)
                elif isinstance(dist, list):
                    # Discrete distribution
                    min_val = min(dist)
                    max_val = max(dist)
                    if len(dist) > 1:
                        increment = min(abs(dist[i] - dist[i-1]) for i in range(1, len(dist)))
                    else:
                        increment = 1
                    parameters[param_name] = ('Y', dist[0], min_val, max_val, increment)
            
            # Define objective function
            def objective(params):
                score, _ = self._evaluate_params(params)
                return score
            
            # Run random search using the Custom Function Library's optimize_parameters
            best_params, results = optimize_parameters(
                parameters=parameters,
                evaluation_func=objective,
                optimize_flag=True,
                max_combinations=n_iter
            )
            
            # Format results
            formatted_results = []
            for result in results:
                params = result['parameters']
                score = result['score']
                
                # Get the original backtest result
                _, backtest_result = self._evaluate_params(params)
                
                formatted_results.append({
                    'params': params,
                    'score': score,
                    'backtest_result': backtest_result
                })
            
            # Sort results by score in descending order
            formatted_results.sort(key=lambda x: x['score'], reverse=True)
            
            # Update best parameters and score
            self.best_params = best_params
            self.best_score = formatted_results[0]['score'] if formatted_results else -np.inf
            self.results = formatted_results
            
            return {
                'best_params': self.best_params,
                'best_score': self.best_score,
                'results': self.results
            }
            
        else:
            logger.info("Using built-in random search implementation")
            
            import numpy.random as random
            
            results = []
            
            # Evaluate random parameter combinations
            for i in range(n_iter):
                if verbose:
                    logger.info(f"Evaluating random parameter set {i+1}/{n_iter}")
                
                # Sample parameters from distributions
                params = {}
                for name, dist in param_distributions.items():
                    if isinstance(dist, list):
                        # Discrete choice
                        params[name] = random.choice(dist)
                    elif isinstance(dist, tuple) and len(dist) == 2:
                        # Uniform continuous
                        params[name] = random.uniform(dist[0], dist[1])
                    else:
                        # Assume callable distribution
                        params[name] = dist()
                
                # Evaluate parameters
                score, backtest_result = self._evaluate_params(params)
                
                # Store results
                result = {
                    'params': params,
                    'score': score,
                    'backtest_result': backtest_result
                }
                results.append(result)
                
                # Update best parameters
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params
                    
                    if verbose:
                        logger.info(f"New best score: {score}, params: {params}")
            
            # Sort results by score
            results.sort(key=lambda x: x['score'], reverse=True)
            self.results = results
            
            return {
                'best_params': self.best_params,
                'results': results
            }
    
    def bayesian_optimization(self, param_bounds, n_iter=10, verbose=True):
        """
        Perform Bayesian optimization over parameter space.
        Uses the Custom Function Library's Bayesian optimization if available.
        
        Args:
            param_bounds (dict): Dictionary of parameter names and bounds
            n_iter (int): Number of iterations
            verbose (bool): Whether to print progress
            
        Returns:
            dict: Best parameters and results
        """
        if custom_lib_optimization:
            logger.info("Using Bayesian optimization from Custom Function Library")
            
            # Convert param_bounds to parameter tuples format expected by the library
            parameters = {}
            for param_name, bounds in param_bounds.items():
                min_val, max_val = bounds
                default_val = (min_val + max_val) / 2  # Use midpoint as default
                # Use range / 10 as increment
                increment = (max_val - min_val) / 10
                parameters[param_name] = ('Y', default_val, min_val, max_val, increment)
            
            # Define objective function
            def objective(params):
                score, _ = self._evaluate_params(params)
                return score
            
            # Run Bayesian optimization using the Custom Function Library's optimize_parameters
            best_params, results = optimize_parameters(
                parameters=parameters,
                evaluation_func=objective,
                optimize_flag=True,
                max_combinations=n_iter
            )
            
            # Format results
            formatted_results = []
            for result in results:
                params = result['parameters']
                score = result['score']
                
                # Get the original backtest result
                _, backtest_result = self._evaluate_params(params)
                
                formatted_results.append({
                    'params': params,
                    'score': score,
                    'backtest_result': backtest_result
                })
            
            # Sort results by score in descending order
            formatted_results.sort(key=lambda x: x['score'], reverse=True)
            
            # Update best parameters and score
            self.best_params = best_params
            self.best_score = formatted_results[0]['score'] if formatted_results else -np.inf
            self.results = formatted_results
            
            return {
                'best_params': self.best_params,
                'best_score': self.best_score,
                'results': self.results
            }
            
        else:
            logger.warning("Bayesian optimization requires the Custom Function Library or scikit-optimize")
            logger.info("Falling back to random search")
            
            # Convert bounds to distributions for random search
            param_distributions = {k: (v[0], v[1]) for k, v in param_bounds.items()}
            
            return self.random_search(param_distributions, n_iter, verbose)
    
    def optimize(self, method='grid', params=None, **kwargs):
        """
        Optimize parameters using the specified method.
        
        Args:
            method (str): Optimization method (grid, random, bayesian)
            params (dict): Parameters for the optimization method
            **kwargs: Additional arguments for the optimization method
            
        Returns:
            dict: Best parameters and results
        """
        if method == 'grid':
            return self.grid_search(params, **kwargs)
        elif method == 'random':
            return self.random_search(params, **kwargs)
        elif method == 'bayesian':
            return self.bayesian_optimization(params, **kwargs)
        else:
            raise ValueError(f"Unknown optimization method: {method}")
    
    def get_parameter_importance(self):
        """
        Calculate parameter importance from optimization results.
        
        Returns:
            DataFrame: Parameter importance
        """
        if not self.results:
            return None
            
        try:
            # Extract parameters and scores
            param_names = list(self.results[0]['params'].keys())
            param_values = []
            scores = []
            
            for result in self.results:
                param_values.append([result['params'][name] for name in param_names])
                scores.append(result['score'])
            
            # Convert to DataFrame
            df = pd.DataFrame(param_values, columns=param_names)
            df['score'] = scores
            
            # Calculate correlation between parameters and score
            importance = {}
            for param in param_names:
                if df[param].nunique() > 1:  # Only for parameters with multiple values
                    importance[param] = abs(df[param].corr(df['score']))
            
            # Normalize importance
            total = sum(importance.values())
            if total > 0:
                importance = {k: v / total for k, v in importance.items()}
            
            return pd.Series(importance).sort_values(ascending=False)
            
        except Exception as e:
            logger.error(f"Error calculating parameter importance: {e}")
            return None
    
    def save_results(self, filepath):
        """
        Save optimization results to file.
        
        Args:
            filepath (str): Path to save results
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # Extract results for saving
            results_to_save = []
            for result in self.results:
                # Create a serializable version of the result
                # Extract only the metrics from backtest_result
                if result['backtest_result'] is not None and 'performance' in result['backtest_result']:
                    performance = result['backtest_result']['performance']
                else:
                    performance = {}
                    
                serializable_result = {
                    'params': result['params'],
                    'score': result['score'],
                    'performance': performance
                }
                results_to_save.append(serializable_result)
                
            # Save to CSV using pandas
            results_df = pd.DataFrame([
                {**r['params'], 'score': r['score'], **{f'perf_{k}': v 
                                                      for k, v in r.get('performance', {}).items()}}
                for r in results_to_save
            ])
            results_df.to_csv(filepath, index=False)
            
            logger.info(f"Optimization results saved to {filepath}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving optimization results: {e}")
            return False


def optimize_allocation_parameters(backtest_func, param_grid, evaluation_metric='sharpe_ratio',
                                 optimization_method='grid', n_iter=10, verbose=True):
    """
    Convenience function to optimize allocation parameters.
    
    Args:
        backtest_func (callable): Function to run backtest with parameters
        param_grid (dict): Dictionary of parameter names and possible values
        evaluation_metric (str): Metric to optimize for
        optimization_method (str): Optimization method (grid, random, bayesian)
        n_iter (int): Number of iterations (for random and bayesian)
        verbose (bool): Whether to print progress
        
    Returns:
        dict: Best parameters and results
    """
    optimizer = ParameterOptimizer(backtest_func, evaluation_metric)
    
    if optimization_method == 'grid':
        return optimizer.grid_search(param_grid, verbose)
    elif optimization_method == 'random':
        return optimizer.random_search(param_grid, n_iter, verbose)
    elif optimization_method == 'bayesian':
        # Convert grid to bounds for Bayesian optimization
        param_bounds = {}
        for name, values in param_grid.items():
            if isinstance(values, list):
                if all(isinstance(v, (int, float)) for v in values):
                    param_bounds[name] = (min(values), max(values))
                else:
                    # Categorical parameter, not supported in simple Bayesian optimization
                    logger.warning(f"Parameter {name} has non-numeric values, not suitable for Bayesian optimization")
                    param_bounds[name] = values
        
        return optimizer.bayesian_optimization(param_bounds, n_iter, verbose)
    else:
        raise ValueError(f"Unknown optimization method: {optimization_method}")


def walk_forward_optimization(backtest_func, param_grid, data, 
                            train_size=0.7, n_splits=3, 
                            evaluation_metric='sharpe_ratio',
                            optimization_method='grid', n_iter=10, verbose=True):
    """
    Perform walk-forward optimization to avoid overfitting.
    
    Args:
        backtest_func (callable): Function to run backtest with parameters
        param_grid (dict): Dictionary of parameter names and possible values
        data (dict): Dictionary containing data for different time periods
        train_size (float): Fraction of data to use for training
        n_splits (int): Number of train/test splits
        evaluation_metric (str): Metric to optimize for
        optimization_method (str): Optimization method (grid, random, bayesian)
        n_iter (int): Number of iterations (for random and bayesian)
        verbose (bool): Whether to print progress
        
    Returns:
        dict: Results from walk-forward optimization
    """
    results = []
    
    # Function to split data into train and test sets
    def split_data(data, train_indices, test_indices):
        train_data = {}
        test_data = {}
        
        for key, value in data.items():
            if isinstance(value, pd.DataFrame) or isinstance(value, pd.Series):
                train_data[key] = value.iloc[train_indices]
                test_data[key] = value.iloc[test_indices]
            else:
                # Copy data as is
                train_data[key] = value
                test_data[key] = value
        
        return train_data, test_data
    
    # Get indices for time series split
    if isinstance(data.get('price_data', None), pd.DataFrame):
        ts_data = data['price_data']
    elif isinstance(data.get('returns_data', None), pd.DataFrame):
        ts_data = data['returns_data']
    else:
        logger.error("Could not find price_data or returns_data in data")
        return {'error': 'No time series data found'}
    
    n_samples = len(ts_data)
    indices = np.arange(n_samples)
    
    # Create time series splits
    split_size = n_samples // n_splits
    
    for i in range(n_splits):
        if verbose:
            logger.info(f"Walk-forward split {i+1}/{n_splits}")
            
        # Calculate indices for this split
        start_idx = i * split_size
        train_end_idx = start_idx + int(split_size * train_size)
        test_end_idx = min((i + 1) * split_size, n_samples)
        
        train_indices = indices[start_idx:train_end_idx]
        test_indices = indices[train_end_idx:test_end_idx]
        
        if len(train_indices) == 0 or len(test_indices) == 0:
            continue
        
        # Split data
        train_data, test_data = split_data(data, train_indices, test_indices)
        
        # Create backtest functions for train and test data
        def train_backtest(**params):
            return backtest_func(data=train_data, **params)
        
        def test_backtest(**params):
            return backtest_func(data=test_data, **params)
        
        # Optimize on training data
        optimizer = ParameterOptimizer(train_backtest, evaluation_metric)
        opt_result = optimizer.optimize(
            method=optimization_method, 
            params=param_grid,
            n_iter=n_iter,
            verbose=verbose
        )
        
        # Test best parameters on test data
        best_params = opt_result['best_params']
        test_result = test_backtest(**best_params)
        
        # Store results
        split_result = {
            'split': i,
            'train_indices': train_indices,
            'test_indices': test_indices,
            'best_params': best_params,
            'train_result': opt_result['results'][0]['backtest_result'],
            'test_result': test_result
        }
        results.append(split_result)
    
    # Aggregate results
    aggregated_params = {}
    for param_name in param_grid.keys():
        values = [r['best_params'][param_name] for r in results]
        if all(isinstance(v, (int, float)) for v in values):
            # Numeric parameter - use mean
            aggregated_params[param_name] = sum(values) / len(values)
        else:
            # Non-numeric parameter - use mode
            from collections import Counter
            counter = Counter(values)
            aggregated_params[param_name] = counter.most_common(1)[0][0]
    
    return {
        'split_results': results,
        'aggregated_params': aggregated_params
    }
