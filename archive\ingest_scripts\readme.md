# Documentation Ingestion Script README

## Project Overview
This script automates combining project documentation (Markdown files) from memory-bank and docs directories into a single, timestamped Markdown file for easy management and AI ingestion. It was developed to address memory retention issues in Windsurf sessions by simplifying file handling and integration with AI memory tools.

## Steps to Run
1. Ensure Python is installed in the isolated virtual environment at `F:\AI_Library\my_quant_env`.
2. Run the batch file from the project root or script directory:
   - Command: `ingest_docs.bat`
   - This executes the Python script `ingest_docs.py`.

## Output
- A timestamped Markdown file (e.g., `combined_documentation_YYYYMMDD_HHMMSS.md`) is created in the `combined_docs` directory.
- The file includes a table of contents, all original content, source paths, and tags for quick reference.

## Next Steps
- Test the workflow by running `/ingest_documentation` in Windsurf to automate ingestion and memory update.
- Enhance portability by parameterizing paths in `ingest_docs.py` for use in other projects.
- For the next session, consider migrating this to a function library in `S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library` to make it reusable across projects.

## Making It More Portable
- Parameterize the project root and file paths in `config.ini` to easily adapt to other directories.
- Convert the script into a Python module that can be imported, reducing dependency on batch files.
- Migrate to a shared function library for multi-project use, following Windsurf's modularity rules (e.g., split into utility functions if needed).
