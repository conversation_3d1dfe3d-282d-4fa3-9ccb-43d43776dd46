---
description: Ingest project documentation and update Windsurf AI memory using the new docs/Comb output folder
---

# Workflow: Documentation Ingestion and Memory Update

1. **Run the batch file to combine all documentation into a single markdown file.**
   - Always use the shared library batch file (do not reference a project-local batch file):
     `"S:/Dropbox/Scott Only Internal/Quant_Python_24/Custom Function Library/documentation/ingest_docs/ingest_docs.bat"`
   - Always wrap the path in double quotes if it contains spaces (required for both CMD and PowerShell).
   - The batch file reads its config from `ingest_config.ini` in the same directory.
   - Output will always be placed in:
     `S:/Dropbox/Scott Only Internal/Quant_Python_24/Backtest_FinAsset_Alloc_Template/docs/Comb`
   - If the output directory does not exist, the script will create it.
   - **Troubleshooting:**
     - If you see "command not found" or "not recognized" errors, check:
       - That you are using the correct path format for your shell
       - That you have permission to run batch files
       - That the config file paths are correct and files exist

2. **Update Windsurf AI memory with the combined documentation:**
   - After running the batch file, locate the newest file in `docs/Comb` in the project.
   - Use the following command to update memory:

     ```markdown
     /create_memory --action update --id MEMORY[770ab331-cdd4-431e-9884-3e69a8334b09] --title "Combined Documentation" --content (paste content of latest combined file) --tags "documentation,ai_memory,workflow"
     ```
Tip: Always use the most recent combined file for memory updates.