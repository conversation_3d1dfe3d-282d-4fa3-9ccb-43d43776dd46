@echo off
REM ============================================
REM Script: verify_trade_filter.bat
REM Description: Verify if trade filter is working properly
REM ============================================

SETLOCAL

REM --- Paths ---
SET "PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe"
SET "SCRIPT_DIR=%~dp0"
SET "VERIFY_SCRIPT=%SCRIPT_DIR%verify_trade_filter.py"

echo [%TIME%] Running trade filter verification...
echo ============================================

"%PYTHON_EXE%" "%VERIFY_SCRIPT%"
SET "EXIT_CODE=%ERRORLEVEL%"

echo ============================================
echo [%TIME%] Verification completed with exit code %EXIT_CODE%

IF %EXIT_CODE% EQU 0 (
    echo ✅ TRADE FILTER IS WORKING CORRECTLY
) ELSE (
    echo ❌ TRADE FILTER IS NOT WORKING PROPERLY
    echo Check the output above for details.
)

ENDLOCAL
exit /b %EXIT_CODE%
