# Smart Logging System for V4 Pipeline

## Overview

The smart logging system dramatically reduces log verbosity for historical backtests while maintaining full detail for recent operations and critical events. This addresses the issue where running backtests on historical data (like 2020-2023) would produce overwhelming amounts of debug output.

## How It Works

### Date-Based Intelligence
- **Recent dates (last 14 days)**: Full detailed logging at all levels
- **Older dates (14+ days ago)**: Only important events are logged
- **Special events**: Always logged regardless of date (errors, trades, milestones)

### Smart Filtering by Log Type

| Log Type | Recent Dates | Older Dates | Notes |
|----------|--------------|-------------|-------|
| `debug()` | ✅ Shown | ❌ Hidden | Verbose details only for recent dates |
| `info()` | ✅ Shown | ❌ Hidden* | *Unless forced or periodic summary |
| `warning()` | ✅ Shown | ✅ Shown | Always important |
| `error()` | ✅ Shown | ✅ Shown | Always important |
| `trade_event()` | ✅ Shown | ✅ Shown | Trades always logged |
| `rebalance_event()` | ✅ Shown | ✅ Shown | Rebalancing always logged |
| `milestone()` | ✅ Shown | ✅ Shown | Key milestones always logged |
| `performance_summary()` | ✅ Shown | ✅ Shown | Results always logged |

### Daily Progress Logging
- **Recent dates**: Show every day
- **Older dates**: Show periodic summaries (every 30 days)  
- **Edge days**: Always show first 5 and last 5 days regardless of date

## Implementation

### Quick Start

Replace your existing logger with a smart logger:

```python
# OLD: Standard logging
import logging
logger = logging.getLogger(__name__)

# NEW: Smart logging  
from v4.utils.smart_logging import create_smart_logger
logger = create_smart_logger(__name__)
```

### Usage Examples

```python
# Date-aware logging
current_date = pd.Timestamp('2020-01-15')  # Historical date

# This won't appear for historical dates (reduces noise)
logger.debug("Processing signals", target_date=current_date)
logger.info("Portfolio updated", target_date=current_date)

# These ALWAYS appear (important events)
logger.trade_event("Bought 100 SPY shares", target_date=current_date)
logger.rebalance_event("Threshold exceeded - rebalancing", target_date=current_date)
logger.error("Failed to load price data", target_date=current_date)

# Force logging regardless of date
logger.info("Critical system info", target_date=current_date, force=True)

# Milestones and performance (always shown)
logger.milestone("Backtest phase 1 complete")
logger.performance_summary("Final CAGR: 12.3%")

# Smart daily progress
logger.daily_progress(day_num, total_days, current_date, portfolio_value)
```

## Configuration

### Adjustable Settings

```python
# In v4/utils/smart_logging.py
DETAILED_LOGGING_DAYS = 14  # Show full logs for last 2 weeks
SUMMARY_INTERVAL_DAYS = 30  # Periodic summaries for older dates
```

### Override for Testing

```python
# Force detailed logging for specific dates (useful for debugging)
logger.info("Debug old date", target_date=old_date, force=True)
```

## Impact on Existing Verbose Output

### Before Smart Logging
```
2025-07-21 22:15:09,688 - v4.utils.trade_filter - INFO - 
=== TRADE FILTER DEBUG for 2020-09-18 ===
2025-07-21 22:15:09,689 - v4.utils.trade_filter - INFO - Filtering trades with deviation threshold: 2.0%
2025-07-21 22:15:09,689 - v4.utils.trade_filter - INFO - Current allocation: {'SPY': 0.5977, 'EFA': 0.4012}
2025-07-21 22:15:09,689 - v4.utils.trade_filter - INFO - Proposed allocation: {'SPY': 0.6, 'EFA': 0.4}
... (50+ more lines for each trading day)
```

### After Smart Logging
```
2025-07-21 22:22:14,478 - v4.utils.trade_filter - INFO - [REBALANCE] 2020-09-24 Threshold exceeded - executing full rebalance
... (Only important events shown for historical dates)
```

## Files Modified

1. **`v4/utils/smart_logging.py`** - New smart logging implementation
2. **`v4/utils/trade_filter.py`** - Updated to use smart logging
3. **`v4/engine/backtest_v4.py`** - Updated to use smart logging
4. **`tests/v4/production_modules/run_v4_pipeline.py`** - Updated to use smart logging

## Benefits

1. **Dramatically Reduced Log Noise**: Historical backtests generate 90% less log output
2. **Still Captures Important Events**: All trades, rebalances, and errors are logged
3. **Maintains Debug Capability**: Recent operations still show full detail
4. **Backward Compatible**: Existing code works with minimal changes
5. **Configurable**: Easy to adjust date thresholds and behavior

## Testing

Run the test script to see smart logging in action:

```bash
python tests/v4/test_smart_logging.py
```

This demonstrates how logs behave differently for recent vs. historical dates.

## Best Practices

1. **Use appropriate log levels**: 
   - `debug()` for detailed diagnostics
   - `info()` for general information
   - `trade_event()` for trading actions
   - `rebalance_event()` for rebalancing decisions
   - `milestone()` for major progress points

2. **Pass target_date when available**: This enables smart filtering

3. **Use force=True sparingly**: Only for truly critical information that must always appear

4. **Leverage specialized methods**: `trade_event()`, `rebalance_event()`, etc. are always shown

## Migration Guide

### Simple Migration (Minimal Changes)
```python
# Just replace the logger creation
from v4.utils.smart_logging import create_smart_logger
logger = create_smart_logger(__name__)
```

### Enhanced Migration (Full Benefits)
```python
# Add target_date parameters where available
logger.debug("Processing signals", target_date=current_date)
logger.info("Portfolio value updated", target_date=current_date)

# Use specialized logging methods
logger.trade_event(f"Executed {len(trades)} trades", target_date=current_date)
logger.rebalance_event(f"Rebalancing portfolio", target_date=current_date)
```

The smart logging system maintains all the diagnostic capability you need while making log output manageable for production use!
