"""
Backtesting engine for financial asset allocation strategies.
This module serves as a wrapper around the Custom Function Library's backtesting framework.
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from utils.date_utils import (
    standardize_date,
    standardize_date_range,
    standardize_dataframe_index,
    filter_dataframe_by_dates,
    display_date
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import local modules
from data.data_loader import load_data_for_backtest
from models.allocation_model import get_allocation_model

# Import the necessary functions from the Custom Function Library
from backtesting.backtest_framework import backtest_strategy, calculate_trades
from backtesting.backtest_performance import calculate_performance
from reporting.performance_reporting import create_performance_tables
from utils.trade_log import TradeLogger


def run_backtest(config):
    """
    Run backtest with the given configuration.
    Uses the Custom Function Library's backtest_strategy function.
    
    Args:
        config (dict): Configuration dictionary
        
    Returns:
        dict: Backtest results
    """
    logger.info("Starting backtest")
    
    # Load data
    data = load_data_for_backtest(config)
    price_data = data['price_data']
    returns_data = data['returns_data']
    
    # Get allocation model
    strategy_name = config.get('allocation_strategy', 'equal_weight')
    strategy_func = get_allocation_model(strategy_name)
    
    # Get backtest parameters
    backtest_params = config['backtest_params']
    allocation_params = config['allocation_params']
    
    # Set up rebalancing frequency
    rebalance_freq = backtest_params['rebalance_freq']
    
    # Run backtest using the Custom Function Library
    logger.info(f"Running backtest with {strategy_name} strategy and {rebalance_freq} rebalancing")
    strategy_returns, weights_history, signal_history = backtest_strategy(
        price_data=price_data,
        strategy_func=strategy_func,
        strategy_params=allocation_params,
        rebalance_freq=rebalance_freq,
        execution_delay=backtest_params['execution_delay'],
        start_date=standardize_date(config['data_params']['start_date']),
        end_date=standardize_date(config['data_params']['end_date'])
    )
    
    # Calculate portfolio value
    initial_capital = backtest_params['initial_capital']
    portfolio_value = initial_capital * (1 + strategy_returns).cumprod()
    
    # Calculate trades
    trades = calculate_trades(
        weights_df=weights_history, 
        price_data=price_data,
        trade_threshold=0.001
    )
    
    # Calculate performance metrics using the Custom Function Library
    performance_metrics = calculate_performance(strategy_returns)
    
    # Create trade log
    trade_logger = TradeLogger()
    trades_df = trade_logger.create_trade_log_from_trades(
        trades=trades,
        price_data=price_data,
        initial_capital=initial_capital,
        transaction_cost=backtest_params['transaction_cost']
    )
    
    results = {
        'portfolio_value': portfolio_value,
        'portfolio_returns': strategy_returns,
        'weights_history': weights_history,
        'signal_history': signal_history,
        'trades': trades_df,
        'performance_metrics': performance_metrics
    }
    
    # Save results if configured
    reporting_params = config['reporting_params']
    if reporting_params.get('save_trade_log', False) and 'trades' in results:
        output_dir = reporting_params['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = display_date(date.today())
        
        # Save the detailed trade log with formatting to Excel
        strategy_name = config.get('allocation_strategy', 'strategy')
        trade_logger.save_detailed_trade_log(
            trade_log=results['trades'],
            output_dir=output_dir,
            filename_prefix=strategy_name
        )
        
        # Also save CSV version for backwards compatibility
        trade_log_file = os.path.join(output_dir, f"trade_log_{timestamp}.csv")
        results['trades'].to_csv(trade_log_file, index=False)
        logger.info(f"Simple trade log saved to {trade_log_file}")
    
    # Save performance report if configured
    if reporting_params.get('save_performance_metrics', False):
        # Convert performance metrics to DataFrame for reporting
        perf_df = pd.DataFrame([performance_metrics])
        
        # Use the Custom Function Library's performance reporting
        output_dir = reporting_params['output_dir']
        os.makedirs(output_dir, exist_ok=True)
        
        timestamp = display_date(date.today())
        report_filename = f"performance_report_{timestamp}.xlsx"
        
        # Create performance tables using the Custom Function Library
        create_performance_tables(
            performance_data=perf_df,
            weights_history=weights_history,
            signal_history=signal_history,
            output_dir=output_dir,
            filename=report_filename
        )
        
        logger.info(f"Performance report saved to {os.path.join(output_dir, report_filename)}")
    
    return results
