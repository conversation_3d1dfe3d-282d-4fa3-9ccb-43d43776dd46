# Warp Aliases Setup - Fast AI Instructions

# Add these to your PowerShell profile for instant "/command" style access

# SESSION START - Type: start-session
function start-session {
    Write-Host "START SESSION" -ForegroundColor Green
    Write-Host ""
    Write-Host "Please read these files in order to understand current context:"
    Write-Host "1. memory-bank/current_session_context.md (current priorities & status)"
    Write-Host "2. memory-bank/execution_reference.md (exact commands & locations)"
    Write-Host "3. memory-bank/codebase_map.md (existing functions to avoid duplication)"
    Write-Host ""
    Write-Host "Then provide:"
    Write-Host "- Summary of current phase and progress %"
    Write-Host "- Top 3 priority tasks for this session"
    Write-Host "- Any critical blockers from previous session" 
    Write-Host "- Recommended first action"
    Write-Host ""
    Write-Host "Be concise and action-oriented." -ForegroundColor Yellow
}

# MID UPDATE - Type: mid-update
function mid-update {
    Write-Host "MID UPDATE" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Update memory-bank/current_session_context.md with current progress:"
    Write-Host "- <PERSON> completed tasks with ✅"
    Write-Host "- Add any new issues to BLOCKERS section"
    Write-Host "- Add discoveries to LEARNED THIS SESSION"
    Write-Host "- Update NEXT SESSION GOALS if priorities changed"
    Write-Host ""
    Write-Host "Be specific about what was accomplished and what was learned." -ForegroundColor Yellow
}

# END SESSION - Type: end-session
function end-session {
    Write-Host "END SESSION" -ForegroundColor Red
    Write-Host ""
    Write-Host "Perform these documentation actions:"
    Write-Host "1. Create memory-bank/session_updates/[today's date]_completed.md with accomplishments"
    Write-Host "2. Update memory-bank/current_session_context.md - remove completed, add new priorities"
    Write-Host "3. Update memory-bank/codebase_map.md if new functions/modules were discovered"
    Write-Host "4. Set 3 clear, specific goals for next session"
    Write-Host ""
    Write-Host "Focus on concrete outcomes and next steps." -ForegroundColor Yellow
}

# QUICK STATUS - Type: quick-status
function quick-status {
    Write-Host "QUICK STATUS" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Read memory-bank/current_session_context.md and provide:"
    Write-Host "- Current phase and % complete"
    Write-Host "- Top priority task right now"
    Write-Host "- Any critical blockers"
    Write-Host "- Recommended immediate next action (be specific)"
    Write-Host ""
    Write-Host "Keep response under 100 words." -ForegroundColor Yellow
}

# FUNCTION CHECK - Type: function-check
function function-check {
    Write-Host "FUNCTION CHECK" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "Before creating any new function, check memory-bank/codebase_map.md for existing implementations."
    Write-Host ""
    Write-Host "Report:"
    Write-Host "- Does this function already exist?"
    Write-Host "- What similar functionality is available?"
    Write-Host "- Where should new function be placed if needed?"
    Write-Host "- Any naming conflicts to avoid?"
    Write-Host ""
    Write-Host "Prevent code duplication." -ForegroundColor Yellow
}

Write-Host "AI Instruction aliases loaded! Available commands:" -ForegroundColor Green
Write-Host "- start-session"
Write-Host "- mid-update" 
Write-Host "- end-session"
Write-Host "- quick-status"
Write-Host "- function-check"
