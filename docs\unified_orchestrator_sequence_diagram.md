# Unified Orchestrator Design - Sequence Diagram

## Overview
This document outlines the high-level sequence diagram for the Unified Orchestrator that integrates the Signal Generation and Trading phases into a single, streamlined process while maintaining the option to run in legacy (decoupled) mode.

## Sequence Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Main Entry    │    │   Orchestrator  │    │  Signal Phase   │    │  Trading Phase  │
│     Point       │    │                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │1. Start │                  │                       │                       │
    │ Process │                  │                       │                       │
    └────┬────┘                  │                       │                       │
         │                       │                       │                       │
         │──────────────────────▶│                       │                       │
         │   Initialize()        │                       │                       │
         │                       │                       │                       │
         │                  ┌────┴────┐                  │                       │
         │                  │2. Setup │                  │                       │
         │                  │& Config │                  │                       │
         │                  │  Load   │                  │                       │
         │                  └────┬────┘                  │                       │
         │                       │                       │                       │
         │                       │── load_settings() ────┤                       │
         │                       │                       │                       │
         │                       │── setup_logging() ────┤                       │
         │                       │                       │                       │
         │                       │── validate_paths() ───┤                       │
         │                       │                       │                       │
         │                       │── check_integration_flag()                    │
         │                       │                       │                       │
         │                  ┌────┴────┐                  │                       │
         │                  │3. Signal│                  │                       │
         │                  │  Phase  │                  │                       │
         │                  │  Call   │                  │                       │
         │                  └────┬────┘                  │                       │
         │                       │                       │                       │
         │                       │──────────────────────▶│                       │
         │                       │   run_signal_phase()  │                       │
         │                       │                       │                       │
         │                       │                       │── load_data() ────────┤
         │                       │                       │                       │
         │                       │                       │── generate_signals()──┤
         │                       │                       │                       │
         │                       │                       │── [Conditional CSV]──┤
         │                       │                       │   save_to_csv()       │
         │                       │                  ┌────┴────┐                  │
         │                       │                  │4. Return│                  │
         │                       │                  │DataFrame│                  │
         │                       │                  │Objects  │                  │
         │                       │                  └────┬────┘                  │
         │                       │◄──────────────────────┤                       │
         │                       │   signals_df,         │                       │
         │                       │   price_data_df       │                       │
         │                       │                       │                       │
         │                  ┌────┴────┐                  │                       │
         │                  │5. In-   │                  │                       │
         │                  │ Memory  │                  │                       │
         │                  │ Handoff │                  │                       │
         │                  └────┬────┘                  │                       │
         │                       │                       │                       │
         │                       │── validate_signals()──┤                       │
         │                       │                       │                       │
         │                       │── prepare_trading_inputs()                     │
         │                       │                       │                       │
         │                  ┌────┴────┐                  │                       │
         │                  │6. Trade │                  │                       │
         │                  │  Phase  │                  │                       │
         │                  │  Call   │                  │                       │
         │                  └────┬────┘                  │                       │
         │                       │                       │                       │
         │                       │──────────────────────────────────────────────▶│
         │                       │   run_trading_phase(                          │
         │                       │     signals_df=signals_df,                   │
         │                       │     price_data=price_data_df)                │
         │                       │                       │                       │
         │                       │                       │                       │── execute_trades()
         │                       │                       │                       │
         │                       │                       │                       │── calculate_performance()
         │                       │                       │                       │
         │                       │                       │                  ┌────┴────┐
         │                       │                       │                  │7. Return│
         │                       │                       │                  │Results  │
         │                       │                       │                  └────┬────┘
         │                       │◄──────────────────────────────────────────────┤
         │                       │   backtest_results                            │
         │                       │                       │                       │
         │                  ┌────┴────┐                  │                       │
         │                  │8. Final │                  │                       │
         │                  │Reporting│                  │                       │
         │                  │& Cleanup│                  │                       │
         │                  └────┬────┘                  │                       │
         │                       │                       │                       │
         │                       │── generate_reports()──┤                       │
         │                       │                       │                       │
         │                       │── consolidate_logs()──┤                       │
         │                       │                       │                       │
         │                       │── cleanup_temp_files()│                       │
         │                       │                       │                       │
         │◄──────────────────────┤                       │                       │
         │   Complete Results    │                       │                       │
         │                       │                       │                       │
    ┌────┴────┐                  │                       │                       │
    │9. Exit  │                  │                       │                       │
    │ Process │                  │                       │                       │
    └─────────┘                  │                       │                       │
```

## Key Features

### 1. Environment Setup & Config Load
- **Settings Loading**: Load parameters from `settings_CPS_v4.py`
- **Integration Flag Check**: Determine if running in unified or legacy mode
- **Path Validation**: Ensure all required directories exist
- **Logging Setup**: Initialize centralized logging system

### 2. Signal Phase Call
- **Data Loading**: Load market data for analysis
- **Signal Generation**: Run the configured signal generation algorithm
- **Conditional CSV Output**: Save CSV files only if `retain_csv_signal_output = True`
- **Return DataFrames**: Pass signals and price data as in-memory objects

### 3. In-Memory Handoff
- **DataFrame Validation**: Ensure signal data integrity
- **Memory Efficiency**: No disk I/O between phases (unless explicitly configured)
- **Data Preparation**: Format data for trading phase consumption

### 4. Trading Phase Call
- **Direct Memory Input**: Consume signals and price data from memory
- **Trade Execution**: Simulate trades based on signals
- **Performance Calculation**: Generate comprehensive performance metrics

### 5. Consolidated Shutdown & Final Logging
- **Report Generation**: Create unified performance reports
- **Log Consolidation**: Merge logs from all phases
- **Cleanup**: Remove temporary files and release resources

## Configuration Flags

### Integration Flag
- **Purpose**: Toggle between unified and legacy (decoupled) execution modes
- **Location**: `v4/settings/settings_parameters_v4.ini` in `[System]` section
- **Parameter**: `integration_flag`
- **Values**: `True` (unified) / `False` (legacy)
- **Default**: `False` (maintains backward compatibility)

### CSV Retention Flag  
- **Purpose**: Control whether intermediate CSV files are saved during unified runs
- **Location**: `v4/settings/settings_parameters_v4.ini` in `[System]` section
- **Parameter**: `retain_csv_signal_output`
- **Values**: `True` (save CSV) / `False` (memory only)
- **Default**: `False` (optimizes for memory efficiency)
- **Note**: Only applies when `integration_flag = True`

### Configuration Examples

```ini
[System]
# Legacy mode (current behavior)
integration_flag = False
retain_csv_signal_output = False

# Unified mode with memory-only handoff (optimal performance)
integration_flag = True
retain_csv_signal_output = False

# Unified mode with CSV retention (debugging/validation)
integration_flag = True
retain_csv_signal_output = True
```

## Benefits

1. **Performance**: Eliminates disk I/O overhead between phases
2. **Simplicity**: Single execution path for most use cases
3. **Flexibility**: Maintains legacy mode for debugging and validation
4. **Memory Efficiency**: Direct DataFrame passing reduces memory overhead
5. **Debugging**: Optional CSV retention for troubleshooting

## Implementation Notes

- The unified orchestrator will be implemented as a new module: `v4/unified_orchestrator.py`
- Legacy pipeline (`v4/run_v4_pipeline.py`) remains unchanged for backward compatibility
- Main entry point will check `integration_flag` to determine which orchestrator to use
- All existing phase modules remain functional and unchanged
