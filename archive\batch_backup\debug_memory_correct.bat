@echo off
REM Activate virtual environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Set correct module path
set MODULE_PATH=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\memory_tool

REM Verify module exists
dir "%MODULE_PATH%\__init__.py" || (
    echo ERROR: Could not find memory_tool at:
    echo %MODULE_PATH%
    goto end
)

REM Test import
python -c "import sys; sys.path.insert(0, r'S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs'); import memory_tool; print('SUCCESS: memory_tool found at:', memory_tool.__file__)"

:end
pause
