My Action Plan
Create master_config.bat with the content shown above.
Read config/paths.py to understand its current state.
Propose the new, simplified content for config/paths.py.
Modify run_trading_phase_standalone.bat to use the new master config.


So, I think we need to have the virtual envir reactivated to be safe in every bat file run, for example in this one run_trading_phase_standalone.bat I might be wrong - but it does not seem to always stay activated as different tasks take place?

So the running the @master_config.bat file once at start is fine necessary but not sufficient also then need something that passes / used in every individual bat file setup?