# Combined Project Documentation

Generated: 2025-06-02 15:01:21

## Table of Contents

1. [v3_module+functions_list_AI](#v3_module+functions_list_ai)
2. [reporting_system_AI](#reporting_system_ai)
3. [systemFiles+Flow_AI](#systemfiles+flow_ai)
4. [parameter_management_AI](#parameter_management_ai)
5. [v3_module+functions_list](#v3_module+functions_list)
6. [v3_performance_reporting_standards_a](#v3_performance_reporting_standards_a)
7. [v3_parameter_system_reference](#v3_parameter_system_reference)

---

## v3_module+functions_list_AI

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\memory-bank\v3_module+functions_list_AI.md`*

*Tags: documentation,module_reference*

# V3 Module + Functions Reference (AI-Optimized)

<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 Module Function Reference
Tags: [module_reference, function_listing, v3_system]
-->

> **Last Updated**: 2025-05-14
> **Purpose**: Comprehensive reference of all V3 modules and their functions
> **Related Files**: 
> - [System Files + Flow](systemFiles+Flow_AI.md)
> - [Parameter System Reference](v3_parameter_system_reference.md)

## 🔍 Quick Module Lookup

| Component | Key Modules | Status |
|-----------|-------------|:------:|
| **Parameter System** | `v3_engine/parameter_registry.py`, `v3_engine/parameters.py` | ✅ |
| **GUI** | `app/gui/v3_gui_core.py`, `app/gui/v3_parameter_widgets.py` | ✅ |
| **Strategy** | `v3_engine/ema_v3_adapter.py` | ✅ |
| **Reporting** | `v3_reporting/v3_performance_report.py`, `v3_reporting/v3_allocation_report.py` | ✅ |

## 🧩 Core Engine Modules

| Module | Key Functions | Status | Description |
|--------|---------------|:------:|-------------|
| `v3_engine/parameter_registry.py` | `register_parameter()`, `get_parameter()` | ✅ | Central parameter registry |
| `v3_engine/parameters.py` | `NumericParameter.__init__()`, `CategoricalParameter.validate()` | ✅ | Parameter class definitions |
| `v3_engine/strategy_parameter_set.py` | `register_parameters()`, `get_parameter_values()` | ✅ | Strategy parameter container |
| `v3_engine/parameter_optimizer.py` | `get_optimization_combinations()`, `optimize_parameters()` | ✅ | Parameter optimization |
| `v3_engine/ema_v3_adapter.py` | `generate_signal()`, `calculate_ema_metrics()` | ✅ | EMA strategy adapter |
| `v3_engine/performance_reporter_adapter.py` | `adapt_parameters()`, `convert_metrics()` | ✅ | Reporting adapter |
| `v3_engine/gui_parameter_manager.py` | `create_parameter_widgets()`, `update_parameters()` | ✅ | GUI parameter management |
| `v3_engine/data_validator.py` | `validate_signal_allocation()`, `validate_price_data()` | ⚠️ | Data validation utilities |

## 📊 Reporting Modules

| Module | Key Functions | Status | Description |
|--------|---------------|:------:|-------------|
| `v3_reporting/v3_performance_report.py` | `generate_v3_performance_report()` | ✅ | Performance report generation |
| `v3_reporting/v3_allocation_report.py` | `generate_v3_allocation_report()` | ✅ | Allocation report generation |
| `v3_reporting/v3_visualization.py` | `create_performance_chart()`, `create_allocation_chart()` | ✅ | Chart generation |
| `v3_reporting/v3_performance_charts.py` | `create_return_chart()`, `create_drawdown_chart()` | ✅ | Performance chart generation |
| `v3_reporting/v3_trade_log.py` | `format_trade_log()`, `summarize_trades()` | ✅ | Trade log formatting |

## 🖥️ GUI Modules

| Module | Key Functions | Status | Description |
|--------|---------------|:------:|-------------|
| `app/gui/v3_gui_core.py` | `MainWindowV3.__init__()`, `_run_backtest()` | ✅ | Main GUI window |
| `app/gui/v3_parameter_widgets.py` | `create_parameter_widget()`, `update_from_widget()` | ✅ | Parameter widgets |
| `app/gui/v3_gui_actions.py` | `run_backtest_action()`, `save_config_action()` | ✅ | GUI action handlers |
| `app/gui/v3_register_parameters.py` | `register_core_parameters()`, `register_ema_parameters()` | ✅ | Parameter registration |

## 📈 Strategy Modules

| Module | Key Functions | Status | Description |
|--------|---------------|:------:|-------------|
| `v3_strategies/ema_strategy.py` | `calculate_signal()`, `get_weights()` | ✅ | EMA strategy implementation |
| `v3_strategies/strategy_base.py` | `Strategy.__init__()`, `generate_signal()` | ✅ | Base strategy class |
| `v3_strategies/strategy_factory.py` | `create_strategy()`, `get_available_strategies()` | 🔄 | Strategy factory (planned) |
| `v3_strategies/strategy_validator.py` | `validate_strategy_config()`, `check_required_parameters()` | 🔄 | Strategy validation (planned) |

## 🔄 V2 to V3 Module Mapping

| V2 Module | V3 Module | Status | Key Changes |
|-----------|-----------|:------:|-------------|
| `config/config_v2.py` | `v3_engine/parameter_registry.py` | ✅ | Parameter tuples → type-safe objects |
| `config/parameter_optimization.py` | `v3_engine/parameter_optimizer.py` | ✅ | Enhanced categorical parameter support |
| `models/ema_allocation_model.py` | `v3_engine/ema_v3_adapter.py` | ✅ | Adapter pattern for V3 parameters |
| `performance/performance_reporting.py` | `v3_engine/performance_reporter_adapter.py` | ✅ | Adapter pattern for reporting |
| `config_interface.py` | `v3_engine/gui_parameter_manager.py` | ✅ | Enhanced GUI integration |

## 📊 Data Quality Modules

| V3 Module | Legacy Equivalent | Key Functions | Status | Key Changes |
|-----------|-------------------|---------------|:------:|-------------|
| `v3_engine/data_validator.py` | `data/quality_check.py` | `validate_data()`, `check_missing()` | ⚠️ | Basic validation rules implemented |
| `v3_engine/data_cleaner.py` | N/A | `clean_price_data()`, `handle_missing_values()` | 🔄 | Planned data cleaning utilities |
| `v3_engine/data_metrics.py` | N/A | `calculate_data_metrics()`, `report_data_quality()` | 🔄 | Planned data quality metrics |

## ⚠️ Error Handling Modules

| V3 Module | Legacy Equivalent | Key Functions | Status | Description |
|-----------|-------------------|---------------|:------:|-------------|
| `v3_engine/error_handler.py` | N/A | `handle_error()`, `log_error()` | 🔄 | Centralized error handling |
| `v3_engine/validation_errors.py` | N/A | `ValidationError`, `ParameterError` | 🔄 | Error class definitions |
| `v3_engine/error_reporter.py` | N/A | `report_errors()`, `format_error_message()` | 🔄 | User-friendly error reporting |

## 🔄 Strategy Loading System

```mermaid
flowchart LR
    StrategyFactory[v3_strategies/strategy_factory.py] -->|Create| Strategy[v3_strategies/strategy_base.py]
    Strategy -->|Register Parameters| ParameterRegistry[v3_engine/parameter_registry.py]
    ParameterRegistry -->|Parameter Values| Strategy
    Strategy -->|Validate Config| StrategyValidator[v3_strategies/strategy_validator.py]
    
    classDef factory fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef validator fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class StrategyFactory factory;
    class Strategy strategy;
    class ParameterRegistry param;
    class StrategyValidator validator;
```

## 🔄 Component Interactions

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data/data_loader.py] -->|OHLCV Data| EMAAdapter[v3_engine/ema_v3_adapter.py]
    DataLoader -->|Benchmark Data| BacktestEngine[engine/backtest.py]

    %% Parameter Flow
    ParameterRegistry[v3_engine/parameter_registry.py] -->|Parameters| EMAAdapter
    ParameterRegistry -->|Parameters| BacktestEngine
    
    %% Execution Flow
    EMAAdapter -->|Signals| BacktestEngine
    
    %% Reporting Flow
    BacktestEngine -->|Results| PerformanceReporter[v3_reporting/v3_performance_report.py]
    BacktestEngine -->|Allocations| AllocationReporter[v3_reporting/v3_allocation_report.py]
    
    %% Styling
    classDef data fill:#F5F5F5,stroke:#616161,color:#212121;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class DataLoader data;
    class ParameterRegistry param;
    class EMAAdapter strategy;
    class BacktestEngine strategy;
    class PerformanceReporter,AllocationReporter report;
```

## 📝 Parameter Class Documentation

### Parameter Class Hierarchy

```
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
└── ConfigParameter
```

### Parameter Usage Examples

#### NumericParameter

```python
# Definition
lookback_period = NumericParameter(
    name='lookback_period',
    default=30,
    min_val=10,
    max_val=100,
    step=5,
    show_in_gui=True,
    optimizable=True
)

# Registration
registry.register_parameter('ema', lookback_period)

# Usage
value = registry.get_parameter('lookback_period', 'ema').value
```

#### CategoricalParameter

```python
# Definition
rebalance_frequency = CategoricalParameter(
    name='rebalance_frequency',
    default='monthly',
    options=['daily', 'weekly', 'monthly', 'quarterly'],
    show_in_gui=True,
    optimizable=True
)

# Registration
registry.register_parameter('core', rebalance_frequency)

# Usage
freq = registry.get_parameter('rebalance_frequency', 'core').value
```

## 📈 Parameter Flow

1. **Definition**: Parameters are defined in registration modules
   ```python
   # In v3_register_parameters.py
   def register_ema_parameters():
       lookback_period = NumericParameter('lookback_period', 30, 10, 100, 5)
       registry.register_parameter('ema', lookback_period)
   ```

2. **Registration**: Parameters are registered with the registry
   ```python
   # In v3_gui_core.py
   registry = ParameterRegistry()
   register_core_parameters(registry)
   register_ema_parameters(registry)
   ```

3. **GUI Integration**: Parameters appear in the GUI
   ```python
   # In v3_parameter_widgets.py
   def create_widgets(registry):
       for param in registry.get_parameters('ema'):
           if param.show_in_gui:
               create_parameter_widget(param)
   ```

4. **Value Setting**: Values are set via GUI or code
   ```python
   # In v3_gui_actions.py
   def update_from_gui():
       for param, widget in param_widgets.items():
           param.value = get_value_from_widget(widget)
   ```

5. **Engine Usage**: Parameters are used in backtest
   ```python
   # In ema_v3_adapter.py
   def generate_signal(registry):
       lookback = registry.get_parameter('lookback_period', 'ema').value
       # Use lookback in calculations
   ```

6. **Reporting**: Parameters appear in reports
   ```python
   # In v3_performance_report.py
   def generate_report(registry, results):
       params = {p.name: p.value for p in registry.get_all_parameters()}
       # Include params in report
   ```

## 🔍 Recent Changes (May 2025)

1. **Parameter System**:
   - Added validation for execution_delay parameter
   - Fixed optimization handling for categorical parameters
   - Improved type safety throughout parameter flow

2. **Reporting**:
   - Enhanced allocation report formatting
   - Added parameter optimization results to performance tab
   - Fixed signal history tracking for reports

3. **GUI**:
   - Added parameter group visibility controls
   - Improved parameter widget layout
   - Fixed stalling issues in backtest execution

---

*This document is maintained as the comprehensive reference for all V3 modules and functions. Update as the system evolves.*


---

## reporting_system_AI

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\memory-bank\reporting_system_AI.md`*

*Tags: system_architecture,documentation,reporting*

# V3 Reporting System Guide (AI-Optimized)

<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 Reporting System Reference
Tags: [reporting_system, troubleshooting, v3_system]
-->

> **Last Updated**: 2025-05-14  
> **Purpose**: Comprehensive reference for the V3 reporting system and troubleshooting guide  
> **Related Files**:  
> 
> - [Parameter Management](parameter_management_AI.md)
> - [System Files + Flow](systemFiles+Flow_AI.md)
> - [V3 Module Functions List](v3_module+functions_list_AI.md)

## 🔍 Quick Reference

| Component                 | Key Files                                                                     | Purpose                                    |
| ------------------------- | ----------------------------------------------------------------------------- | ------------------------------------------ |
| **Performance Reporting** | `v3_reporting/v3_performance_report.py`, `reporting/performance_reporting.py` | Generate performance metrics tables        |
| **Allocation Reporting**  | `v3_reporting/v3_allocation_report.py`, `reporting/allocation_report.py`      | Generate allocation history reports        |
| **Visualization**         | `v3_reporting/v3_visualization.py`, `visualization/performance_charts.py`     | Create charts and visualizations           |
| **Adapter**               | `v3_engine/performance_reporter_adapter.py`                                   | Bridge between V3 parameters and reporting |

## 📊 Reporting System Flow

```mermaid
flowchart LR
    %% Parameter Flow
    Registry[parameter_registry.py] -->|Parameters| Backtest[backtest.py]
    Backtest -->|Results| Adapter[performance_reporter_adapter.py]

    %% Reporting Flow
    Adapter -->|Adapted Parameters| PerfReport[v3_performance_report.py]
    Adapter -->|Adapted Parameters| AllocReport[v3_allocation_report.py]
    PerfReport -->|Generate| Excel[Excel Reports]
    AllocReport -->|Generate| Charts[PNG Charts]

    %% Styling
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef engine fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef output fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;

    class Registry param;
    class Backtest engine;
    class Adapter,PerfReport,AllocReport report;
    class Excel,Charts output;
```

## 📝 Required Output Files

| Output Type             | Trigger Parameter          | File Format | Content                                            | Module                                |
| ----------------------- | -------------------------- | ----------- | -------------------------------------------------- | ------------------------------------- |
| **Performance Summary** | `create_excel`             | XLSX        | Parameter values + performance metrics             | `reporting/performance_reporting.py`  |
| **Allocation Report**   | `create_allocation_report` | XLSX        | Signal by date/ticker, allocation % by date/ticker | `reporting/allocation_report.py`      |
| **Trade Log**           | `save_trade_log`           | CSV         | Detailed log of individual trades                  | `utils/trade_log.py`                  |
| **Charts**              | `create_charts`            | PNG         | Performance visualizations                         | `visualization/performance_charts.py` |

## 🔄 Parameter Flow to Reports

```text
GUI → Parameter Registry → Backtest Engine → Performance Reporter Adapter → Reports
```

## 🚨 Known Issues and Status

1. **Parameter Conversion Gap**: 
   
   - Reporting parameters (`create_excel`, `metrics`, `create_charts`, `chart_types`) not yet converted to V3 parameter classes
   - These parameters don't flow through the V3 registry system

2. **Adapter Integration**:
   
   - `v3_engine/performance_reporter_adapter.py` may not be properly receiving or passing all parameters
   - End-to-end verification through full V3 backtest hasn't happened yet

3. **Signal History**:
   
   - Signal history may not be properly populated or preserved in backtest engine
   - Error: "signal_history is None or empty, allocation report/graph may not be produced"

4. **Allocation Report**:
   
   - `generate_rebalance_report` function coded but not yet working properly
   - Should have clear warnings, specific output paths, and follow naming conventions

## 🛠️ Systematic Troubleshooting Approach

### Phase 1: Parameter Registration

1. **Identify Current Parameter Definitions**:
   
   - Locate where reporting parameters are currently defined
   - Check `config/config.py`, `config/config_v2.py`, or similar files

2. **Create V3 Parameter Classes**:
   
   - Define proper V3 parameter classes for all reporting parameters
   - Example:
     
     ```python
     create_excel = ConfigParameter(
       name='create_excel',
       default=True,
       show_in_gui=True,
       optimizable=False
     )
     ```

3. **Register Parameters**:
   
   - Register these parameters with the V3 registry
   - Add to appropriate registration function in `app/gui/v3_register_parameters.py`

### Phase 2: Update Code to Use V3 Parameters

4. **Modify Reporter Adapter**:
   
   - Update `v3_engine/performance_reporter_adapter.py` to:
     - Accept the full V3 parameter set
     - Retrieve values of reporting parameters
     - Pass these values to reporting functions

5. **Update Reporting Modules**:
   
   - Modify core reporting modules to use parameters from adapter
   - Update XLSX header row generation to fetch all parameters
   - Ensure proper formatting according to standards

6. **Update Engine/Runner Script**:
   
   - Ensure main script passes complete parameter set to adapter
   - Verify parameter flow through entire system

### Phase 3: Signal History Debugging

7. **Trace Signal History**:
   
   - Add logging to track signal_history creation and modification
   - Verify signal_history is properly returned from backtest engine
   - Check for any data type issues or empty structures

8. **Fix Signal History**:
   
   - Ensure signal_history is properly populated during backtest
   - Add validation checks before reporting attempts to use it
   - Implement recovery logic if needed

## 📋 File-by-File Inspection Guide

### 1. Parameter Registry and Flow

```python
# Check parameter registration
grep_search for "register_parameter" in app/gui/v3_register_parameters.py

# Check parameter retrieval
grep_search for "get_parameter" in v3_engine/performance_reporter_adapter.py

# Check parameter passing
grep_search for "create_excel" in reporting/performance_reporting.py
```

### 2. Signal History Tracking

```python
# Check signal history creation
grep_search for "signal_history" in engine/backtest.py

# Check signal history usage
grep_search for "signal_history" in reporting/allocation_report.py

# Check error handling
grep_search for "signal_history is None" in v3_reporting/v3_allocation_report.py
```

### 3. Report Generation

```python
# Check performance report generation
grep_search for "generate_performance_report" in v3_reporting/v3_performance_report.py

# Check allocation report generation
grep_search for "generate_allocation_report" in v3_reporting/v3_allocation_report.py

# Check adapter functionality
grep_search for "adapt_parameters" in v3_engine/performance_reporter_adapter.py
```

## 📊 Required Report Format

### Performance Report (XLSX)

- **Column Order**: Parameters (left) → Metrics (right)
- **Parameters**: Strategy, lookbacks, rebalance frequency, etc.
- **Metrics**: CAGR, Sharpe, max drawdown, etc.
- **Formatting**: Native Excel number formats
- **Header Row**: Must include all parameter values

### Allocation Report (XLSX)

- **Tab 1**: Signal by date/ticker
- **Tab 2**: Allocation % by date/ticker
- **Header**: Parameter values and timestamp
- **Naming**: Must follow conventions with date/time

## 🔍 Recent Fixes (May 2025)

1. **Signal History**:
   
   - Modified `_calculate_results` in `engine/backtest.py` to include signal_history
   - Added recovery logic in `run_backtest_v2_with_metrics.py`

2. **Allocation Report**:
   
   - Updated allocation report generation to handle empty signal history
   - Fixed formatting issues in allocation charts

## 🔄 Structured Verification Process

A comprehensive verification system has been implemented to ensure the V3 reporting system functions correctly. This process includes:

### Expected Output Files

| Category                | Key Files                                               | Min Size | Verification Criteria                                      |
| ----------------------- | ------------------------------------------------------- | -------- | ---------------------------------------------------------- |
| **Log Files**           | `logs/v3_engine_reporting_test_{timestamp}.log`         | 1KB      | Contains "Starting V3 engine" and "Completed successfully" |
|                         | `logs/v3_debug_{timestamp}.txt`                         | 5KB      | Contains signal generation and report entries              |
|                         | `logs/v3_error_{timestamp}.log`                         | 0KB      | Empty if no errors                                         |
| **Performance Reports** | `output/{strategy}_performance_tables_{timestamp}.xlsx` | 50KB     | Contains all required tabs                                 |
|                         | `output/{strategy}_monthly_returns_{timestamp}.png`     | 100KB    | Image dimensions ≥ 1200x800                                |
|                         | `output/{strategy}_cumulative_returns_{timestamp}.png`  | 100KB    | Contains drawdown panel                                    |
| **Data Files**          | `output/{strategy}_signal_history_{timestamp}.csv`      | 10KB     | Contains dates and ticker columns                          |
|                         | `output/{strategy}_weights_history_{timestamp}.csv`     | 10KB     | Contains dates and ticker columns                          |
|                         | `output/{strategy}_returns_{timestamp}.csv`             | 10KB     | Contains dates and return values                           |

### Verification Steps

1. **Run Test with Enhanced Logging**
   
   - Set `BACKTEST_LOG_LEVEL=DEBUG`
   - Capture console output to dedicated log file

2. **Validate File Existence and Size**
   
   - Verify all expected files exist
   - Check file sizes against minimum requirements
   - Flag undersized files as potential errors

3. **Verify Content Structure**
   
   - Check log files for expected entries
   - Verify Excel files contain required sheets
   - Validate CSV files for proper structure

4. **Generate Verification Report**
   
   - Produce pass/fail status for each component
   - List missing or undersized files
   - Provide specific error details and fix recommendations

### Implementation Tools

- **Verification Script**: `verify_v3_reporting.py` automates the verification process

- **Test Wrapper**: `run_v3_test_with_verification.bat` runs test and verification together

- **Detailed Documentation**: See `docs/v3_reporting_analysis.md` for complete verification checklist
  
  - Modified to strip time from dates in Excel output
  - Improved chart formatting (years on x-axis, higher DPI, better colors)
3. **Parameter Handling**:
   - Fixed parameter handling to preserve tuple format
   - Modified performance reporting to extract values from parameter tuples

## 🔄 Testing Process

1. Run `run_ema_v3_gui_test.bat` to test the GUI and parameter flow
2. Check output directory for generated reports
3. Verify all required reports are generated with correct format
4. Inspect report content for accuracy and completeness
5. Check for any error messages in console output

---

*This document is maintained as the central reference for the V3 reporting system. Update as troubleshooting progresses.*


---

## systemFiles+Flow_AI

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\memory-bank\systemFiles+Flow_AI.md`*

*Tags: system_architecture,documentation,system_flow*

# System Files + Flow Documentation (AI-Optimized)

<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 System Architecture Reference
Tags: [system_architecture, module_flow, v3_system]
-->

> **Last Updated**: 2025-05-14
> **Purpose**: Central reference for system architecture, module relationships, and data flows
> **Related Files**:
> - [V3 Module Functions List](v3_module+functions_list_AI.md)
> - [Parameter System Reference](v3_parameter_system_reference.md)

## 🔍 Quick Reference

| Component Type | Key Files | Purpose |
|----------------|-----------|---------|
| **Parameter System** | `v3_engine/parameter_registry.py`, `v3_engine/parameters.py` | Type-safe parameter handling |
| **Backtest Engine** | `engine/backtest.py`, `engine/portfolio.py` | Core backtest logic |
| **Strategy** | `v3_engine/ema_v3_adapter.py` | Signal generation |
| **Reporting** | `v3_reporting/v3_performance_report.py` | Results output |

## 📊 System Flow Diagram

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data_loader.py] -->|OHLCV Data| Strategy[ema_v3_adapter.py]
    DataLoader -->|Benchmark Data| Backtest[backtest.py]

    %% Parameter Flow
    ParamRegistry[parameter_registry.py] -->|Parameters| Strategy
    ParamRegistry -->|Parameters| Backtest
    
    %% Execution Flow
    Strategy -->|Signals| Backtest
    Backtest -->|Results| Reports[v3_performance_report.py]
    
    %% Styling
    classDef data fill:#F5F5F5,stroke:#616161,color:#212121;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef engine fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class DataLoader data;
    class ParamRegistry param;
    class Strategy,Backtest engine;
    class Reports report;
```

## 🧩 Core Engine Components

| Module | Key Functions | Purpose | Status |
|--------|---------------|---------|--------|
| **`engine/backtest.py`** | `run_backtest()`, `calculate_metrics()` | Main backtest execution | ✅ |
| **`engine/portfolio.py`** | `update_portfolio()`, `calculate_value()` | Portfolio tracking | ✅ |
| **`engine/execution.py`** | `execute_trades()`, `apply_slippage()` | Trade execution | ✅ |
| **`v3_engine/parameter_registry.py`** | `register_parameter()`, `get_parameter()` | Parameter management | ✅ |

## 🔄 Key Data Flows

### 1. Parameter Flow

```text
GUI → Parameter Registry → Strategy → Backtest Engine → Reports
```

**Key Components**: See [Parameter Management](parameter_management_AI.md) for details

### 2. Data Flow

```text
Data Loader → Strategy → Backtest Engine → Portfolio → Reports
```

**Key Components**:
- `data/data_loader.py`: Loads price data
- `v3_engine/ema_v3_adapter.py`: Generates signals
- `engine/backtest.py`: Executes backtest
- `engine/portfolio.py`: Tracks portfolio state

### 3. Reporting Flow

```text
Backtest Engine → Performance Reporter → Excel/Charts
```

**Key Components**:
- `v3_reporting/v3_performance_report.py`: Generates performance reports
- `v3_reporting/v3_allocation_report.py`: Generates allocation reports
- `v3_engine/performance_reporter_adapter.py`: Adapts V3 parameters to reporting

## 📝 Parameter System

For detailed information about the parameter system, including registry API, parameter classes, and optimization, see the [Parameter Management](parameter_management_AI.md) document.

## 📈 V3 Reporting System

| Module | Purpose | Status |
|--------|---------|:------:|
| `v3_reporting/v3_performance_report.py` | Performance metrics | ✅ |
| `v3_reporting/v3_allocation_report.py` | Allocation history | ✅ |
| `v3_reporting/v3_visualization.py` | Chart generation | ✅ |
| `v3_engine/performance_reporter_adapter.py` | V2/V3 bridge | ✅ |

## 🔄 V3 Process Flow

```mermaid
flowchart TD
    subgraph GUI[GUI Layer]
        A[v3_gui_core.py] -->|Initialize| B[v3_parameter_widgets.py]
        A -->|Actions| C[v3_gui_actions.py]
        B -->|Manage| D[gui_parameter_manager.py]
    end
    
    subgraph Engine[Engine Layer]
        E[parameter_registry.py] -->|Type Defs| F[parameters.py]
        E -->|Strategy Params| G[strategy_parameter_set.py]
        H[ema_v3_adapter.py] -->|Signals| I[backtest.py]
    end
    
    subgraph Reporting[Reporting Layer]
        J[v3_performance_report.py]
        K[v3_allocation_report.py]
        L[performance_reporter_adapter.py]
    end
    
    D -->|Sync| E
    G -->|Apply| H
    I -->|Results| J
    I -->|Allocations| K
    L -->|Bridge| J
    
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef engine fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class A,B,C,D gui;
    class E,F,G,H,I engine;
    class J,K,L report;
```

## 🔍 Recent Changes (May 2025)

1. **Parameter System**: See [Parameter Management](parameter_management_AI.md) for recent changes

2. **Reporting**:
   - Enhanced allocation report formatting
   - Added parameter optimization results to performance tab
   - Fixed signal history tracking for reports

3. **GUI**:
   - Added parameter group visibility controls
   - Improved parameter widget layout
   - Fixed stalling issues in backtest execution

## 📋 Implementation Status

| Component | Status | Notes |
|-----------|:------:|-------|
| Parameter System | ✅ | See [Parameter Management](parameter_management_AI.md) |
| GUI Integration | ✅ | Fully functional |
| EMA Strategy Adapter | ✅ | Complete with parameter integration |
| Backtest Engine Integration | ✅ | Working with V3 parameters |
| Performance Reporting | ✅ | Complete with optimization support |
| Allocation Reporting | ✅ | Complete with improved formatting |
| Data Validation | ⚠️ | Basic implementation, needs expansion |
| Error Handling | 🔄 | Planned for future implementation |

---

*This document is maintained as the central reference for V3 system architecture. Update as the system evolves.*


---

## parameter_management_AI

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\memory-bank\parameter_management_AI.md`*

*Tags: documentation,parameter_system*

# Parameter Management System (AI-Optimized)
<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 Parameter System Reference
Tags: [parameter_system, parameter_types, v3_system]
-->

> **Last Updated**: 2025-05-14
> **Purpose**: Comprehensive reference for the V3 parameter system
> **Related Files**:
> - [System Files + Flow](systemFiles+Flow_AI.md)
> - [V3 Module Functions List](v3_module+functions_list_AI.md)

## 🔍 Quick Reference

| Component | Key Files | Purpose |
|-----------|-----------|---------|
| **Parameter Registry** | `v3_engine/parameter_registry.py` | Central parameter management |
| **Parameter Types** | `v3_engine/parameters.py` | Parameter class definitions |
| **Strategy Parameters** | `v3_engine/strategy_parameter_set.py` | Strategy-specific parameters |
| **Parameter Optimizer** | `v3_engine/parameter_optimizer.py` | Parameter optimization logic |

## 📊 Parameter Flow Diagram

```mermaid
flowchart LR
    %% Parameter Flow
    GUI[v3_gui_core.py] -->|User Input| ParamWidgets[v3_parameter_widgets.py]
    ParamWidgets -->|Update Values| ParamMgr[gui_parameter_manager.py]
    ParamMgr -->|Register/Update| Registry[parameter_registry.py]
    Registry -->|Define Types| Params[parameters.py]
    Registry -->|Group Parameters| StratParams[strategy_parameter_set.py]
    Registry -->|Optimize| Optimizer[parameter_optimizer.py]
    Registry -->|Parameters| Strategy[ema_v3_adapter.py]
    Registry -->|Parameters| Backtest[backtest.py]
    Registry -->|Parameter Values| Reports[v3_performance_report.py]
    
    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef engine fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class GUI,ParamWidgets,ParamMgr gui;
    class Registry,Params,StratParams,Optimizer param;
    class Strategy,Backtest engine;
    class Reports report;
```

## 🔄 Parameter Flow Process

```text
GUI → Parameter Registry → Strategy → Backtest Engine → Reports
```

**Key Components**:

- `v3_engine/parameter_registry.py`: Central registry for all parameters
- `v3_engine/parameters.py`: Parameter class definitions
- `v3_engine/strategy_parameter_set.py`: Strategy-specific parameter grouping
- `v3_engine/parameter_optimizer.py`: Parameter optimization logic

## 📝 Parameter Registry API

| Method | Purpose | Example Usage |
|--------|---------|---------------|
| `register_parameter(group, parameter)` | Register single parameter | `registry.register_parameter('ema', lookback_period)` |
| `register_parameter_list(group, parameters)` | Register multiple parameters | `registry.register_parameter_list('core', [initial_capital, commission_rate])` |
| `get_parameter(name, group=None)` | Get parameter by name | `lookback = registry.get_parameter('lookback_period', 'ema')` |
| `get_parameters(group)` | Get all parameters in group | `ema_params = registry.get_parameters('ema')` |
| `get_all_parameters()` | Get all registered parameters | `all_params = registry.get_all_parameters()` |
| `get_parameter_values(group=None)` | Get parameter values as dict | `param_dict = registry.get_parameter_values()` |

## 🧪 Parameter Classes

| Class | Purpose | Optimizable | GUI Visible | Example |
|-------|---------|:-----------:|:-----------:|---------|
| `NumericParameter` | Integer/float values | ✅ | ✅ | `lookback_period = NumericParameter('lookback_period', 30, 10, 100, 5)` |
| `CategoricalParameter` | Selection from options | ✅ | ✅ | `rebalance_freq = CategoricalParameter('rebalance_frequency', 'monthly', ['daily', 'weekly', 'monthly'])` |
| `ConfigParameter` | Configuration values | ❌ | ❌ | `data_dir = ConfigParameter('data_directory', 'data/')` |
| `BaseParameter` | Base class for all parameters | ❌ | ❌ | Not used directly |

## 📋 Parameter Class Hierarchy

```text
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
└── ConfigParameter
```

## 🔄 Parameter Registration Process

### 1. Define Parameters

```python
# In v3_register_parameters.py
def register_ema_parameters():
    # Create parameter objects
    lookback_period = NumericParameter(
        name='lookback_period',
        default=30,
        min_val=10,
        max_val=100,
        step=5,
        show_in_gui=True,
        optimizable=True
    )
    
    # Return list of parameters
    return [lookback_period]
```

### 2. Register with Registry

```python
# In v3_gui_core.py
def initialize_parameters():
    # Create registry
    registry = ParameterRegistry()
    
    # Register core parameters
    core_params = register_core_parameters()
    registry.register_parameter_list('core', core_params)
    
    # Register strategy parameters
    ema_params = register_ema_parameters()
    registry.register_parameter_list('ema', ema_params)
    
    return registry
```

### 3. Connect to GUI

```python
# In v3_parameter_widgets.py
def create_parameter_widgets(registry):
    widgets = {}
    
    # Create widgets for each parameter
    for param in registry.get_all_parameters():
        if param.show_in_gui:
            widget = create_widget_for_parameter(param)
            widgets[param] = widget
            
    return widgets
```

### 4. Update from GUI

```python
# In v3_gui_actions.py
def update_parameters_from_gui(registry, widgets):
    # Update parameter values from widgets
    for param, widget in widgets.items():
        value = get_value_from_widget(widget)
        param.value = value
```

### 5. Use in Backtest

```python
# In ema_v3_adapter.py
def generate_signal(registry, price_data):
    # Get parameter values
    lookback = registry.get_parameter('lookback_period', 'ema').value
    
    # Use in calculations
    signals = calculate_ema_signals(price_data, lookback)
    return signals
```

### 6. Include in Reports

```python
# In v3_performance_report.py
def generate_report(registry, results):
    # Get all parameter values as dict
    params = registry.get_parameter_values()
    
    # Include in report
    report_data = {
        'parameters': params,
        'results': results
    }
    
    return report_data
```

## 🔄 Parameter Optimization

### 1. Define Optimization Ranges

```python
# In v3_register_parameters.py
lookback_period = NumericParameter(
    name='lookback_period',
    default=30,
    min_val=10,
    max_val=100,
    step=5,
    show_in_gui=True,
    optimizable=True  # Mark as optimizable
)
```

### 2. Generate Combinations

```python
# In v3_engine/parameter_optimizer.py
def get_optimization_combinations(registry):
    # Get optimizable parameters
    optimizable = [p for p in registry.get_all_parameters() if p.optimizable]
    
    # Generate combinations
    combinations = []
    for param in optimizable:
        if isinstance(param, NumericParameter):
            values = range(param.min_val, param.max_val + 1, param.step)
        elif isinstance(param, CategoricalParameter):
            values = param.options
            
        combinations.append(values)
        
    # Return product of all combinations
    return itertools.product(*combinations)
```

### 3. Run Optimization

```python
# In run_backtest_v3.py
def run_optimization(registry):
    # Get parameter combinations
    combinations = get_optimization_combinations(registry)
    
    # Run backtest for each combination
    results = []
    for combo in combinations:
        # Update parameter values
        for param, value in zip(get_optimizable_parameters(registry), combo):
            param.value = value
            
        # Run backtest with current parameters
        result = run_backtest(registry)
        results.append(result)
        
    return results
```

## 🔍 Recent Parameter System Changes (May 2025)

1. **Execution Delay Parameter**:
   - Fixed optimization handling for execution_delay parameter
   - Added proper type checking in backtest engine
   - Ensured consistent handling in performance reports

2. **Categorical Parameters**:
   - Improved validation for categorical parameters
   - Added support for categorical parameter optimization
   - Fixed GUI display for categorical parameters

3. **Type Safety**:
   - Enhanced parameter validation throughout the system
   - Added clear error messages for parameter type mismatches
   - Implemented consistent parameter conversion between components

## 📋 Implementation Status

| Component | Status | Notes |
|-----------|:------:|-------|
| Parameter Registry | ✅ | Complete with all parameter types |
| Parameter Classes | ✅ | All parameter types implemented |
| GUI Integration | ✅ | Fully functional parameter widgets |
| Parameter Optimization | ✅ | Working with all parameter types |
| Parameter Validation | ⚠️ | Basic validation implemented, needs expansion |
| Parameter Persistence | 🔄 | Planned for future implementation |

---

*This document is maintained as the central reference for the V3 parameter system. Update as the system evolves.*


---

## v3_module+functions_list

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\v3_module+functions_list.md`*

*Tags: documentation,module_reference*

# V3 Module Mapping Matrix

This document provides a detailed mapping between V2 and V3 modules, showing how each V2 component is represented, enhanced, or replaced in the V3 architecture. The mapping helps ensure that all existing capabilities are maintained in the V3 transition.

## Implementation Status

This document distinguishes between modules that are currently implemented and those that are planned for future implementation:

- ✓ Fully implemented and in use
- ⚠️ Partially implemented or in progress
- 🔄 Planned for future implementation

## Module Mapping Table

| V2 Module                              | V3 Module                                                 | Status | Key Functions                                                      | Description of Changes                                                         |
| -------------------------------------- | --------------------------------------------------------- | ------ | ------------------------------------------------------------------ | ------------------------------------------------------------------------------ |
| `config/config_v2.py`                  | `v3_engine/parameter_registry.py`<br/>`config/config_v3.py` | ✓      | `register_parameter()`, `get_parameter()`, `get_parameter_values()` | Parameter tuples replaced by type-safe parameter objects in a central registry |
| `config/parameter_optimization.py`     | `v3_engine/parameter_optimizer.py`                        | ✓      | `get_optimization_combinations()`, `optimize_parameters()`          | Enhanced with support for categorical parameters and execution_delay fixes     |
| `engine/backtest.py`                   | (unchanged)                                               | ✓      | `run_backtest()`, `calculate_metrics()`                            | No direct changes, becomes a consumer of parameters from registry              |
| `engine/portfolio.py`                  | (unchanged)                                               | ✓      | `update_portfolio()`, `calculate_portfolio_value()`                | No direct changes, becomes a consumer of parameters from registry              |
| `engine/orders.py`                     | (unchanged)                                               | ✓      | `create_order()`, `execute_order()`                               | No direct changes, becomes a consumer of parameters from registry              |
| `engine/execution.py`                  | (unchanged)                                               | ✓      | `execute_trades()`, `apply_slippage()`                            | No direct changes, becomes a consumer of parameters from registry              |
| `engine/allocation.py`                 | (unchanged)                                               | ✓      | `compare_positions()`, `generate_orders()`                        | No direct changes, becomes a consumer of parameters from registry              |
| `models/ema_allocation_model.py`       | `v3_engine/ema_v3_adapter.py`                             | ✓      | `generate_signal()`, `calculate_ema_metrics()`                    | Adapter pattern for EMA model with V3 parameters                              |
| `performance/performance_reporting.py` | `v3_engine/performance_reporter_adapter.py`               | ✓      | `adapt_parameters()`, `convert_metrics()`                         | Adapter pattern maintains existing output format                               |
| `config_interface.py`                  | `v3_engine/gui_parameter_manager.py`                      | ✓      | `create_parameter_widgets()`, `update_parameters_from_widgets()`   | Enhanced GUI integration                                                       |
| (New)                                  | `app/gui/v3_gui_core.py`                                  | ✓      | `MainWindowV3.__init__()`, `_run_backtest()`                      | Main GUI window implementation for V3                                          |
| (New)                                  | `app/gui/v3_parameter_widgets.py`                         | ✓      | `create_parameter_widgets()`, `update_parameters_from_widgets()`   | Parameter-specific GUI widgets                                                 |
| (New)                                  | `app/gui/v3_gui_actions.py`                               | ✓      | `run_backtest_action()`, `generate_v3_performance_report()`       | GUI action handlers                                                            |
| (New)                                  | `v3_engine/parameters.py`                                 | ✓      | `NumericParameter.__init__()`, `CategoricalParameter.validate()`   | Core parameter classes                                                         |
| (New)                                  | `v3_engine/strategy_parameter_set.py`                     | ✓      | `register_parameters()`, `get_parameter_values()`                  | Strategy parameter container                                                   |
| (New)                                  | `v3_engine/data_validator.py`                             | ✓      | `validate_data()`, `check_missing()`                               | Data validation utilities                                                      |

## Module Function Reference

### Core Engine Modules (Implemented)

| Module                                      | Key Functions                                                  | Status | Description                               |
| ------------------------------------------- | -------------------------------------------------------------- | ------ | ----------------------------------------- |
| `v3_engine/parameter_registry.py`           | `register_parameter()`, `get_parameter()`, `get_parameter_values()` | ✓      | Central parameter registry                |
| `v3_engine/parameter_optimizer.py`          | `get_optimization_combinations()`, `optimize_parameters()`     | ✓      | Parameter optimization                    |
| `v3_engine/performance_reporter_adapter.py` | `adapt_parameters()`, `convert_metrics()`                      | ✓      | Bridges V3 parameters to legacy reporting |
| `v3_engine/data_validator.py`               | `validate_data()`, `check_missing()`                           | ✓      | Data quality validation                   |
| `v3_engine/ema_v3_adapter.py`               | `generate_signal()`, `calculate_ema_metrics()`                 | ✓      | EMA strategy adapter for V3               |
| `v3_engine/strategy_parameter_set.py`       | `register_parameters()`, `get_parameter_values()`              | ✓      | Strategy parameter container              |
| `v3_engine/parameters.py`                   | `NumericParameter.__init__()`, `CategoricalParameter.validate()` | ✓    | Parameter class definitions              |
| `v3_engine/gui_parameter_manager.py`        | `create_parameter_widgets()`, `update_parameters_from_widgets()` | ✓    | GUI parameter management                 |

### Core Engine Modules (Planned)

| Module                                      | Key Functions                                                  | Status | Description                               |
| ------------------------------------------- | -------------------------------------------------------------- | ------ | ----------------------------------------- |
| `v3_engine/data_quality_metrics.py`         | `calculate_quality_score()`, `generate_quality_report()`       | 🔄      | Data quality scoring                      |
| `v3_engine/exception_handler.py`            | `handle_error()`, `log_exception()`, `recover_from_error()`    | 🔄      | Unified error handling                    |

### Reporting Modules (Implemented)

| Module                                  | Key Functions                                             | Status | Description                   |
| --------------------------------------- | --------------------------------------------------------- | ------ | ----------------------------- |
| `v3_reporting/v3_performance_report.py` | `generate_v3_performance_report()`                        | ✓      | Performance report generation |
| `v3_reporting/v3_allocation_report.py`  | `generate_v3_allocation_report()`                         | ✓      | Allocation report generation  |
| `v3_reporting/v3_visualization.py`      | `create_chart()`, `save_chart()`, `format_axes()`         | ✓      | Chart generation              |
| `v3_reporting/v3_performance_charts.py` | `create_performance_chart()`, `plot_metrics()`            | ✓      | Performance-specific charts   |
| `v3_reporting/v3_trade_log.py`          | `format_trade_log()`, `summarize_trades()`                | ✓      | Trade log formatting and export |

### Strategy Modules

#### Implemented

| Module                                | Key Functions                                                       | Status | Description                       |
| ------------------------------------- | ------------------------------------------------------------------- | ------ | --------------------------------- |
| `v3_engine/ema_v3_adapter.py`         | `generate_signal()`, `calculate_ema_metrics()`                      | ✓      | EMA strategy adapter for V3        |

#### Planned

| Module                                | Key Functions                                                       | Status | Description                       |
| ------------------------------------- | ------------------------------------------------------------------- | ------ | --------------------------------- |
| `v3_strategies/ema_strategy.py`       | `generate_signals()`, `rebalance()`, `calculate_weights()`          | 🔄      | EMA strategy implementation       |
| `v3_strategies/strategy_discovery.py` | `load_strategies()`, `validate_strategy()`, `get_strategy_params()` | 🔄      | Dynamic strategy loading          |
| `v3_strategies/strategy_validator.py` | `validate_config()`, `check_parameters()`, `verify_compatibility()` | 🔄      | Strategy configuration validation |

## Data Quality

### Implemented Data Validation

| V3 Module                           | Legacy Equivalent         | Key Functions                                   | Status | Key Changes                                    |
| ----------------------------------- | ------------------------- | ----------------------------------------------- | ------ | ---------------------------------------------- |
| `v3_engine/data_validator.py`       | `data/quality_check.py`   | `validate_data()`, `check_missing()`            | ✓      | Basic validation rules implemented             |

### Planned Data Quality Metrics

| V3 Module                           | Legacy Equivalent         | Key Functions                                   | Status | Key Changes                                    |
| ----------------------------------- | ------------------------- | ----------------------------------------------- | ------ | ---------------------------------------------- |
| `v3_engine/data_quality_metrics.py` | `data/quality_metrics.py` | `calculate_quality_score()`, `generate_quality_report()` | 🔄    | Quantitative scoring with error reporting      |

### Current Data Flow

```mermaid
flowchart LR
DataLoader --> DataValidator --> Strategy
```

The current data validation process provides basic validation of input data before it's used in strategy calculations. Error handling is currently managed through standard Python exceptions and logging.

## Error Handling

### Current Implementation

The V3 system currently uses standard Python exception handling and logging rather than a dedicated error handling module. Error handling is implemented directly in each module as needed.

### Planned Error Handling Modules

| V3 Module                        | Legacy Equivalent          | Key Functions                                                | Status | Description                                     |
| -------------------------------- | -------------------------- | ------------------------------------------------------------ | ------ | ----------------------------------------------- |
| `v3_engine/exception_handler.py` | `utils/error_handling.py`  | `handle_error()`, `log_exception()`, `recover_from_error()`  | 🔄      | Context-aware handling with TradeLog integration |
| `v3_engine/error_reporter.py`    | `logging/error_reports.py` | `report_error()`, `format_error()`, `categorize_error()`     | 🔄      | Structured error logging with severity levels   |

### Planned Error Flow

```mermaid
flowchart TD
    Strategy -->|Errors| ExceptionHandler[v3_engine/exception_handler.py]
    DataValidator -->|Validation Errors| ExceptionHandler
    ExceptionHandler -->|Logged Errors| TradeLog[utils/trade_log.py]
    ExceptionHandler -->|Formatted Reports| ErrorReporter[v3_engine/error_reporter.py]
    ErrorReporter -->|User Notifications| GUI
```

The planned error handling system will provide comprehensive error management across the entire application, with appropriate logging and user feedback.

## Strategy Loading

### Current Strategy Implementation

Currently, the V3 system uses a direct adapter approach rather than a dynamic strategy loading system. The EMA strategy is adapted through the `v3_engine/ema_v3_adapter.py` module, which connects the existing EMA allocation model to the V3 parameter system.

| V3 Module                      | Legacy Equivalent              | Key Functions                                | Status | Description                                   |
| ------------------------------ | ------------------------------ | -------------------------------------------- | ------ | --------------------------------------------- |
| `v3_engine/ema_v3_adapter.py`  | `models/ema_allocation_model.py` | `generate_signal()`, `calculate_ema_metrics()` | ✓      | Adapter for EMA model with V3 parameters      |

### Current Strategy Loading Flow

```mermaid
flowchart TD
    ParameterRegistry[v3_engine/parameter_registry.py] -->|Parameter Values| EMAAdapter[v3_engine/ema_v3_adapter.py]
    EMAAdapter -->|Generate Signals| BacktestEngine[engine/backtest.py]
```

### Planned Strategy Loading System

A more comprehensive strategy loading system is planned for future implementation:

| V3 Module                                | Legacy Equivalent              | Key Functions                                                       | Status | Description                                    |
| ---------------------------------------- | ------------------------------ | ------------------------------------------------------------------- | ------ | ---------------------------------------------- |
| `v3_strategies/strategy_discovery.py`    | N/A                            | `load_strategies()`, `validate_strategy()`, `get_strategy_params()` | 🔄      | Dynamic strategy loading and validation         |
| `v3_strategies/strategy_validator.py`    | N/A                            | `validate_config()`, `check_parameters()`, `verify_compatibility()` | 🔄      | Strategy configuration validation               |
| `v3_strategies/strategy_registry.py`     | N/A                            | `register_strategy()`, `get_strategy()`, `list_strategies()`         | 🔄      | Central registry for available strategies       |

### Planned Strategy Loading Flow

```mermaid
flowchart TD
    StrategyRegistry[v3_strategies/strategy_registry.py] -->|Available Strategies| StrategyDiscovery[v3_strategies/strategy_discovery.py]
    StrategyDiscovery -->|Load Strategy| Strategy[Strategy Implementation]
    Strategy -->|Register Parameters| ParameterRegistry[v3_engine/parameter_registry.py]
    ParameterRegistry -->|Parameter Values| Strategy
    Strategy -->|Validate Config| StrategyValidator[v3_strategies/strategy_validator.py]
```

The planned strategy loading system will provide a plug-and-play architecture for adding new strategies with minimal code changes, where each strategy self-registers its parameters with the central registry.

## Component Interactions

### Actual Component Flow

The following diagram shows the actual component interactions in the current V3 implementation:

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data/data_loader.py] -->|OHLCV Data| EMAAdapter
    DataLoader -->|Benchmark Data| BacktestEngine

    %% Parameter Flow
    ParameterRegistry[v3_engine/parameter_registry.py] -->|Parameter Values| EMAAdapter[v3_engine/ema_v3_adapter.py]
    ParameterRegistry -->|Parameter Values| BacktestEngine[engine/backtest.py]
    ParameterRegistry -->|Parameter Values| PerformanceReporterAdapter[v3_engine/performance_reporter_adapter.py]
    ParameterOptimizer[v3_engine/parameter_optimizer.py] -->|Optimization Combinations| ParameterRegistry

    %% Execution Flow
    EMAAdapter -->|Generate Signals| BacktestEngine
    BacktestEngine -->|Execute Trades| BacktestEngine

    %% Reporting Flow
    BacktestEngine -->|Results| PerformanceReporter[v3_reporting/v3_performance_report.py]
    BacktestEngine -->|Allocations| AllocationReporter[v3_reporting/v3_allocation_report.py]
    PerformanceReporter -->|Charts| PerformanceCharts[v3_reporting/v3_performance_charts.py]
    AllocationReporter -->|Charts| Visualization[v3_reporting/v3_visualization.py]
```

This diagram shows the actual flow of data and parameters in the current V3 implementation, with the EMA adapter serving as the strategy component.

## Architecture Changes

### Parameter System Implementation

#### V2 Parameter Approach

- Parameters defined as tuples (`'Y', default, min, max, step`)
- Manual parameter handling in each component
- Implicit type conversion between components
- Parameter optimization logic intermixed with configuration

#### V3 Parameter Approach (Implemented)

- Type-safe parameter objects with validation
- Central registry as single source of truth
- Unified parameter flow from definition → GUI → engine → reports
- Separate optimization system that preserves parameter types

### Strategy Management Evolution

#### V2 Strategy Approach

- Strategies with hardcoded parameters
- Manual parameter registration in multiple places
- Custom code for each strategy parameter in GUI

#### Current V3 Strategy Approach

- EMA adapter connects existing model to V3 parameter system
- Parameters registered centrally in v3_register_parameters.py
- GUI controls generated automatically based on parameter types

#### Future V3 Strategy Approach (Planned)

- Plug-and-play strategies with self-contained parameters
- Automatic parameter registration with registry
- Dynamic strategy loading and discovery

### Performance Reporting Integration

#### V2 Reporting Approach

- Direct parameter usage in reports
- Parameter format conversions in report generation

#### V3 Reporting Approach (Implemented)

- Adapter preserves existing reporting capabilities
- Clean parameter interface that hides implementation details
- Special handling for execution_delay parameter
- Dedicated V3 reporting modules for performance and allocation reports

### V3 Reporting System

#### Table

| V3 Module                                   | Legacy Module                         | Purpose                                                 |
| ------------------------------------------- | ------------------------------------- | ------------------------------------------------------- |
| `v3_reporting/v3_performance_report.py`     | `reporting/performance_reporting.py`  | Wrapper for performance reports using V3 parameters     |
| `v3_reporting/v3_allocation_report.py`      | `reporting/allocation_report.py`      | Wrapper for allocation reports using V3 parameters      |
| `v3_reporting/v3_visualization.py`          | `visualization/performance_charts.py` | Wrapper for chart generation using V3 parameters        |
| `v3_engine/performance_reporter_adapter.py` | N/A                                   | Adapter between V3 parameters and legacy reporting code |
| `v3_engine/reporting_parameters.py`         | N/A                                   | Registers all reporting parameters with V3 registry     |

### Key Differences

- V3 modules handle parameter conversion and validation
- Legacy modules remain unchanged for backward compatibility
- All new development should use V3 modules

## Parameter Class Documentation

### Parameter Class Hierarchy

The V3 parameter system uses a class hierarchy to represent different types of parameters with varying behaviors:

```markdown
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
└── ConfigParameter
```

### Parameter Conversion Guidelines

When converting config variables to parameter classes:

1. Use **ConfigParameter** for variables that:
   
   - Should not appear in the GUI
   - Should not be optimized
   - Are loaded from config
   - Should appear in performance reports

2. Use **NumericParameter** for variables that:
   
   - Have numeric values with min/max bounds
   - May need optimization
   - Should appear in the GUI

3. Use **CategoricalParameter** for variables that:
   
   - Have a fixed set of options
   - May need optimization over those options
   - Should appear in the GUI

4. Use **BaseParameter** as a fallback for any other variables

### Parameter Flow

Parameters flow through the system in the following way:

1. **Definition**: Parameters are defined in their respective registration functions
2. **Registration**: Parameters are registered with the V3 registry
3. **GUI Integration**: Parameters marked with `show_in_gui=True` appear in the GUI
4. **Value Setting**: Parameter values are set via GUI or directly in code
5. **Engine Usage**: Parameters are passed to the backtest engine
6. **Reporting**: Parameter values are included in performance reports

## Implementation Plans

### Phase 1: Core Parameter System

- Implement parameter classes and registry 
- Create strategy parameter set framework 
- Design GUI parameter manager 

### Phase 2: Integration Layer

- Develop performance reporter adapter 
- Create configuration conversion utilities
- Implement ticker list configuration 

### Phase 3: Strategy Refactoring

- Refactor EMA strategy to use StrategyParameterSet
- Create new strategy implementations
- Test plug-and-play capability

### Phase 4: GUI Integration

- Integrate GUI parameter manager with existing interface
- Implement strategy selector in GUI
- Add parameter optimization controls

### Phase 5: Engine Integration

- Update backtest engine to use registry for parameters
- Ensure full backward compatibility
- Comprehensive testing

## Testing Strategy

1. Unit tests for each V3 component
2. Integration tests for parameter flow
3. Specific tests for execution_delay parameter optimization
4. Backward compatibility tests with V2 configurations
5. Performance comparison between V2 and V3 implementations


---

## v3_performance_reporting_standards_a

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\v3_performance_reporting_standards_a.md`*

*Tags: documentation,reporting_standards*

# V3 Performance Reporting Standards (Version A)

---

# Tier 1: Major Output Reports

## Performance Table Report (XLSX)

### Tabs within Performance Table Report (Tier 2)

#### Signal History

- **Tab Name**: "Signal History"
- **Structure**:
  - Rows: Daily dates (no timestamps)
  - Columns: Tickers + Cash
- **Content**:
  - % allocation signals (end-of-day)
  - Each row sums to 100%
- **Format**: `0.00%`

---

#### Allocation History

- **Tab Name**: "Allocation History"
- **Structure**: Matches Signal History
- **Content**:
  - Each row reflects the engine's ACTUAL capital allocation for that day, based on all trades executed up to and including that date.
  - Once an allocation is made, it is held constant and shown on all subsequent days until the next trade or allocation event. There are no gaps—every day in the backtest period must be fully allocated across all assets and cash.
  - Actual % allocations (from trade logs)
  - Typically 1-day lag from signals
  - Each row sums to 100%
- **Format**: `0.00%`

##### Sample Table

| Date       | SPY | SHV | EFA | TLT | PFF | Cash |
| ---------- | --- | --- | --- | --- | --- | ---- |
| 2020-01-02 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-03 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-06 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-07 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-08 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| 2020-01-09 | 0   | 0   | 0   | 0.6 | 0.4 | 0    |
| ...        | ... | ... | ... | ... | ... | ...  |

##### Allocation Weights Over Time Graphic (PNG)

- **Requirement:**
  - Produce a PNG image visualizing portfolio allocation weights over time, for all assets in the Allocation History tab.
  - The graphic should show the evolution of allocations as a stacked area or bar chart, with the x-axis as Date and y-axis as Allocation (0 to 1).
  - The legend must clearly indicate each asset.
  - Store the image as `docs/allocation_history_example.png`.
  - The PNG may be a separate output file or, if easy, an additional tab in the XLSX (image embedded).
- **Example:**
  - ![Example Allocation History Graphic](../docs/allocation_history_example.png)

---

#### Trade Log

- **Tab Name**: "Trade Log"
- **Header**:
  - Cell A1 must display all main/default parameter settings in text format  
    Example: "st_lookback=30, mt_lookback=90, lt_lookback=180, execution_delay=1, top_n=5"
- **Structure:**
  - Columns (in order):
    1. `trade_num` (sequential integer)
    2. `symbol` (ticker)
    3. `quantity` (signed integer, + for buy, - for sell)
    4. `execution_date` (YYYY-MM-DD, no time)
    5. `execution_price` (float, 3 decimals)
    6. `commission+slippage` (float, 2 decimals) — *Total of commission and slippage for the trade*
    7. `amount` (float, 2 decimals)
    8. `pnl` (float, 2 decimals)
- **Rows:**
  - One per executed trade (buy/sell), sorted by execution_date and trade_num.
  - Final date: All open positions forcibly closed, with closing trades shown.
- **Content:**
  - Every executed trade from the backtest engine, not intended but actual.
  - All tickers, all dates in range.
  - No missing trades; partial fills included if engine supports.
- **Formatting:**
  - Dates: `YYYY-MM-DD`
  - `execution_price`: 3 decimals (e.g., 97.123)
  - `commission+slippage`, `amount`, `pnl`: 2 decimals (e.g., 4019.66, -7.85)
  - All values as floats (no currency formatting required)
  - No blank rows or extra summary/statistics.
- **Audit Rule:**
  - Must be a direct, reproducible output from the engine for given parameters.

##### Sample Table

| trade_num | symbol | quantity | execution_date | execution_price | commission+slippage | amount   | pnl   |
| --------- | ------ | -------- | -------------- | --------------- | ------------------- | -------- | ----- |
| 1         | SHV    | 41       | 2020-04-03     | 97.942          | 4.02                | 4019.66  | 0.00  |
| 2         | TLT    | 40       | 2020-04-03     | 147.598         | 6.05                | 5909.90  | 0.00  |
| 3         | SHV    | -41      | 2020-06-03     | 97.749          | 4.01                | -4007.62 | -7.85 |
| ...       | ...    | ...      | ...            | ...             | ...                 | ...      | ...   |

---

### performance_tables File Requirements

- **Filename**: `EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx`, Tabsheet= Performance
- **Header**: 
  - Cell A1 must display all main/default parameter settings in text format  
    Example: "st_lookback=30, mt_lookback=90, lt_lookback=180, execution_delay=1, top_n=5"

### Structure Requirements

1. **Parameter Columns** (left side):
   
   - Strategy
   - st_lookback
   - mt_lookback
   - lt_lookback
   - execution_delay
   - top_n
   - [Other parameters]

2. **Strategy Rows**:
   
   - One row per parameter combination during optimization
     - That means a new row for everytime a new parameter combination is generation (with all columns updated for those parameter outcomes)
   - All parameter values must be explicitly shown
   - 

3. **Metric Columns** (right side):
   
   - CAGR
   - Sharpe
   - Sortino
   - Max Drawdown
   - Win Rate
   - Final Value
   - YTD returns
   - Annual returns

### Formatting Rules

- **Percentages**: `0.00%`
- **Ratios**: `0.00`
- **Currency**: `$#,##0.00`
- **Benchmark Row**:
  - Appears exactly once at top
  - Full metrics, empty parameter columns

---

## Monthly & Annual Returns Graphic (PNG)

- **Report Name:** Monthly and Annual Actual Returns (PNG)
- **Output Format:** PNG image, stored in the report output folder alongside the Excel file.
- **Purpose:** Visual summary of the ACTUAL realized strategy returns for the main/default variable parameter settings, for the entire backtest period.
- **Content:**
  - **Monthly Returns:**
    - Displayed as a heatmap/matrix, with years as rows and months as columns.
    - Each cell shows the monthly return as a percentage (e.g., "2.40%"), color-coded from red (negative) to green (positive).
    - All values are actual, realized returns from the engine (not signals or intended allocations).
  - **Annual Returns:**
    - Displayed as a single column next to the monthly matrix, showing the total annual return for each year.
    - Same color-coding as monthly.
  - **Color Bar:**
    - Include a color bar/legend indicating the return scale (e.g., -5% to +8%).
- **Formatting:**
  - Title: "Monthly Returns" (centered above matrix)
  - Axis labels: Year (rows), Month (columns: Jan–Dec)
  - All values as percentages, with 2 decimals (e.g., "-5.81%")
  - No missing months/years; blank or gray out if no data
  - High DPI (at least 300, ideally 600) for print-quality output
- **File Naming:**
  - `EMA_V3_1_monthly_returns_YYYYMMDD_HHMMSS.png` (match Excel naming convention)
- **Audit Rule:**
  - Must match the actual realized returns in the Allocation History and Trade Log tabs for the main/default parameter settings
- **Example:**
  - ![Example Monthly/Annual Returns Heatmap](../docs/example_monthly_returns.png)

---

## Combined Cumulative Returns & Drawdown Graphic (PNG)

- **Report Name:** Combined Cumulative Returns and Drawdowns (PNG)
- **Output Format:** PNG image, stored in the report output folder alongside the Excel file.
- **Purpose:** Visual summary of the ACTUAL realized strategy performance for the main/default variable parameter settings, showing both cumulative returns and drawdowns in a single graphic for the entire backtest period.
- **Layout Requirements:**
  - The top panel displays cumulative returns for the strategy and benchmark (if present), using actual post-engine capital values.
  - The bottom panel displays drawdowns over the same period.
  - Both plots must share the same x-axis (Date), covering the full backtest period.
- **Parameter Display:**
  - The sub-title must show the start and end date of the backtest period (e.g., "2020-01-01 to 2025-05-01").
  - All main/default parameter settings (e.g., st_lookback=30, mt_lookback=90, etc.) must be displayed in a smaller but readable font at the bottom of the image.
- **Overlay Requirement:**
  - If possible, overlay arrows or markers on the cumulative returns plot to indicate each date when a remix/change in % allocation (from Allocation History) occurs.
  - Arrows/markers should be clear but not obscure the main trend lines.
- **File Naming:**
  - `EMA_V3_1_cumulative_returns_drawdown_YYYYMMDD_HHMMSS.png` (match Excel naming convention)
- **Audit Rule:**
  - Must match the actual realized returns and drawdowns in the Allocation History, Trade Log, and other reports for the main/default parameter settings.
- **Example:**
  - to be continued

---

## Implementation Rules

1. Never remove existing functionality
2. Never change column ordering
3. Never modify core formatting
4. All Reporting = Protected sections and require explicit approval before changing!


---

## v3_parameter_system_reference

*Source: `s:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\v3_parameter_system_reference.md`*

*Tags: documentation,parameter_system*

# V3 Parameter System Reference

This document provides a comprehensive reference for the V3 parameter system, including parameter classes, registry methods, parameter mappings, and implementation guidelines.

## Parameter System Core Components

### Parameter Registry Methods

| Method                                       | Description                                   | Usage                                                    |
| -------------------------------------------- | --------------------------------------------- | -------------------------------------------------------- |
| `register_parameter(group, parameter)`       | Register a single parameter with the registry | For registering individual parameters                    |
| `register_parameter_list(group, parameters)` | Register multiple parameters at once          | **Preferred method** for registering multiple parameters |
| `get_parameter(name, group=None)`            | Get a parameter by name                       | Retrieve a parameter for use                             |
| `get_parameters(group)`                      | Get all parameters in a group                 | Retrieve all parameters in a specific group              |
| `get_parameter_values(group)`                | Get parameter values as a dictionary          | Get parameter values for use in backtest                 |
| `get_core_parameters()`                      | Get all core parameters                       | Get engine parameters that are always present            |
| `get_strategy_parameters()`                  | Get all strategy parameters                   | Get parameters from groups starting with 'strategy_'     |
| `get_all_parameters()`                       | Get all parameters                            | Get all parameters from all groups                       |
| `clear_group(group)`                         | Clear all parameters in a group               | Used during re-registration of parameters                |

### Parameter Class Hierarchy

The V3 parameter system uses a class hierarchy to represent different types of parameters with varying behaviors:

```
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
├── ConfigParameter
└── StrategyOptimizeParameter
```

### Parameter Classes and Their Uses

| Class                         | Description                                         | GUI Visible | Optimizable | Reportable | Use Cases                                              |
| ----------------------------- | --------------------------------------------------- |:-----------:|:-----------:|:----------:| ------------------------------------------------------ |
| **BaseParameter**             | Base class for all parameters                       | Yes         | No          | No         | Default for any non-defined variables                  |
| **NumericParameter**          | For numeric values with min/max/step                | Yes         | Yes         | No         | Lookback periods, thresholds, execution delay          |
| **CategoricalParameter**      | For selection from fixed options                    | Yes         | Yes         | No         | Rebalance frequency, strategy selection                |
| **CategoricalListParameter**  | For user-defined groups in config files             | Yes         | Yes         | No         | Ticker groups, asset classes                           |
| **ConfigParameter**           | For config-only values, not in GUI                  | No          | No          | No         | Initial capital, commission rate, slippage rate        |
| **StrategyOptimizeParameter** | Enhanced strategy parameters with reporting control | Yes         | Yes         | Yes        | Strategy-specific parameters needing reporting control |

## Parameter Flow

```mermaid
graph TD
  GUI -->|Parameters| Engine
  Engine -->|Results + Parameters| V3_Reporting
  V3_Reporting -->|Formatted Output| Reports
```

Parameters flow through the system in the following way:

1. **Definition**: Parameters are defined in their respective registration functions
2. **Registration**: Parameters are registered with the V3 registry
3. **GUI Integration**: Parameters marked with `show_in_gui=True` appear in the GUI
4. **Value Setting**: Parameter values are set via GUI or directly in code
5. **Engine Usage**: Parameters are passed to the backtest engine
6. **Reporting**: Parameter values are included in performance reports

## Parameter Matrix

### Data Parameters (`data` group)

| Parameter          | Class            | Description                       | Default Value                         | GUI Visible | Optimizable | Status       |
| ------------------ | ---------------- | --------------------------------- | ------------------------------------- |:-----------:|:-----------:| ------------ |
| `data_storage_mode`| ConfigParameter  | Data storage mode                 | `'Read'`                              | No          | No          | Implemented  |
| `start_date`       | ConfigParameter  | Start date for backtest           | `'2020-01-01'`                        | No          | No          | Implemented  |
| `end_date`         | ConfigParameter  | End date for backtest             | `'2025-04-23'`                        | No          | No          | Implemented  |
| `price_field`      | ConfigParameter  | Price field for calculations      | `'Close'`                             | No          | No          | Implemented  |
| `risk_free_ticker` | ConfigParameter  | Ticker for risk-free rate         | `'^IRX'`                              | No          | No          | Implemented  |

### Core Engine Parameters (`core` group)

| Parameter          | Class                     | Description                       | Default Value | GUI Visible | Optimizable | Status       |
| ------------------ | ------------------------- | --------------------------------- | ------------- |:-----------:|:-----------:| ------------ |
| `initial_capital`  | ConfigParameter           | Initial capital for portfolio     | `1000000`     | No          | No          | Implemented  |
| `commission_rate`  | ConfigParameter           | Commission rate for trades        | `0.001`       | No          | No          | Implemented  |
| `slippage_rate`    | ConfigParameter           | Slippage rate for trades          | `0.001`       | No          | No          | Implemented  |
| `execution_delay`  | StrategyOptimizeParameter | Trade execution delay in days     | `1`           | Yes         | Yes         | Implemented  |
| `rebalance_freq`   | CategoricalParameter      | Portfolio rebalancing frequency   | `'weekly'`    | Yes         | Yes         | Implemented  |

### EMA Strategy Parameters (`strategy_ema` group)

| Parameter      | Class                     | Description                   | Default Value     | GUI Visible | Optimizable | Status      |
| -------------- | ------------------------- | ----------------------------- | ----------------- |:-----------:|:-----------:| ----------- |
| `st_lookback`  | StrategyOptimizeParameter | Short-term EMA lookback       | `20`              | Yes         | Yes         | Implemented |
| `mt_lookback`  | StrategyOptimizeParameter | Medium-term EMA lookback      | `60`              | Yes         | Yes         | Implemented |
| `lt_lookback`  | StrategyOptimizeParameter | Long-term EMA lookback        | `120`             | Yes         | Yes         | Implemented |
| `top_n`        | StrategyOptimizeParameter | Number of top assets to hold  | `2`               | Yes         | Yes         | Implemented |
| `signal_algo`  | StrategyOptimizeParameter | Signal generation algorithm   | `'ema_crossover'` | Yes         | Yes         | Implemented |
| `tickers`      | StrategyOptimizeParameter | Ticker group for strategy     | Ticker list       | Yes         | Yes         | Implemented |

### Reporting Parameters (Future Implementation)

| Parameter         | Class           | Description                      | Default Value                                            | GUI Visible | Optimizable | Status      |
| ----------------- | --------------- | -------------------------------- | -------------------------------------------------------- |:-----------:|:-----------:| ----------- |
| `create_excel`    | ConfigParameter | Whether to create Excel reports  | `True`                                                   | No          | No          | Planned     |
| `save_trade_log`  | ConfigParameter | Whether to save trade logs       | `True`                                                   | No          | No          | Planned     |
| `chart_dpi`       | ConfigParameter | DPI for output charts            | `300`                                                    | No          | No          | Implemented |
| `trade_log_format`| ConfigParameter | Format for trade log output      | `'csv'`                                                  | No          | No          | Implemented |
| `report_benchmark`| ConfigParameter | Benchmark ticker for comparison  | `'SPY'`                                                  | No          | No          | Implemented |
| `metrics`         | ConfigParameter | Performance metrics to include   | `['total_return', 'annualized_return', ..., 'win_rate']` | No          | No          | Planned     |

## V3 Reporting System

### Core Modules

1. **Parameter Registration** (`v3_engine/reporting_parameters.py`)
   - Central registry for all reporting parameters
   - Handles parameter types, defaults and validation
   - Integrated with main parameter registry

2. **Reporting Adapter** (`v3_engine/performance_reporter_adapter.py`)
   - Bridges V3 parameters to legacy reporting code
   - Handles parameter conversion and validation
   - Maintains backward compatibility

3. **Reporting Modules** (`v3_reporting/`)
   - `v3_performance_report.py`: Performance reporting wrapper
   - `v3_allocation_report.py`: Allocation reporting wrapper
   - `v3_visualization.py`: Chart generation wrapper

4. **Validation Utilities** (`v3_engine/data_validator.py`)
   - Validates data and signal/allocation history

### Error Handling Features

- Automatic parameter type conversion
- Fallback logic for missing signal history
- Graceful degradation when legacy features are unavailable

### Optimization Support

- Proper handling of parameter optimization tuples
- Preservation of optimization context through reporting chain
- Clear labeling of optimized parameters in output

## Parameter Conversion Guidelines

When converting config variables to parameter classes:

1. Use **ConfigParameter** for variables that:
   - Should not appear in the GUI
   - Should not be optimized
   - Are loaded from config
   - Should appear in performance reports

2. Use **NumericParameter** for variables that:
   - Have numeric values with min/max bounds
   - May need optimization
   - Should appear in the GUI

3. Use **CategoricalParameter** for variables that:
   - Have a fixed set of options
   - May need optimization over those options
   - Should appear in the GUI

4. Use **BaseParameter** as a fallback for any other variables

5. Use **StrategyOptimizeParameter** for strategy-specific parameters that:
   - Need reporting control
   - Are always reportable
   - Are always optimizable
   - Are always GUI visible

## Implementation Status and Next Steps

### Current Status

- Core parameter system implemented
- Parameter registry implemented
- EMA strategy parameters converted to V3 format
- GUI integration for core and EMA parameters
- Basic reporting adapter implemented

### Next Steps

1. **Complete Conversion of Legacy Parameters**
   - Convert remaining visualization parameters
   - Convert remaining reporting parameters
   - Ensure all data parameters use appropriate classes

2. **Strategy Parameter Standardization**
   - Create standardized structure for strategy parameter sets
   - Implement plug-and-play capability for new strategies
   - Design strategy selector for GUI

3. **Reporting System Enhancement**
   - Improve performance reporter adapter for better parameter handling
   - Enhance chart generation with V3 parameters
   - Standardize allocation reporting


---

