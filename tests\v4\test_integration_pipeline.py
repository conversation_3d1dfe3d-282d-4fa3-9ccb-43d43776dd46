# tests/v4/test_integration_pipeline.py

import logging
from pathlib import Path
import pandas as pd
import pytest
import sys
from datetime import date

# Add project paths for production modules
project_root = Path(__file__).resolve().parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(Path(__file__).resolve().parent / "production_modules"))

try:
    from run_v4_pipeline import run_pipeline
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.utils.trade_filter import filter_trades, is_business_day
    from v4.engine.data_loader_v4 import load_data_for_backtest
except ImportError as e:
    logger.error(f"Import error: {e}")
    logger.error(f"Current working directory: {Path.cwd()}")
    logger.error(f"Python paths: {sys.path}")
    raise

logger = logging.getLogger(__name__)

@pytest.fixture(scope="module")
def historical_window():
    """Fixture for test historical window settings using existing data."""
    window_settings = {
        "start_date": "2020-01-01",
        "end_date": "2020-02-28",  # Short window for testing
        "expected_tickers": ['SPY', 'SHV', 'EFA', 'TLT', 'PFF']
    }
    return window_settings


def test_full_signal_trading_pipeline(historical_window):
    """
    Test end-to-end pipeline execution using production flow.
    """
    # Load settings - use existing production settings
    settings = load_settings()
    
    # Run the full pipeline
    results = run_pipeline()
    
    # Basic assertions that pipeline completed and returned expected structure
    assert results is not None, "Pipeline did not return results"
    assert 'performance' in results, "Pipeline results missing performance data"
    assert 'trade_log' in results, "Pipeline results missing trade log"
    assert 'allocation_history' in results, "Pipeline results missing allocation history"
    
    logger.info(f"Pipeline completed successfully with {len(results['trade_log'])} trades")


def test_trade_deviation_threshold():
    """
    Test that trades only trigger if any asset deviates by more than 2% of total equity.
    """
    settings = load_settings()
    
    # Load price data for business day checking
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    
    # Define the deviation threshold
    deviation_threshold = 0.02  # 2% of total equity
    
    # Mock current portfolio allocations
    current_allocation = pd.Series({'SPY': 0.6, 'SHV': 0.4, 'EFA': 0.0, 'TLT': 0.0, 'PFF': 0.0})
    
    # Mock a proposed allocation with small deviations
    small_deviation = pd.Series({'SPY': 0.601, 'SHV': 0.399, 'EFA': 0.0, 'TLT': 0.0, 'PFF': 0.0})
    
    # Test: Allowance should not be triggered for small deviation
    result_small = filter_trades(
        current_allocation,
        small_deviation,
        price_data=price_data,
        trade_date=date(2020, 1, 2)
    )
    print(f"Small deviation allowed trades: {result_small['allowed']}")
    print(f"Small deviation filtered trades: {result_small['filtered']}")
    
    # Ensure that no trades are allowed
    assert result_small['allowed'].empty, "No trades should be allowed for small deviations."
    
    # Mock a proposed allocation with large deviations
    large_deviation = pd.Series({'SPY': 0.8, 'SHV': 0.0, 'EFA': 0.2, 'TLT': 0.0, 'PFF': 0.0})
    
    # Test: Allowance should trigger for large deviation
    result_large = filter_trades(
        current_allocation,
        large_deviation,
        price_data=price_data,
        trade_date=date(2020, 1, 2)
    )
    print(f"Large deviation allowed trades: {result_large['allowed']}")
    print(f"Large deviation filtered trades: {result_large['filtered']}")
    
    # Ensure that trades are allowed for large deviations
    assert not result_large['allowed'].empty, "Trades should be allowed for large deviations."
    
    logger.info("Trade deviation threshold correctly enforced.")


def test_non_price_data_days_skipped():
    """
    Test that non-price data days (weekends/holidays) are properly skipped.
    """
    settings = load_settings()
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    
    # Test specific dates we know should/shouldn't have price data
    # Weekends typically don't have price data
    weekend_date = date(2020, 1, 4)  # Saturday
    business_date = date(2020, 1, 2)  # Thursday
    
    # Check business day detection
    assert not is_business_day(weekend_date, price_data), "Weekend should not be a business day"
    assert is_business_day(business_date, price_data), "Thursday should be a business day"
    
    # Test filter_trades with non-business day
    current_allocation = pd.Series({'SPY': 0.6, 'SHV': 0.4})
    proposed_allocation = pd.Series({'SPY': 0.4, 'SHV': 0.6})
    
    result_weekend = filter_trades(
        current_allocation,
        proposed_allocation,
        price_data=price_data,
        trade_date=weekend_date
    )
    
    # Should return empty allowed trades for non-business days
    assert len(result_weekend['allowed']) == 0, "No trades should be allowed on non-business days"
    assert result_weekend['reason'] == 'Non-business day', "Reason should indicate non-business day"
    
    logger.info("Non-price data days correctly identified and skipped")


def test_real_data_integration():
    """
    Test using real historical data files to validate the pipeline.
    """
    # Check for existing signal and trade log files
    trace_dir = Path.cwd() / "v4_trace_outputs"
    signal_files = list(trace_dir.glob("signals_output_*.csv"))
    trade_files = list(trace_dir.glob("trade_log_*.csv"))
    
    assert len(signal_files) > 0, "No historical signal files found for testing"
    assert len(trade_files) > 0, "No historical trade log files found for testing"
    
    # Load one of the recent signal files
    signal_file = sorted(signal_files)[-1]  # Most recent
    signals = pd.read_csv(signal_file, index_col='Date', parse_dates=True)
    
    # Basic validation of signal file structure
    assert not signals.empty, "Signal file is empty"
    assert signals.sum(axis=1).min() > 0.99, "Signal weights should sum to approximately 1.0"
    assert signals.sum(axis=1).max() < 1.01, "Signal weights should sum to approximately 1.0"
    
    # Load corresponding trade file if available
    trade_file = sorted(trade_files)[-1]  # Most recent
    trades = pd.read_csv(trade_file, index_col=0, parse_dates=['date'])
    
    # Basic validation of trade structure
    assert not trades.empty, "Trade file is empty"
    assert 'ticker' in trades.columns, "Trade file missing ticker column"
    assert 'action' in trades.columns, "Trade file missing action column"
    assert 'quantity' in trades.columns, "Trade file missing quantity column"
    
    logger.info(f"Real data integration test passed with {len(signals)} signal periods and {len(trades)} trades")
