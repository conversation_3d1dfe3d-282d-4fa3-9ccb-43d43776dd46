# AI Maintenance Instructions - Streamlined

## SESSION WORKFLOW (3 Commands Only)

### SESSION START

**Command:** "START SESSION"
**Action:** Read these 3 files in order:

1. `memory-bank/current_session_context.md` (current status & priorities)
2. `memory-bank/execution_reference.md` (exact commands & locations)  
3. `memory-bank/codebase_map.md` (existing functions & connections)

### MID-SESSION UPDATE

**Command:** "MID UPDATE"
**Action:** Update `current_session_context.md` with:

- Tasks completed (mark ✅)
- Issues encountered (add to BLOCKERS)
- New discoveries (add to LEARNED)

### SESSION END

**Command:** "END SESSION"  
**Action:** 

1. Move completed work to `session_updates/YYYY-MM-DD_completed.md`
2. Reset `current_session_context.md` with remaining work
3. Update `codebase_map.md` if new functions/modules discovered

## FILE UPDATE RULES

### current_session_context.md

- **REPLACE don't append** - keep current only
- **Maximum 20 tasks** - if more, prioritize and move others to future
- **Status only:** TODO → DOING → DONE → BLOCKED

### execution_reference.md

- **Never delete** - only add new commands/locations
- **Test all commands** before documenting
- **Include exact file paths** and expected outputs

### codebase_map.md

- **Add new discoveries immediately**
- **Group by module/functionality**  
- **Include mermaid diagrams** for data flow

## ANTI-DUPLICATION RULES

1. **Before creating ANY function** - search codebase_map.md first
2. **Before adding ANY task** - check if already exists in current_session_context.md
3. **Before documenting anything** - verify it's not already documented

## SUCCESS METRIC

**New AI session can be productive in under 2 minutes of reading.**
