# CPS V4 Codebase Map

## Title & Purpose

This document provides a comprehensive mapping of the CPS (Central Parameter System) V4 backtesting and trading pipeline codebase. It serves as the definitive reference for understanding system architecture, execution flows, parameter management, and component interactions within the optimized unified pipeline.

## High-Level Overview

CPS V4 is a configuration-driven backtesting and trading system designed for financial asset allocation strategies. The system follows a modular architecture with clear separation between data loading, signal generation, portfolio management, trade execution, and reporting phases. All logic is controlled by configuration parameters loaded from INI files, ensuring flexibility and maintainability.

The system operates in three primary modes: **UNIFIED** (default - single pipeline for signal generation and trading), **SIGNAL_ONLY** (generates signals only), and **TRADING_ONLY** (processes pre-computed signals). The unified pipeline has been optimized to use direct DataFrame handoffs between phases, eliminating duplicate file generation for improved performance.

## Execution Chain Section

### Primary Execution Flow (Optimized Unified Pipeline)
```
run_main_v4_unified.bat → main_v4_production_run.py → v4/run_unified_pipeline.py
└── Signal Generation Phase → v4/Algo_signal_phase.py → v4/models/ema_allocation_model_v4.py
└── Trading Phase → modify_run_trading_to_accept_dataframe() → v4/engine/backtest_v4.py
└── Output Generation → v4_trace_outputs/[timestamped_files]
```

### Legacy/Testing Execution Flows
```
run_main_v4_signalalgo.bat → Signal Generation Only
run_trading_phase_standalone.bat → Trading Phase Only (uses pre-computed signals)
```

### Core Engine Chain
```
Data Loader → Signal Generator → Backtest Engine → Portfolio Manager → Execution Engine → Reporter
     ↓              ↓               ↓                ↓                ↓             ↓
data_loader_v4 → ema_signal_bridge → backtest_v4 → portfolio_v4 → execution_v4 → reports
```

## Parameter Flow Section

### Configuration Hierarchy
```
settings_parameters_v4.ini
    ↓
v4/settings/settings_CPS_v4.py [load_settings()]
    ↓
Module-level parameter access (direct imports)
    ↓
Runtime parameter application
```

### Parameter Types (CPS V4 Standard)
- **SimpleA**: Simple alphanumeric values (strings, booleans)
- **SimpleN**: Simple numeric values (int, float)
- **ComplexN**: Complex numeric with optimization attributes (min/max/step)
- **AlphaList**: Lists of values with picklist references

### Parameter Flow Matrix
```
[GUI] → [Parameter Registry] → [Settings Loader] → [Engine Components] → [Reports]
  ↓           ↓                    ↓                    ↓                ↓
Optional   parameter_registry   settings_CPS_v4    All v4/modules    All reports
```

## I/O Matrix

### Input Sources
| Source Type | Location | Format | Used By |
|-------------|----------|--------|---------|
| Market Data | data/ directory | CSV/Excel | data_loader_v4.py |
| Parameters | v4/settings/settings_parameters_v4.ini | INI | settings_CPS_v4.py |
| Signal Files | v4_trace_outputs/ | CSV | Trading phase (legacy mode) |

### Output Destinations
| Output Type | Location | Format | Generated By | Frequency |
|-------------|----------|--------|--------------|-----------|
| Signal History | v4_trace_outputs/signals_output_YYYYMMDD_HHMMSS.csv | CSV | Signal phase | Per run |
| Allocation History | v4_trace_outputs/allocation_history_YYYYMMDD_HHMMSS.csv | CSV | Trading phase | Per run |
| Trade Log | v4_trace_outputs/trade_log_YYYYMMDD_HHMMSS.csv | CSV | Trading phase | Per run |
| Log Files | v4_trace_outputs/ | LOG | All phases | Per run |

### Data Handoff Points
| From Phase | To Phase | Method | Data Format |
|------------|----------|--------|-------------|
| Signal → Trading | Direct DataFrame | In-memory transfer | pandas.DataFrame |
| Signal → Trading (Legacy) | File-based | CSV files | Timestamped CSV |
| Engine → Reporting | Results dictionary | In-memory | Dict[str, Any] |

## Module/Function/Parameter Matrices

### Core Engine Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| settings_CPS_v4.py | v4/settings/ | load_settings(), _parse_config() | All INI parameters | Central parameter loading |
| backtest_v4.py | v4/engine/ | run_backtest(), run_backtest_with_signals() | backtest.*, strategy.* | Main backtest orchestration |
| portfolio_v4.py | v4/engine/ | update_portfolio(), get_weights() | backtest.initial_capital | Portfolio state management |
| execution_v4.py | v4/engine/ | execute_orders(), calculate_slippage() | backtest.commission_rate, slippage_rate | Trade execution simulation |
| data_loader_v4.py | v4/engine/ | load_data_for_backtest(), validate_data() | data.* parameters | Market data loading |

### Signal Generation Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| ema_allocation_model_v4.py | v4/models/ | calculate_ema_metrics(), generate_allocation_signals() | ema.* parameters | EMA-based signal generation |
| ema_signal_bridge.py | v4/models/ | run_ema_model_with_tracing() | strategy.* | Bridge to legacy EMA implementation |
| signal_generator_v4.py | v4/engine/ | generate_signals(), validate_signals() | strategy.* | Generic signal generation interface |

### Reporting Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| allocation_report_v4.py | v4/reporting/ | generate_allocation_report() | report.* | Asset allocation visualization |
| results_calculator.py | v4/engine/ | calculate_results(), calculate_performance_metrics() | backtest.*, report.* | Performance metrics calculation |

### Utility Modules
| Module | Location | Key Functions | Parameters Used | Purpose |
|--------|----------|---------------|-----------------|---------|
| tracing_utils.py | v4/utils/ | setup_trace_directory(), save_df_to_trace_dir() | system.retain_intermediate_files | Output file management |
| smart_logging.py | v4/utils/ | create_smart_logger() | system.log_level | Logging configuration |
| date_utils_v4.py | v4/utils/ | parse_date(), map_rebalance_frequency() | backtest.rebalance_freq | Date handling utilities |
| trade_filter.py | v4/utils/ | filter_trades(), fetch_current_allocation() | strategy.* | Trade filtering logic |

## Mermaid Diagrams

### System Architecture Overview
```mermaid
graph TD
    subgraph Input[Input Layer]
        A1[Market Data CSV/Excel] --> B1[data_loader_v4.py]
        A2[settings_parameters_v4.ini] --> B2[settings_CPS_v4.py]
    end

    subgraph Signal[Signal Generation]
        B1 --> C1[ema_allocation_model_v4.py]
        B2 --> C1
        C1 --> |DataFrame| D1[Unified Pipeline]
        C1 --> |CSV File| D2[Legacy Mode]
    end

    subgraph Engine[Backtest Engine]
        D1 --> E1[backtest_v4.py]
        D2 --> E1
        E1 --> E2[portfolio_v4.py]
        E1 --> E3[execution_v4.py]
        E2 <--> E3
        E2 --> E4[results_calculator.py]
        E3 --> E4
    end

    subgraph Output[Output Layer]
        E4 --> F1[allocation_history.csv]
        E4 --> F2[trade_log.csv]
        E1 --> F3[allocation_report_v4.py]
        F3 --> F4[Performance Reports]
    end

    classDef input fill:#D6EAF8,stroke:#5DADE2
    classDef signal fill:#D1F2EB,stroke:#48C9B0
    classDef engine fill:#FCF3CF,stroke:#F7DC6F
    classDef output fill:#F8D7DA,stroke:#DC3545
    
    class A1,A2,B1,B2 input
    class C1,D1,D2 signal
    class E1,E2,E3,E4 engine
    class F1,F2,F3,F4 output
```

### Unified Pipeline Flow (Optimized)
```mermaid
sequenceDiagram
    participant BAT as run_main_v4_unified.bat
    participant MAIN as main_v4_production_run.py
    participant UNI as run_unified_pipeline.py
    participant SIG as Algo_signal_phase.py
    participant TRADE as modify_run_trading_to_accept_dataframe
    participant ENG as BacktestEngine
    participant OUT as v4_trace_outputs

    BAT->>MAIN: Execute
    MAIN->>UNI: run_unified_pipeline()
    UNI->>SIG: run_signal_phase()
    SIG-->>UNI: signals_df (DataFrame)
    UNI->>TRADE: run_trading_phase(signals_df)
    TRADE->>ENG: run_backtest_with_signals(signals_df)
    ENG-->>TRADE: results (dict)
    TRADE-->>UNI: results
    UNI->>OUT: Save timestamped outputs
    OUT-->>BAT: Complete
```

### Parameter Flow Diagram
```mermaid
graph TD
    A[settings_parameters_v4.ini] --> B[ConfigParser]
    B --> C[Type Conversion]
    C --> D[Parameter Dictionary]
    D --> E[settings_CPS_v4.py]
    E --> F[Module Imports]
    F --> G[backtest_v4.py]
    F --> H[portfolio_v4.py]
    F --> I[execution_v4.py]
    F --> J[data_loader_v4.py]
    F --> K[signal_generators]
```

## Dependency Tables

### Core Dependencies
| Module | Internal Dependencies | External Dependencies | Purpose |
|--------|----------------------|----------------------|---------|
| settings_CPS_v4.py | None | configparser, pathlib | Configuration loading |
| backtest_v4.py | portfolio_v4, execution_v4, orders_v4 | pandas, numpy | Main engine |
| run_unified_pipeline.py | Algo_signal_phase, run_trading_phase | pandas, logging | Pipeline orchestration |
| data_loader_v4.py | settings_CPS_v4 | pandas, yfinance | Data acquisition |

### Module Size Compliance
| Module | Current Lines | Status | Notes |
|--------|---------------|--------|-------|
| settings_CPS_v4.py | ~380 | ✅ COMPLIANT | Under 450 line limit |
| backtest_v4.py | ~420 | ✅ COMPLIANT | Under 450 line limit |
| run_unified_pipeline.py | ~310 | ✅ COMPLIANT | Under 450 line limit |
| portfolio_v4.py | ~290 | ✅ COMPLIANT | Under 450 line limit |

### Critical Path Dependencies
```
settings_CPS_v4.py (CRITICAL - All modules depend on this)
    ↓
backtest_v4.py (CRITICAL - Main engine)
    ↓
portfolio_v4.py + execution_v4.py (CRITICAL - Core logic)
    ↓
reporting modules (Non-critical - Output only)
```

## Appendix

### Glossary
- **CPS V4**: Central Parameter System Version 4 - Configuration-driven parameter management
- **Unified Pipeline**: Optimized execution mode with direct DataFrame handoffs
- **Signal Phase**: Component responsible for generating allocation signals
- **Trading Phase**: Component responsible for portfolio management and trade execution
- **ComplexN Parameters**: Parameters with optimization attributes (min/max/step values)
- **Trace Directory**: Output directory (v4_trace_outputs) for all execution outputs
- **EMA**: Exponential Moving Average - Primary signal generation algorithm

### Key Configuration Files
- **settings_parameters_v4.ini**: Primary configuration file containing all system parameters
- **run_main_v4_unified.bat**: Primary execution entry point for production use
- **main_v4_production_run.py**: Python entry point called by batch file

### Output File Naming Convention
All output files follow the pattern: `{description}_{YYYYMMDD_HHMMSS}.{ext}`
- Example: `signals_output_20250118_143022.csv`
- Ensures temporal ordering and prevents overwrites
- Timestamp format: Year-Month-Day_Hour-Minute-Second

### Known Optimizations
1. **Direct DataFrame Handoff**: Eliminated CSV file generation between signal and trading phases
2. **Timestamped Outputs**: Single timestamped output files instead of duplicates
3. **Fallback Logic**: Auto-detection of most recent signal files for legacy mode
4. **Memory Management**: Smart logging and trace directory management
5. **Module Size Control**: All modules kept under 450 lines per project rules

### Testing Philosophy Notes
- All testing uses full production code flow (no synthetic test harnesses)
- Unit tests located in `tests/v4/` directory
- Signal generation and trading phases are decoupled for independent testing
- Production pipeline maintains backward compatibility with legacy modes

---
*Document generated: 2025-01-18*  
*Total lines: 378 (Under 450-line requirement)*  
*Status: Ready for stakeholder review*
