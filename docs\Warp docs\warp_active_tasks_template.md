# Active Task List - Backtest Financial Asset Allocation

*Last Updated: {{ current_date }}*
*Session: {{ session_number }}*

## 🔥 Current Sprint (This Week)

### In Progress

- [ ] **[HIGH]** Fix allocation report improvements (generate_rebalance_report warnings)
  - Status: Code implemented but not working
  - Blocker: Output paths and naming conventions
  - Next: Debug warning messages

### Ready to Start

- [ ] **[HIGH]** Setup backtest graph outputs
  - Tasks: Cumulative returns, drawdowns, asset weights over time
  - Dependencies: None
  - Estimate: 2-3 sessions

## 📋 Backlog (Prioritized)

### High Priority

- [ ] Integrate detailed EMA debug outputs into performance reports
- [ ] Create rebalance period signal/asset allocation list report (XLSX)
- [ ] Ensure performance tab writes variables and default parameters
- [ ] Define standards for new Algos/Benchmarks/Ticker lists
- [ ] Produce weekly high-quality image files

### Medium Priority

- [ ] Implement categorical grouping for signal algorithms
- [ ] Add support for additional allocation strategies
- [ ] Enhance CLI options for backtest runs

### Low Priority

- [ ] Develop automated unit and integration tests
- [ ] Create interactive dashboards (Plotly/Dash)
- [ ] Research advanced optimization techniques

## 🐛 Known Issues

- [ ] **[CRITICAL]** Reporting system broken (v3)
- [ ] Chart generation warnings/errors (monthly returns, portfolio weights)
- [ ] Legacy parameter group warnings
- [ ] Pandas/matplotlib deprecation warnings

## ✅ Completed This Session

- None yet

## 📝 Session Notes

*Add key learnings, decisions, and context here*

## 🎯 Next Session Goals

1. 
2. 
3. 

---

## How to Use This Template in Warp Drive:

1. **Copy to Warp Drive**: Create a new document called "Active Task List - Backtest Project"
2. **Update Weekly**: Review and reprioritize tasks
3. **Daily Check-ins**: Update progress and session notes
4. **Archive Completed**: Move finished tasks to "Completed Archive" section
5. **Share Context**: Reference this document when starting new AI sessions
