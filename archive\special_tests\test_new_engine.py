"""
Test script for the improved backtest engine.
Demonstrates how to use the new engine with the existing EMA allocation model.
"""

import sys
import os
import pandas as pd
from datetime import date
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import local modules
from config.config import config
from config.paths import *  # All paths are handled here
from data.data_loader import load_data_for_backtest
from models.ema_allocation_model import ema_allocation_model
from engine.backtest import BacktestEngine
from reporting.performance_reporting import generate_performance_report_local

def adapter_ema_allocation(price_data, **params):
    """
    Adapter function to convert from price_data to the format expected by ema_allocation_model.
    
    Args:
        price_data (DataFrame): Historical price data
        **params: Additional parameters for the model
        
    Returns:
        dict: Asset weights dictionary
    """
    # Extract parameters
    st_lookback = params.get('st_lookback', 10)
    mt_lookback = params.get('mt_lookback', 50)
    lt_lookback = params.get('lt_lookback', 150)
    
    # Call the existing model
    weights = ema_allocation_model(
        price_data=price_data,
        returns_data=None,  # Not needed for EMA model
        st_lookback=st_lookback,
        mt_lookback=mt_lookback,
        lt_lookback=lt_lookback
    )
    
    return weights

def run_test():
    """Run a test of the new backtest engine."""
    logger.info("Starting test of new backtest engine")
    
    # Load data
    logger.info("Loading data...")
    data = load_data_for_backtest(config)
    price_data = data['price_data']
    
    # Create backtest engine
    engine = BacktestEngine(
        initial_capital=100000.0,
        commission_rate=0.001,  # 0.1%
        slippage_rate=0.001     # 0.1%
    )
    
    # Run backtest
    param_combinations = get_parameter_combinations(strategy_params)
    results = engine.run_backtest(
        price_data=price_data,
        signal_generator=adapter_ema_allocation,
        rebalance_freq=config['backtest_params']['rebalance_freq'],
        execution_delay=config['backtest_params']['execution_delay'],
        st_lookback=param_combinations['st_lookback'],
        mt_lookback=param_combinations['mt_lookback'],
        lt_lookback=param_combinations['lt_lookback']
    )
    
    # Print summary
    logger.info("Backtest results:")
    logger.info(f"Initial capital: ${results['initial_capital']:,.2f}")
    logger.info(f"Final value: ${results['final_value']:,.2f}")
    logger.info(f"Total return: {results['total_return']:.2%}")
    logger.info(f"CAGR: {results['performance']['cagr']:.2%}")
    logger.info(f"Volatility: {results['performance']['volatility']:.2%}")
    logger.info(f"Sharpe ratio: {results['performance']['sharpe']:.2f}")
    logger.info(f"Max drawdown: {results['performance']['max_drawdown']:.2%}")
    
    # Generate performance report
    try:
        # Extract strategy parameters for the performance table
        strategy_params = {
            'st_lookback': config['strategy_params'].get('st_lookback', ''),
            'mt_lookback': config['strategy_params'].get('mt_lookback', ''),
            'lt_lookback': config['strategy_params'].get('lt_lookback', ''),
            'top_n': config['strategy_params'].get('top_n', ''),
            'execution_delay': config['backtest_params'].get('execution_delay', '')
        }
        
        # Generate performance report with all data directly
        report_path = generate_performance_report_local(
            data_dict={
                'returns': results['strategy_returns'],
                'weights_history': results['weights_history'],
                'signal_history': results['signal_history'],
                'benchmark_returns': None,  # No benchmark in this test
                'strategy_params': strategy_params,
                # Create portfolio value data for the report
                'portfolio_value': pd.DataFrame((1 + results['strategy_returns']).cumprod()),
                'metrics': pd.DataFrame.from_dict(results['performance'], orient='index', columns=['Value'])
            }, 
            output_dir=OUTPUT_DIR,
            filename_prefix=f"new_engine_{config['backtest_params']['strategy']}"
        )
        
        logger.info(f"Generated performance report: {report_path}")
        
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        import traceback
        traceback.print_exc()
    
    return results

if __name__ == "__main__":
    run_test()
