# V3 Documentation Recap

## Core Documentation Files

### Architecture

- `docs/v3_module_mapping.md`: Module relationships and responsibilities
- `memory-bank/systemPatterns.md`: Technical design patterns

### Parameter System

- `docs/v3_parameter_classification.md`: Numeric vs categorical parameters
- `docs/v3_parameter_flow.md`: How parameters move through system
- `memory-bank/activeContext.md`: Current parameter implementation status

### Reporting

- `ocs/v3_performance_reporting_standards_a.md`: Output standards and Exact Reports to produce
- `memory-bank/progress.md`: Reporting feature status

### Transition

- `docs/v3_transition_plan.md`: Migration strategy
- `docs/v3_user_guide.md`: Usage instructions

## Critical Implementation Files

- `Engine_Modules_Functions_List_v3.md`: Detailed technical specs of all engine components
- `full_Process Flow_Full Architecture_v3.md`: Complete system architecture diagram

```mermaid
graph TD
    A[Parameter Classification] --> B[Parameter Flow]
    B --> C[Module Mapping]
    C --> D[Reporting Standards]
    D --> E[User Guide]
```

## When to Use Each Document

1. **Designing new features**: Start with systemPatterns.md + module_mapping.md
2. **Adding parameters**: Reference parameter_classification.md + parameter_flow.md
3. **Reporting changes**: Check performance_reporting_standards_a.md
4. **Migration work**: Review transition_plan.md

## Workflow Rules

1. ALWAYS search for existing files before considering creation
2. NEVER create new files for existing concepts - only enhance current documentation
3. Use `find_by_name` tool to verify file existence before editing

## Maintenance Tips

- Update progress.md when completing features
- Keep activeContext.md current with implementation details
- Cross-link related documents for easy navigation
