"""
Data loader module for financial asset allocation backtesting.
Directly leverages the Custom Function Library data retrieval components.
"""

import pandas as pd
import logging
import sys
import os
from pathlib import Path
from config.paths import DATA_DIR  # Import centralized path configuration
from config.config import config
from config.paths import CUSTOM_LIB_PATH
from CPS_v4.settings_CPS_v4 import load_settings  # NEW
settings = load_settings()  # NEW
# Extract data parameters from CPS v4
data_params = settings.get('data_params', {})  # NEW
tickers = data_params.get('tickers', [])  # NEW
start_date = data_params.get('start_date', None)  # NEW
end_date = data_params.get('end_date', None)  # NEW
price_field = data_params.get('price_field', 'Close')  # NEW
mode = data_params.get('data_storage_mode', 'Save')  # NEW

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from Custom Function Library
try:
    # Import directly from the modules
    from market_data import data_fetch_stock_data
    from risk_free_rate import get_risk_free_rate as lib_get_risk_free_rate
    
    logger.info("Successfully imported data functions from Custom Function Library")
except ImportError as e:
    logger.error(f"Failed to import from Custom Function Library: {e}")
    raise

# Import the date utilities
from utils.date_utils import (
    standardize_date, 
    standardize_date_range,
    standardize_dataframe_index,
    filter_dataframe_by_dates,
    date_to_str,
    create_date_range
)

def get_adjusted_close_data(tickers, start_date, end_date=None, price_field='Close'):
    try:
        start_ts, end_ts = standardize_date_range(start_date, end_date)
        
        price_data = pd.DataFrame()
        for ticker in tickers:
            try:
                data = data_fetch_stock_data(
                    ticker=ticker,
                    period="max",
                    interval="1d"
                )
                
                # Remove timezone from timestamps if present
                if isinstance(data.index, pd.DatetimeIndex) and data.index.tz is not None:
                    data.index = data.index.tz_localize(None)
                
                data = standardize_dataframe_index(data)
                data = filter_dataframe_by_dates(data, start_ts, end_ts)
                
                # Try preferred price field first, then fall back to alternatives
                price_fields = [price_field, 'Adj Close', 'Close']
                for field in price_fields:
                    if field in data.columns:
                        price_data[ticker] = data[field]
                        break
                else:
                    logger.warning(f"No valid price fields found in data for {ticker}")
                    
            except Exception as e:
                logger.error(f"Error retrieving data for {ticker}: {e}")
                
        price_data = standardize_dataframe_index(price_data)
        
        if len(price_data) == 0:
            logger.warning("No price data retrieved. Creating empty DataFrame with DatetimeIndex.")
            price_data = pd.DataFrame(index=pd.DatetimeIndex([]))
        
        return price_data
    
    except Exception as e:
        logger.error(f"Error retrieving price data: {e}")
        raise

def load_ticker_data():  # updated to use CPS v4 settings (removed config_v2)
    """
    Manage ticker data according to 'data_storage_mode' in config['data_params'].
    Modes:
        - 'Save': Download using get_adjusted_close_data, save to XLSX, return DataFrame
        - 'Read': Load from XLSX, error if missing
        - 'New': Download using get_adjusted_close_data, do NOT save, return DataFrame
    Logs start and completion. Returns DataFrame matching get_adjusted_close_data.
    """
    import pandas as pd
    import os
    from pathlib import Path
    
    # Using CPS v4-loaded tickers, dates, mode, and price_field
    # Use the centralized DATA_DIR path from config/paths.py
    data_dir = DATA_DIR
    data_dir.mkdir(exist_ok=True)  # Ensure the directory exists
    
    # Create the expected file name
    file_name = f"tickerdata_{'_'.join(tickers)}_{start_date}_{end_date}.xlsx"
    xlsx_path = data_dir / file_name
    
    # Log the absolute path being used
    logger.info(f"[TickerData] Using data file at: {xlsx_path.resolve()}")
    
    logger.info(f"[TickerData] Starting data load in mode: {mode}")
    print(f"[TickerData] Using data file path: {xlsx_path.resolve()}")
    
    # Strict behavior based on mode
    if mode == 'Read':
        # In Read mode - only read from file, no fallbacks
        if not xlsx_path.exists():
            logger.error(f"[TickerData] Read mode failed - file not found: {xlsx_path}")
            raise FileNotFoundError(f"Ticker data file not found in Read mode: {xlsx_path}")
        
        df = pd.read_excel(xlsx_path, index_col=0, parse_dates=True)
        logger.info(f"[TickerData] Data loaded from {xlsx_path}")
        
    elif mode in ('Save', 'New'):
        # In Save/New modes - download fresh data
        logger.info(f"[TickerData] Downloading fresh data for {tickers} ({mode} mode)")
        df = get_adjusted_close_data(tickers, start_date, end_date, price_field)
        
        if mode == 'Save':
            df.to_excel(xlsx_path)
            logger.info(f"[TickerData] Data saved to {xlsx_path}")
    
    logger.info("[TickerData] Completed data load.")
    return df


def get_returns_data(price_data):
    """
    Calculate returns from price data.
    
    Args:
        price_data (DataFrame): DataFrame of prices
        
    Returns:
        DataFrame: DataFrame of returns
    """
    returns = price_data.pct_change()
    returns = returns.fillna(0)
    return returns


def get_risk_free_rate(risk_free_ticker='^IRX', start_date=None, end_date=None):
    """
    Get risk-free rate data.
    MODIFIED: Now returns zeros to prevent data fetching.
    
    Args:
        risk_free_ticker (str): Ticker for risk-free rate (ignored)
        start_date (str): Start date in format 'YYYY-MM-DD'
        end_date (str): End date in format 'YYYY-MM-DD'
        
    Returns:
        Series: Risk-free rate series with DatetimeIndex (all zeros)
    """
    logger.info("Using zero risk-free rate to prevent data fetching")
    
    try:
        # Standardize dates
        start_ts, end_ts = standardize_date_range(start_date, end_date)
        
        # Create a date range
        if start_date and end_date:
            # Create a proper date range using our utility
            date_range = create_date_range(start_date, end_date)
            return pd.Series(0.0, index=date_range)
        else:
            # If no dates provided, return empty series
            return pd.Series(0.0, index=pd.DatetimeIndex([]))
            
    except Exception as e:
        logger.warning(f"Error creating date range for risk-free rate: {e}. Using empty series.")
        return pd.Series(0.0, index=pd.DatetimeIndex([]))


def load_data_for_backtest(config):
    """
    Load all necessary data for backtesting based on configuration.
    
    Args:
        config (dict): Configuration dictionary
        
    Returns:
        dict: Dictionary containing price_data, returns_data, risk_free_rate, and benchmark_returns
    """
    data_params = config['data_params']
    backtest_params = config['backtest_params']
    
    # Force data_storage_mode to 'Read' to ensure we use stored data
    data_params['data_storage_mode'] = 'Read'
    logger.info(f"Enforcing data_storage_mode='Read' to use stored data file")
    
    # Load price data using load_ticker_data with 'Read' mode
    try:
        price_data = load_ticker_data()
        logger.info(f"Successfully loaded price data from stored file with shape {price_data.shape}")
    except FileNotFoundError as e:
        logger.error(f"CRITICAL ERROR: Could not find stored data file: {e}")
        logger.error(f"Please ensure the data file exists in the DATA_DIR path")
        raise
    
    # Calculate returns
    returns_data = get_returns_data(price_data)
    
    # For risk-free rate, use zeros instead of fetching
    risk_free_rate = pd.Series(0.0, index=price_data.index)
    logger.info(f"Using zero risk-free rate to avoid data fetching")
    
    # For benchmark returns, use the benchmark ticker from the already loaded price data
    benchmark_returns = None
    if 'benchmark' in backtest_params and backtest_params['benchmark']:
        benchmark_ticker = backtest_params['benchmark']
        if benchmark_ticker in price_data.columns:
            benchmark_returns = price_data[benchmark_ticker].pct_change().fillna(0)
            logger.info(f"Benchmark returns calculated for {benchmark_ticker} from loaded data")
        else:
            logger.warning(f"Benchmark ticker {benchmark_ticker} not found in loaded price data")
    
    return {
        'price_data': price_data,
        'returns_data': returns_data,
        'risk_free_rate': risk_free_rate,
        'benchmark_returns': benchmark_returns
    }
