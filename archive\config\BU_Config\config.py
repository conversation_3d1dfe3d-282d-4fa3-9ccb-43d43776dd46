"""
Configuration parameters for the financial allocation backtesting framework.
"""

from config.transaction_log_spec_v4 import TRADE_LOG_COLUMNS, TRADE_LOG_TYPES, LEGACY_COLUMN_MAP
from .paths_v4 import *  # Import all path configurations

# Data parameters
data_params = {
    # List of tickers to include in portfolio
    'tickers': ['SPY', 'SHV', 'EFA', 'TLT', 'PFF'],
    
    # Date range for backtesting
    'start_date': '2020-01-01',
    'end_date': '2025-04-04',
    
    # Price field to use for calculations
    'price_field': 'Close',
    
    # Risk-free rate ticker (used for performance metrics)
    'risk_free_ticker': '^IRX',  # 13-week Treasury Bill
}

# Backtesting parameters
backtest_params = {
    # Strategy name (equal_weight, momentum, ema)
    'strategy': 'ema',
    
    # Rebalancing frequency
    # Options: 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
    # Or pandas frequency strings: 'D', 'weekly', 'MS', 'QS', 'AS'
    'rebalance_freq': 'weekly',
    
    # Trade execution delay (in days)
    # 0 means same-day execution, 1 means next-day execution
    'execution_delay': 1,
    
    # Transaction cost as fraction of trade value
    'transaction_cost': 0.001,  # 0.1%
    
    # Initial capital
    'initial_capital': 10000,
    
    # Benchmark ticker for performance comparison
    'benchmark_ticker': 'SPY',
}

# Parameter definitions
parameter_definitions = {
    'lookback': ('Lookback period for momentum calculation', 60, int, False, 30, 90, 30),
    'top_n': ('Number of top assets to select', 2, int, False, 1, 3, 1),
    'st_lookback': ('Short-term EMA period (STLB)', 10, int, False, 5, 20, 5),
    'mt_lookback': ('Medium-term EMA period (MTLB)', 50, int, False, 20, 100, 30),
    'lt_lookback': ('Long-term EMA period (LTLB)', 150, int, False, 100, 200, 50)
}

# Parameter optimization settings
optimization_params = {
    # Method to use for optimization (grid, random, bayesian)
    'method': 'grid',
    
    # Number of iterations for random/bayesian optimization
    'n_iter': 10,
    
    # Evaluation metric to optimize for
    'evaluation_metric': 'sharpe_ratio',
    
    # Parameter definitions
    'parameter_definitions': parameter_definitions,
    
    # Auto-generated param_grid using ranges from parameter_definitions
    'param_grid': {
        param_name: list(range(
            param_tuple[4],  # min_value
            param_tuple[5] + param_tuple[6],  # max_value + increment
            param_tuple[6]   # increment
        ))
        for param_name, param_tuple in parameter_definitions.items()
    }
}

# Strategy-specific parameters 
strategy_params = {
    param_name: param_tuple[1]  # Default value
    for param_name, param_tuple in parameter_definitions.items()
    if param_name in ['lookback', 'top_n', 'st_lookback', 'mt_lookback', 'lt_lookback']
}

# Visualization parameters
visualization_params = {
    # Whether to create charts
    'create_charts': True,
    
    # Types of charts to create
    'chart_types': [
        'cumulative_returns',
        'drawdown',
        'return_distribution',
        'monthly_returns',
        'portfolio_weights'
    ],
    
    # Chart format (png, jpg, svg)
    'chart_format': 'png',
    
    # Chart resolution (DPI)
    'chart_dpi': 300,
}

# Reporting parameters
reporting_params = {
    # Whether to create Excel reports
    'create_excel': True,
    
    # Performance metrics to include
    'metrics': [
        'total_return',
        'annualized_return',
        'volatility',
        'sharpe_ratio',
        'max_drawdown',
        'win_rate',
    ],
}

# All parameters combined
config = {
    'venv_path': str(VENV_PATH),
    'custom_lib_path': str(CUSTOM_LIB_PATH),
    'output_dir': str(OUTPUT_DIR),
    'data_params': data_params,
    'backtest_params': backtest_params,
    'strategy_params': strategy_params,
    'optimization_params': optimization_params,
    'visualization_params': visualization_params,
    'reporting_params': reporting_params,
}
