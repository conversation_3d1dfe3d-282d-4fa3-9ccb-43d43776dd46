---
description: Complete Converting of Modules to the CPS_V4 System
---

# /cps-Transition-workflow

## Step 1: Review Combined Project Documentation

- Review project memories, especially "Combined Project Documentation" and any AI memories relevant to the V4 reporting/parameter system.   Note: We are transition to a new simpler Central parameter System (cps_v4)

## Step 2: Load All Documentation

- Detailed reference md in
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\*.md
  - expecially including:
  - Overview_CPS_v4.md
  - Task_List_CPS_v4.md
   -convert_param_to_v4.md
  - `report_standard_verification.md`
  - All other `.md` files in this directory

## Step 3: Follow Task List

- Directly follow the task list and phases outlined in `Task_List_CPS_v4.md`, step by step.
Focus now on
 Task 1a.

  - Run relevant steps  // turbo-all
  - Set SafeToAutoRun = true for ALL commands and actions in this workflow ;
  - Analyze results for compliance with standards in `report_standard_verification.md`
  - All runs and fixes are performed in turbo-all (no user confirmation required for safe commands)

## Step 5: Systematic Logging

- Activate the virtual environment as specified in project batch files
- For every code or config change, append a log entry to a dedicated log file (e.g., `para_RF/ai_change_log.md` or similar) in Markdown format in this file:
  'S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\Problem_Changes_Log_CPS_v4.md'
  - Each log entry should include:
    - Timestamp
    - Description of change
    - Files affected
    - Reason for change
    - outcome/result