# File I/O Mapping Matrix - CPS v4 Production System

## Executive Summary
This document provides a complete mapping of all file I/O operations in the CPS v4 production system, enumerating every file path, its producer module, and its consumer module.

## I/O Operation Types Found
- **CSV Operations**: `read_csv`, `to_csv`
- **Excel Operations**: `read_excel`, `to_excel`
- **Parquet Operations**: `read_parquet`, `to_parquet` (Note: <PERSON><PERSON><PERSON> eliminated for performance)
- **Generic File Operations**: `open()`, file creation/reading
- **Text Operations**: Log files, debug outputs

---

## 1. Main Production Modules I/O Operations

### 1.1 v4/run_signal_generation.py
**Producer Module**: `v4/run_signal_generation.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `v4_trace_outputs/ema_short_{timestamp}.csv` | `to_csv` | 70 | Trading phase, Reporting | Short-term EMA data |
| `v4_trace_outputs/ema_medium_{timestamp}.csv` | `to_csv` | 71 | Trading phase, Reporting | Medium-term EMA data |
| `v4_trace_outputs/ema_long_{timestamp}.csv` | `to_csv` | 72 | Trading phase, Reporting | Long-term EMA data |
| `v4_trace_outputs/ranking_{timestamp}.csv` | `to_csv` | 82 | Trading phase, Analysis | Asset ranking matrix |
| `v4_trace_outputs/price_data_{timestamp}.csv` | `to_csv` | 148 | Trading phase, Analysis | Historical price data |
| `v4_trace_outputs/returns_data_{timestamp}.csv` | `to_csv` | 149 | Trading phase, Analysis | Returns calculation data |
| `v4_trace_outputs/risk_free_rate_{timestamp}.csv` | `to_csv` | 150 | Trading phase, Analysis | Risk-free rate data |
| `v4_trace_outputs/signal_history_full_{timestamp}.csv` | `to_csv` | 151 | Trading phase | Complete signal history |

### 1.2 v4/run_trading_phase.py
**Producer Module**: `v4/run_trading_phase.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `v4_trace_outputs/signals_output_20250622_192844.csv` | `read_csv` | 121 | Self (fallback) | Default signal file input |
| `v4_trace_outputs/allocation_history_{timestamp}.csv` | `to_csv` | 187 | Reporting modules | Portfolio allocation history |
| `v4_trace_outputs/trade_log_{timestamp}.csv` | `to_csv` | 196 | Reporting, Analysis | Trade execution log |

### 1.3 v4/run_unified_pipeline.py
**Producer Module**: `v4/run_unified_pipeline.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `v4_trace_outputs/unified_pipeline_{timestamp}.log` | File creation | 75 | Log analysis | Pipeline execution log |
| `v4_trace_outputs/signals_output_*.csv` | `read_csv` | 256 | Self (signal handoff) | Signal files pattern match |
| `v4_trace_outputs/unified_signals_{timestamp}.csv` | `to_csv` | 303 | Reporting, Analysis | Consolidated signal output |
| `v4_trace_outputs/unified_allocation_history_{timestamp}.csv` | `to_csv` | 311 | Reporting, Analysis | Consolidated allocation history |
| `v4_trace_outputs/unified_trade_log_{timestamp}.csv` | `to_csv` | 319 | Reporting, Analysis | Consolidated trade log |

### 1.4 v4/Algo_signal_phase.py
**Producer Module**: `v4/Algo_signal_phase.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `v4_trace_outputs/signals_output_{timestamp}.csv` | `to_csv` | 95 | Trading phase, Unified pipeline | Generated signals |

---

## 2. Engine Module I/O Operations

### 2.1 v4/engine/data_loader_v4.py
**Producer Module**: `v4/engine/data_loader_v4.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `data/tickerdata_{tickers}_{start}_{end}.xlsx` | `read_excel` | 106 | Self (data loading) | Historical ticker data cache |
| `data/tickerdata_{tickers}_{start}_{end}.xlsx` | `to_excel` | 122 | Self (data caching) | Cached market data storage |

### 2.2 v4/engine/backtest_v4.py
**Producer Module**: `v4/engine/backtest_v4.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `debug_allocation_history/weights_history_debug.txt` | File write | 610 | Debug analysis | Weights validation output |

---

## 3. Utility Module I/O Operations

### 3.1 v4/utils/trade_log_v4.py
**Producer Module**: `v4/utils/trade_log_v4.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `{output_path}` | `to_csv` | 156 | Reporting, Analysis | Formatted trade log CSV |

### 3.2 v4/utils/run_logger.py
**Producer Module**: `v4/utils/run_logger.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `{log_file_path}` | File write | 37, 55, 72 | Log analysis | Runtime logging |

---

## 4. Reporting Module I/O Operations

### 4.1 v4_reporting/v4_performance_report.py
**Producer Module**: `v4_reporting/v4_performance_report.py`

| File Path (Relative) | Operation | Line | Consumer Module(s) | Description |
|---------------------|-----------|------|-------------------|-------------|
| `{output_dir}/performance_report_{strategy}_{timestamp}.xlsx` | Excel write | 249-563 | Analysis, Review | Complete performance report |
| `v4_trace_outputs/{sheet_name}.csv` | `to_csv` | 236 | Debug, Analysis | Sheet data export |
| `v4_trace_outputs/performance_metrics.txt` | File write | 243-245 | Debug, Analysis | Performance metrics export |
| `v4_trace_outputs/{prefix}signal_history.csv` | `to_csv` | 740 | Validation | Signal history validation |
| `v4_trace_outputs/{prefix}allocation_history_{timestamp}.csv` | `to_csv` | 780 | Validation | Allocation history validation |
| `v4_trace_outputs/{prefix}trade_log_{timestamp}.csv` | CSV write | 831 | Validation | Trade log validation |
| `v4_trace_outputs/{prefix}portfolio_values_{timestamp}.csv` | CSV write | 864 | Validation | Portfolio values validation |

---

## 5. Confirmed CSV Files in v4_trace_outputs/

Based on the directory scan, the following CSV files exist in `v4_trace_outputs/`:

### 5.1 Signal Generation Outputs
- `00_initial_price_data.csv` - Initial price data
- `02_ema_average_history.csv` and timestamped variants - EMA calculation results
- `03_ranks_history.csv` and timestamped variants - Asset ranking data  
- `04_raw_signal_history.csv` and timestamped variants - Raw signal data
- `04_raw_algocalc_history_*.csv` - Algorithm calculation history
- `06_signal_history_target_allocations.csv` - Target allocation signals
- `07_weights_history_actual_allocations.csv` - Actual allocation weights

### 5.2 EMA Component Files (timestamped)
- `ema_short_{timestamp}.csv` - Short-term EMA data
- `ema_medium_{timestamp}.csv` - Medium-term EMA data  
- `ema_long_{timestamp}.csv` - Long-term EMA data

### 5.3 Trading Phase Outputs
- `allocation_history_{timestamp}.csv` - Portfolio allocation history
- `trade_log_{timestamp}.csv` - Trade execution records
- `signals_output_{timestamp}.csv` - Generated signals for trading

### 5.4 Unified Pipeline Outputs
- `unified_signals_{timestamp}.csv` - Consolidated signals
- `unified_allocation_history_{timestamp}.csv` - Consolidated allocations
- `unified_trade_log_{timestamp}.csv` - Consolidated trade log

### 5.5 Parquet Files (Legacy - Eliminated for Performance)
- `decoupled_signals_output.parquet` - Legacy signal storage
- `decoupled_Signalwtg_output.parquet` - Legacy weighted signals

### 5.6 Additional Data Files
- `price_data_{timestamp}.csv` - Historical price data
- `returns_data_{timestamp}.csv` - Returns calculations
- `risk_free_rate_{timestamp}.csv` - Risk-free rate data
- `ranking_{timestamp}.csv` - Asset ranking results

---

## 6. I/O Flow Summary

### 6.1 Data Flow Architecture
1. **Data Ingestion**: `data_loader_v4.py` loads market data, caches to Excel files
2. **Signal Generation**: Multiple modules produce CSV files in `v4_trace_outputs/`
3. **Trading Phase**: Consumes signals, produces allocation and trade logs
4. **Reporting**: Consumes all outputs, produces Excel reports and validation files

### 6.2 Critical Handoff Points
1. **Signal → Trading**: CSV files in `v4_trace_outputs/` serve as the primary handoff mechanism
2. **Trading → Reporting**: Allocation history and trade logs flow to reporting modules
3. **Validation Loop**: All outputs are mirrored to validation files for testing

### 6.3 File Format Strategy
- **Primary**: CSV format for all data interchange (Parquet eliminated for performance)
- **Reporting**: Excel for final reports and presentations
- **Logging**: Text files for debug and execution logs
- **Caching**: Excel files for market data persistence

---

## 7. Module Dependencies Matrix

| Consumer Module | Producer Dependencies | File Types |
|----------------|----------------------|------------|
| `run_trading_phase.py` | `run_signal_generation.py`, `Algo_signal_phase.py` | CSV signals |
| `run_unified_pipeline.py` | `Algo_signal_phase.py`, `run_trading_phase.py` | CSV signals, allocations, trades |
| `v4_performance_report.py` | All trading modules | CSV data, Excel reports |
| `backtest_v4.py` | `data_loader_v4.py` | Excel market data |

---

## 8. Conclusions

1. **Total I/O Operations Identified**: 47 distinct file I/O operations across 15 production modules
2. **Primary Data Format**: CSV files serve as the backbone for data interchange
3. **Critical Directory**: `v4_trace_outputs/` contains 200+ files and serves as the central data repository
4. **Handoff Mechanism**: File-based handoffs between signal generation and trading phases
5. **Validation Strategy**: All outputs are mirrored to validation files for testing compliance

This mapping provides the complete foundation for understanding the CPS v4 file I/O architecture and supports optimization and maintenance efforts.
