# AI Maintenance Instructions - Consolidated

## PURPOSE

These instructions ensure project documentation stays current across AI sessions without manual intervention.

## STREAMLINED SESSION WORKFLOW (3 Commands Only)

### SESSION START

**Command:** "START SESSION"
**Action:** Read these 3 files in order:

1. `memory-bank/current_session_context.md` (current status & priorities)
2. `memory-bank/execution_reference.md` (exact commands & locations)  
3. `memory-bank/codebase_map.md` (existing functions & connections)

Then summarize:
- Current priorities (top 3 tasks)
- Any blockers from previous session
- Session goals for today

### MID-SESSION UPDATE

**Command:** "MID UPDATE"
**Action:** Update `memory-bank/current_session_context.md` with:

- <PERSON> completed tasks with ✅
- Add any new issues to BLOCKERS section
- Add discoveries to LEARNED THIS SESSION
- Update NEXT SESSION GOALS if priorities changed

Be specific about what was accomplished.

### SESSION END

**Command:** "END SESSION"  
**Action:** 

1. Move completed work to `memory-bank/session_updates/YYYY-MM-DD_completed.md`
2. Reset `memory-bank/current_session_context.md` with remaining work
3. Update `memory-bank/codebase_map.md` if new functions/modules discovered
4. Update `memory-bank/execution_reference.md` with any new commands/paths

## FILE UPDATE RULES

### current_session_context.md

- **REPLACE don't append** - keep current only
- **Maximum 20 tasks** - if more, prioritize and move others to future
- **Status only:** TODO → DOING → DONE → BLOCKED

### execution_reference.md

- **Never delete** - only add new commands/locations
- **Test all commands** before documenting
- **Include exact file paths** and expected outputs

### codebase_map.md

- **Add new discoveries immediately**
- **Group by module/functionality**  
- **Include mermaid diagrams** for data flow

## ANTI-DUPLICATION RULES

1. **Before creating ANY function** - search codebase_map.md first
2. **Before adding ANY task** - check if already exists in current_session_context.md
3. **Before documenting anything** - verify it's not already documented

## DURING SESSION TRACKING

**Track these items continuously:**

- Tasks completed (even partial progress)
- New issues discovered
- Technical decisions made
- Changes to priorities or scope
- Blockers encountered
- Solutions found

## TRIGGER PHRASES

When the user says these phrases, perform the specified actions immediately:

- **"Update project status"** → Update progress.md and task priorities
- **"End session summary"** → Perform all SESSION END actions above
- **"Plan update"** → Revise plans based on new information
- **"Document decision"** → Add entry to decisions_log.md

## CRITICAL RULES

1. **NEVER skip session end updates** - they maintain project continuity
2. **Be specific** - avoid vague entries like "worked on X"
3. **Preserve history** - don't delete previous entries, append new ones
4. **Date everything** - use YYYY-MM-DD format consistently
5. **Cross-reference** - link related tasks and issues

## FILE OWNERSHIP

**AI Maintains These Files:**

- `memory-bank/progress.md`
- `memory-bank/core_project_tasks_priority.md` 
- `memory-bank/session_updates/*.md`
- `memory-bank/decisions_log.md`
- `memory-bank/project_timeline.md`

**User Maintains These Files:**

- `Global_AI_Rules.md`
- `AIAgent_project_Guidelines.md`
- Architecture and design documents in `docs/`

## SUCCESS METRICS

**Session continuity:** New AI sessions can immediately understand current status
**Progress tracking:** Clear view of what's working vs. blocked
**Knowledge preservation:** Solutions and decisions don't get lost
**Efficiency:** Less time spent catching up, more time on actual work
**Quick start:** New AI session can be productive in under 2 minutes of reading.
