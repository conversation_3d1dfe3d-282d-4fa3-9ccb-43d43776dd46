# Build-AIInstruction.ps1
# Generate AI instruction block from current priorities and blockers

function Build-AIInstruction {
    # Navigate to project directory
    Set-Location "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"
    
    Write-Host "Generating AI Instruction Block..." -ForegroundColor Cyan
    
    # Check if required files exist
    if (-not (Test-Path "memory-bank/current_session_context.md")) {
        Write-Host "Error: memory-bank/current_session_context.md not found" -ForegroundColor Red
        return
    }
    
    if (-not (Test-Path "memory-bank/execution_reference.md")) {
        Write-Host "Error: memory-bank/execution_reference.md not found" -ForegroundColor Red
        return
    }
    
    # Read context file to extract priorities and blockers
    $contextContent = Get-Content "memory-bank/current_session_context.md" -Raw
    $execContent = Get-Content "memory-bank/execution_reference.md" -Raw
    
    # Extract priorities (look for numbered list under CURRENT PRIORITIES)
    $prioritiesSection = ($contextContent -split 'CURRENT PRIORITIES')[1]
    if ($prioritiesSection) {
        $prioritiesSection = ($prioritiesSection -split '##')[0]
        $priorities = @()
        $prioritiesSection -split "`n" | ForEach-Object {
            if ($_ -match '^\d+\. \*\*(.+?)\*\*(.*)') {
                $priorities += @{
                    Title = $matches[1].Trim()
                    Context = $matches[2].Trim()
                }
            }
        }
    }
    
    # Extract blockers
    $blockersSection = ($contextContent -split 'BLOCKERS')[1]
    $blockers = @()
    if ($blockersSection) {
        $blockersSection = ($blockersSection -split '##')[0]
        $blockersSection -split "`n" | ForEach-Object {
            if ($_ -match '^- (.+)' -and $_ -notmatch 'None currently') {
                $blockers += $matches[1].Trim()
            }
        }
    }
    
    # Build the instruction block
    $instruction = "# AI INSTRUCTION BLOCK - $(Get-Date -Format 'yyyy-MM-dd HH:mm')`n`n"
    $instruction += "## TOP PRIORITIES`n`n"
    
    # Add top 3 priorities
    $topPriorities = $priorities | Select-Object -First 3
    $priorityNum = 1
    foreach ($priority in $topPriorities) {
        $instruction += "### Priority ${priorityNum}: $($priority.Title)`n"
        $instruction += "**Context:** $($priority.Context)`n`n"
        
        # Add relevant file references based on priority
        $instruction += "**Key Files:**`n"
        switch -Regex ($priority.Title.ToLower()) {
            "test|validate" {
                $instruction += "- ``v4/tests/test_backtest_v4.py```n"
                $instruction += "- ``v4/tests/enhanced_engine_test.py```n"
                $instruction += "- ``memory-bank/codebase_map.md```n"
            }
            "powershell|profile" {
                $instruction += "- ``Global_Configs/Microsoft.PowerShell_profile.ps1```n"
                $instruction += "- ``memory-bank/current_session_context.md```n"
            }
            "signal|trading|pipeline" {
                $instruction += "- ``main_v4_production_run.py```n"
                $instruction += "- ``v4/run_signal_phase.py```n"
                $instruction += "- ``v4/run_trading_phase.py```n"
                $instruction += "- ``v4/settings/settings_CPS_v4.py```n"
            }
            "report|performance" {
                $instruction += "- ``v4_reporting/v4_performance_report.py```n"
                $instruction += "- ``v4_reporting/allocation_report_v4.py```n"
            }
            default {
                $instruction += "- ``memory-bank/codebase_map.md```n"
                $instruction += "- ``v4/settings/settings_CPS_v4.py```n"
            }
        }
        
        # Add execution commands
        $instruction += "`n**Suggested Commands:**`n"
        switch -Regex ($priority.Title.ToLower()) {
            "test|validate" {
                $instruction += "```batch`n"
                $instruction += "# Test full pipeline`n"
                $instruction += "run_main_v4_prod2.bat`n`n"
                $instruction += "# Test signal generation only`n"
                $instruction += "run_main_v4_signalalgo.bat`n`n"
                $instruction += "# Verify outputs`n"
                $instruction += "dir v4_trace_outputs\*.csv`n"
                $instruction += "`n```"
            }
            "signal" {
                $instruction += "```batch`n"
                $instruction += "# Generate signals only`n"
                $instruction += "run_main_v4_signalalgo.bat`n`n"
                $instruction += "# Verify signal output`n"
                $instruction += "type v4_trace_outputs\signals_output_*.csv`n"
                $instruction += "`n```"
            }
            "trading" {
                $instruction += "```batch`n"
                $instruction += "# Run trading phase only`n"
                $instruction += "run_trading_phase_standalone.bat`n`n"
                $instruction += "# Check trade outputs`n"
                $instruction += "dir v4_trace_outputs\*trade*.csv`n"
                $instruction += "`n```"
            }
            default {
                $instruction += "```batch`n"
                $instruction += "# Run full production pipeline`n"
                $instruction += "run_main_v4_prod2.bat`n`n"
                $instruction += "# Check system status`n"
                $instruction += "dir v4_trace_outputs\*.csv`n"
                $instruction += "`n```"
            }
        }
        
        $instruction += "`n---`n"
        $priorityNum++
    }
    
    # Add blockers section if any exist
    if ($blockers.Count -gt 0) {
        $instruction += "`n## CURRENT BLOCKERS`n`n"
        foreach ($blocker in $blockers) {
            $instruction += "- $blocker`n"
        }
        $instruction += "`n"
    }
    
    # Add footer
    $instruction += "`n## NOTES`n"
    $instruction += "- Focus on completing priorities in order`n"
    $instruction += "- Validate each step before proceeding`n"
    $instruction += "- Update session context as tasks complete`n"
    $instruction += "- Check execution reference for exact commands`n`n"
    $instruction += "--- End Instructions ---`n"
    $instruction += "*Generated by Build-AIInstruction at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*`n"
    
    # Output to console
    Write-Host $instruction -ForegroundColor White
    
    # Optionally save to file
    $outputPath = "memory-bank/next_ai_instructions.md"
    $instruction | Out-File -FilePath $outputPath -Encoding UTF8
    Write-Host "`nAI Instruction Block saved to: $outputPath" -ForegroundColor Green
    
    return $instruction
}

