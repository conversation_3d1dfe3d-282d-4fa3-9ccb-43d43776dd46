# V3 Reporting System Guide (AI-Optimized)

<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 Reporting System Reference
Tags: [reporting_system, troubleshooting, v3_system]
-->

> **Last Updated**: 2025-05-14  
> **Purpose**: Comprehensive reference for the V3 reporting system and troubleshooting guide  
> **Related Files**:  
> 
> - [Parameter Management](parameter_management_AI.md)
> - [System Files + Flow](systemFiles+Flow_AI.md)
> - [V3 Module Functions List](v3_module+functions_list_AI.md)

## 🔍 Quick Reference

| Component                 | Key Files                                                                     | Purpose                                    |
| ------------------------- | ----------------------------------------------------------------------------- | ------------------------------------------ |
| **Performance Reporting** | `v3_reporting/v3_performance_report.py`, `reporting/performance_reporting.py` | Generate performance metrics tables        |
| **Allocation Reporting**  | `v3_reporting/v3_allocation_report.py`, `reporting/allocation_report.py`      | Generate allocation history reports        |
| **Visualization**         | `v3_reporting/v3_visualization.py`, `visualization/performance_charts.py`     | Create charts and visualizations           |
| **Adapter**               | `v3_engine/performance_reporter_adapter.py`                                   | Bridge between V3 parameters and reporting |

## 📊 Reporting System Flow

```mermaid
flowchart LR
    %% Parameter Flow
    Registry[parameter_registry.py] -->|Parameters| Backtest[backtest.py]
    Backtest -->|Results| Adapter[performance_reporter_adapter.py]

    %% Reporting Flow
    Adapter -->|Adapted Parameters| PerfReport[v3_performance_report.py]
    Adapter -->|Adapted Parameters| AllocReport[v3_allocation_report.py]
    PerfReport -->|Generate| Excel[Excel Reports]
    AllocReport -->|Generate| Charts[PNG Charts]

    %% Styling
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef engine fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    classDef output fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;

    class Registry param;
    class Backtest engine;
    class Adapter,PerfReport,AllocReport report;
    class Excel,Charts output;
```

## 📝 Required Output Files

| Output Type             | Trigger Parameter          | File Format | Content                                            | Module                                |
| ----------------------- | -------------------------- | ----------- | -------------------------------------------------- | ------------------------------------- |
| **Performance Summary** | `create_excel`             | XLSX        | Parameter values + performance metrics             | `reporting/performance_reporting.py`  |
| **Allocation Report**   | `create_allocation_report` | XLSX        | Signal by date/ticker, allocation % by date/ticker | `reporting/allocation_report.py`      |
| **Trade Log**           | `save_trade_log`           | CSV         | Detailed log of individual trades                  | `utils/trade_log.py`                  |
| **Charts**              | `create_charts`            | PNG         | Performance visualizations                         | `visualization/performance_charts.py` |

## 🔄 Parameter Flow to Reports

```text
GUI → Parameter Registry → Backtest Engine → Performance Reporter Adapter → Reports
```

## 🚨 Known Issues and Status

1. **Parameter Conversion Gap**: 
   
   - Reporting parameters (`create_excel`, `metrics`, `create_charts`, `chart_types`) not yet converted to V3 parameter classes
   - These parameters don't flow through the V3 registry system

2. **Adapter Integration**:
   
   - `v3_engine/performance_reporter_adapter.py` may not be properly receiving or passing all parameters
   - End-to-end verification through full V3 backtest hasn't happened yet

3. **Signal History**:
   
   - Signal history may not be properly populated or preserved in backtest engine
   - Error: "signal_history is None or empty, allocation report/graph may not be produced"

4. **Allocation Report**:
   
   - `generate_rebalance_report` function coded but not yet working properly
   - Should have clear warnings, specific output paths, and follow naming conventions

## 🛠️ Systematic Troubleshooting Approach

### Phase 1: Parameter Registration

1. **Identify Current Parameter Definitions**:
   
   - Locate where reporting parameters are currently defined
   - Check `config/config.py`, `config/config_v2.py`, or similar files

2. **Create V3 Parameter Classes**:
   
   - Define proper V3 parameter classes for all reporting parameters
   - Example:
     
     ```python
     create_excel = ConfigParameter(
       name='create_excel',
       default=True,
       show_in_gui=True,
       optimizable=False
     )
     ```

3. **Register Parameters**:
   
   - Register these parameters with the V3 registry
   - Add to appropriate registration function in `app/gui/v3_register_parameters.py`

### Phase 2: Update Code to Use V3 Parameters

4. **Modify Reporter Adapter**:
   
   - Update `v3_engine/performance_reporter_adapter.py` to:
     - Accept the full V3 parameter set
     - Retrieve values of reporting parameters
     - Pass these values to reporting functions

5. **Update Reporting Modules**:
   
   - Modify core reporting modules to use parameters from adapter
   - Update XLSX header row generation to fetch all parameters
   - Ensure proper formatting according to standards

6. **Update Engine/Runner Script**:
   
   - Ensure main script passes complete parameter set to adapter
   - Verify parameter flow through entire system

### Phase 3: Signal History Debugging

7. **Trace Signal History**:
   
   - Add logging to track signal_history creation and modification
   - Verify signal_history is properly returned from backtest engine
   - Check for any data type issues or empty structures

8. **Fix Signal History**:
   
   - Ensure signal_history is properly populated during backtest
   - Add validation checks before reporting attempts to use it
   - Implement recovery logic if needed

## 📋 File-by-File Inspection Guide

### 1. Parameter Registry and Flow

```python
# Check parameter registration
grep_search for "register_parameter" in app/gui/v3_register_parameters.py

# Check parameter retrieval
grep_search for "get_parameter" in v3_engine/performance_reporter_adapter.py

# Check parameter passing
grep_search for "create_excel" in reporting/performance_reporting.py
```

### 2. Signal History Tracking

```python
# Check signal history creation
grep_search for "signal_history" in engine/backtest.py

# Check signal history usage
grep_search for "signal_history" in reporting/allocation_report.py

# Check error handling
grep_search for "signal_history is None" in v3_reporting/v3_allocation_report.py
```

### 3. Report Generation

```python
# Check performance report generation
grep_search for "generate_performance_report" in v3_reporting/v3_performance_report.py

# Check allocation report generation
grep_search for "generate_allocation_report" in v3_reporting/v3_allocation_report.py

# Check adapter functionality
grep_search for "adapt_parameters" in v3_engine/performance_reporter_adapter.py
```

## 📊 Required Report Format

### Performance Report (XLSX)

- **Column Order**: Parameters (left) → Metrics (right)
- **Parameters**: Strategy, lookbacks, rebalance frequency, etc.
- **Metrics**: CAGR, Sharpe, max drawdown, etc.
- **Formatting**: Native Excel number formats
- **Header Row**: Must include all parameter values

### Allocation Report (XLSX)

- **Tab 1**: Signal by date/ticker
- **Tab 2**: Allocation % by date/ticker
- **Header**: Parameter values and timestamp
- **Naming**: Must follow conventions with date/time

## 🔍 Recent Fixes (May 2025)

1. **Signal History**:
   
   - Modified `_calculate_results` in `engine/backtest.py` to include signal_history
   - Added recovery logic in `run_backtest_v2_with_metrics.py`

2. **Allocation Report**:
   
   - Updated allocation report generation to handle empty signal history
   - Fixed formatting issues in allocation charts

## 🔄 Structured Verification Process

A comprehensive verification system has been implemented to ensure the V3 reporting system functions correctly. This process includes:

### Expected Output Files

| Category                | Key Files                                               | Min Size | Verification Criteria                                      |
| ----------------------- | ------------------------------------------------------- | -------- | ---------------------------------------------------------- |
| **Log Files**           | `logs/v3_engine_reporting_test_{timestamp}.log`         | 1KB      | Contains "Starting V3 engine" and "Completed successfully" |
|                         | `logs/v3_debug_{timestamp}.txt`                         | 5KB      | Contains signal generation and report entries              |
|                         | `logs/v3_error_{timestamp}.log`                         | 0KB      | Empty if no errors                                         |
| **Performance Reports** | `output/{strategy}_performance_tables_{timestamp}.xlsx` | 50KB     | Contains all required tabs                                 |
|                         | `output/{strategy}_monthly_returns_{timestamp}.png`     | 100KB    | Image dimensions ≥ 1200x800                                |
|                         | `output/{strategy}_cumulative_returns_{timestamp}.png`  | 100KB    | Contains drawdown panel                                    |
| **Data Files**          | `output/{strategy}_signal_history_{timestamp}.csv`      | 10KB     | Contains dates and ticker columns                          |
|                         | `output/{strategy}_weights_history_{timestamp}.csv`     | 10KB     | Contains dates and ticker columns                          |
|                         | `output/{strategy}_returns_{timestamp}.csv`             | 10KB     | Contains dates and return values                           |

### Verification Steps

1. **Run Test with Enhanced Logging**
   
   - Set `BACKTEST_LOG_LEVEL=DEBUG`
   - Capture console output to dedicated log file

2. **Validate File Existence and Size**
   
   - Verify all expected files exist
   - Check file sizes against minimum requirements
   - Flag undersized files as potential errors

3. **Verify Content Structure**
   
   - Check log files for expected entries
   - Verify Excel files contain required sheets
   - Validate CSV files for proper structure

4. **Generate Verification Report**
   
   - Produce pass/fail status for each component
   - List missing or undersized files
   - Provide specific error details and fix recommendations

### Implementation Tools

- **Verification Script**: `verify_v3_reporting.py` automates the verification process

- **Test Wrapper**: `run_v3_test_with_verification.bat` runs test and verification together

- **Detailed Documentation**: See `docs/v3_reporting_analysis.md` for complete verification checklist
  
  - Modified to strip time from dates in Excel output
  - Improved chart formatting (years on x-axis, higher DPI, better colors)
3. **Parameter Handling**:
   - Fixed parameter handling to preserve tuple format
   - Modified performance reporting to extract values from parameter tuples

## 🔄 Testing Process

1. Run `run_ema_v3_gui_test.bat` to test the GUI and parameter flow
2. Check output directory for generated reports
3. Verify all required reports are generated with correct format
4. Inspect report content for accuracy and completeness
5. Check for any error messages in console output

---

*This document is maintained as the central reference for the V3 reporting system. Update as troubleshooting progresses.*
