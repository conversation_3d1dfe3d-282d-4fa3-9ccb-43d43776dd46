"""
v4/run_trading_phase.py
Trading phase script for the decoupled backtest architecture.
Part of the CPS v4 compliant backtest system.

This script loads pre-computed signals and executes the trading strategy.
It validates signal data before processing, executes trades based on signal changes,
and generates trade logs and allocation history.
"""

import os
import sys
from pathlib import Path
import pandas as pd
import logging
from datetime import datetime

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import core V4 components
from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.portfolio_v4 import Portfolio

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def validate_signals(signals, price_data):
    """
    Validate signals against price data.
    
    Args:
        signals (DataFrame): Signal data with datetime index
        price_data (DataFrame): Price data with datetime index
        
    Returns:
        bool: True if signals are valid, False otherwise
    """
    # Check if signals are empty
    if signals.empty:
        logger.error("Signals dataframe is empty")
        return False
    
    # Check if signals have the same columns as price data
    missing_columns = set(price_data.columns) - set(signals.columns)
    if missing_columns:
        logger.warning(f"Signals missing columns for assets: {missing_columns}")
        # Not a fatal error, but worth noting
    
    # Check if signals sum to approximately 1.0 for each date (allowing for floating point errors)
    for date, row in signals.iterrows():
        row_sum = row.sum()
        if not (0.99 <= row_sum <= 1.01):
            logger.warning(f"Signals for {date} sum to {row_sum}, not 1.0")
            # Not a fatal error, but worth noting
    
    # Check if signals contain NaN values
    if signals.isna().any().any():
        logger.error("Signals contain NaN values")
        return False
    
    # Check if signals contain any negative values (for long-only strategy)
    if (signals < 0).any().any():
        logger.warning("Signals contain negative values (not suitable for long-only strategy)")
        # Not a fatal error, but worth noting
    
    return True

def run_trading_phase(signals_file=None):
    """
    Run trading phase using signals from file.
    
    Args:
        signals_file (str, optional): Path to signals file. Defaults to None.
        
    Returns:
        dict: Results including portfolio, allocation_history, and trade_log
    """
    logger.info("[MILESTONE] Starting Trading Phase")
    
    # Load settings and data
    settings = load_settings()
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    
    # Log data information
    logger.info(f"Loaded price data with shape {price_data.shape}")
    logger.info(f"Date range: {price_data.index.min()} to {price_data.index.max()}")
    logger.info(f"Tickers: {', '.join(price_data.columns)}")
    
    # Load signals - use the correct CSV file with proper allocations
    if signals_file is None:
        # Use the specific CSV file that has correct allocations summing to 100%
        signals_file = _project_root / "v4_trace_outputs" / "signals_output_20250622_192844.csv"

    signals_path = Path(signals_file)
    if not signals_path.exists():
        logger.error(f"Signals file {signals_file} not found!")
        sys.exit(1)

    try:
        # Determine file type and load accordingly
        if signals_file.endswith('.parquet'):
            signals = pd.read_parquet(signals_file)
            logger.info(f"Loaded signals from Parquet with shape {signals.shape}")
        else:
            # Load CSV file and set Date column as index
            signals = pd.read_csv(signals_file, index_col='Date', parse_dates=True)
            logger.info(f"Loaded signals from CSV with shape {signals.shape}")
        
        logger.info(f"Signals date range: {signals.index.min()} to {signals.index.max()}")

        # Validate that signals sum to 100% (within tolerance)
        signal_sums = signals.sum(axis=1)
        max_deviation = abs(signal_sums - 1.0).max()
        if max_deviation > 0.01:  # 1% tolerance
            logger.warning(f"Signal validation failed! Max deviation from 100%: {max_deviation:.3%}")
        else:
            logger.info(f"Signal validation passed. Max deviation from 100%: {max_deviation:.3%}")

    except Exception as e:
        logger.error(f"Error loading signals file: {e}")
        sys.exit(1)
    
    # Validate signals
    if not validate_signals(signals, price_data):
        logger.error("Signal validation failed")
        sys.exit(1)
    
    # Run backtest
    logger.info("Initializing backtest engine")
    engine = BacktestEngine(settings)
    
    # Add run_backtest_with_signals method to BacktestEngine if it doesn't exist
    if not hasattr(BacktestEngine, 'run_backtest_with_signals'):
        logger.error("BacktestEngine does not have run_backtest_with_signals method")
        logger.error("Please update backtest_v4.py with the required method")
        sys.exit(1)
    
    # HOTFIX: Add _initialize_components method if it doesn't exist
    # This ensures compatibility with the trading engine
    if not hasattr(engine, '_initialize_components'):
        logger.warning("Adding missing _initialize_components method to BacktestEngine instance")
        def initialize_components_hotfix(self, price_data):
            """Hotfix implementation of _initialize_components"""
            # Initialize portfolio - Portfolio class gets initial_capital from module-level settings
            self.portfolio = Portfolio()  # No parameters needed as Portfolio reads from settings
            # Store price data
            self.price_data = price_data
            # Reset pending orders
            self.pending_orders = {}
            # Reset last rebalance date
            self.last_rebalance_date = None
            logger.info("Components initialized using hotfix implementation")
        
        # Add the method to the instance
        import types
        engine._initialize_components = types.MethodType(initialize_components_hotfix, engine)
    
    logger.info("Running backtest with pre-computed signals")
    results = engine.run_backtest_with_signals(signals, price_data)
    
    # Save results
    output_dir = _project_root / "v4_trace_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # Add timestamp to filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    allocation_history = results.get('allocation_history')
    trade_log = results.get('trade_log')
    
    if allocation_history is not None:
        allocation_file = output_dir / f"allocation_history_{timestamp}.csv"
        allocation_history.to_csv(str(allocation_file))
        
        logger.info(f"Saved allocation history to:")
        logger.info(f"  - {allocation_file}")
    else:
        logger.warning("No allocation history in results")
    
    if trade_log is not None:
        trades_file = output_dir / f"trade_log_{timestamp}.csv"
        trade_log.to_csv(str(trades_file))
        
        logger.info(f"Saved trade log to:")
        logger.info(f"  - {trades_file}")
    else:
        logger.warning("No trade log in results")
    
    logger.info("[MILESTONE] Trading phase complete")
    return results

if __name__ == "__main__":
    try:
        run_trading_phase()
    except Exception as e:
        import traceback
        logger.error("Exception occurred during trading phase execution:")
        logger.error(f"Error type: {type(e).__name__}")
        logger.error(f"Error message: {str(e)}")
        logger.error("Full traceback:")
        logger.error(traceback.format_exc())
        # Re-raise the exception to maintain the original exit code
        raise
