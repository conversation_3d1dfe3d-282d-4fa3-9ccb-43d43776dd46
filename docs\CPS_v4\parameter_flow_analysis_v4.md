# Parameter Flow Extraction - CPS v4

**Date**: 2025-01-29  
**Purpose**: Complete mapping of parameter flow from `settings_CPS_v4.py` through the v4 system

## 1. Parameter Categories Identified

### 1.1 Module-Level Constants (settings_CPS_v4.py)
- `RUN_MODE` = "UNIFIED"
- `LOG_LEVEL` = "INFO"
- `LOG_TO_FILE` = True
- `LOG_TO_CONSOLE` = True
- `RETAIN_INTERMEDIATE_FILES` = True
- `RETAIN_DEBUG_OUTPUT` = False
- `MAX_LOG_FILES` = 10
- `MAX_OUTPUT_DAYS` = 30
- `MAX_MEMORY_USAGE_GB` = 8
- `ENABLE_PARALLEL_PROCESSING` = True
- `MAX_WORKER_THREADS` = 4
- `PARAM_TYPES` = {'SimpleA': 'simple_alphanumeric', ...}
- `SETTINGS_FILE` = MODULE_DIR / 'settings_parameters_v4.ini'

### 1.2 Configuration Dictionary Structure (from INI)
Based on `settings_parameters_v4.ini`, organized by sections:

#### [Core] Section Parameters
- `risk_free_ticker` (SimpleA): "^IRX"
- `start_date` (SimpleA): "2020-01-01" 
- `end_date` (SimpleA): "2024-12-31"
- `max_allocation` (ComplexN): Optimizable parameter
- `min_allocation` (ComplexN): Optimizable parameter
- `tickers` (AlphaList): Reference to Group1ETFBase
- `benchmark_ticker` (AlphaList): Reference to Benchmark_SP500

#### [Strategy] Section Parameters
- `signal_algo` (AlphaList): Reference to Strategy_EMA
- `min_weight` (SimpleN): 0.0
- `max_weight` (SimpleN): 1.0
- `strategy_name` (SimpleA): "EMA_Crossover"
- `execution_delay` (SimpleN): 1
- `ema_short_period` (ComplexN): Optimizable parameter
- `ema_long_period` (ComplexN): Optimizable parameter
- `st_lookback` (ComplexN): Optimizable parameter
- `mt_lookback` (ComplexN): Optimizable parameter
- `lt_lookback` (ComplexN): Optimizable parameter

#### [ema_model] Section Parameters
- `short_period` (SimpleN): 12
- `medium_period` (SimpleN): 26
- `long_period` (SimpleN): 50

#### [Backtest] Section Parameters
- `initial_capital` (SimpleN): 1000000
- `commission_rate` (SimpleN): 0.001
- `slippage_rate` (SimpleN): 0.0005
- `deviation_threshold` (SimpleN): 0.02
- `rebalance_freq` (AlphaList): Reference to Rebalance_Monthly
- `benchmark_rebalance_freq` (AlphaList): Reference to Rebalance_Yearly
- `signal_history` (SimpleA): False
- `include_cash` (SimpleA): True

#### [Report] Section Parameters
- `create_excel` (SimpleA): false
- `save_trade_log` (SimpleA): True
- `include_summary` (SimpleA): True
- `export_simple_validation_files` (SimpleA): true
- `excel_format` (AlphaList): Reference to Excel_XLSX
- `output_directory` (SimpleA): "output"

#### [Performance] Section Parameters
- `risk_free_rate` (SimpleN): 0.02
- `include_benchmark` (SimpleA): True
- `benchmark_strategy` (AlphaList): Reference to Benchmark_Equal
- `metrics` (AlphaList): Reference to MetricsList

#### [System] Section Parameters
- `log_level` (SimpleA): "INFO"
- `log_file` (SimpleA): "logs/cps_v4.log"
- `integration_flag` (SimpleA): True
- `retain_csv_signal_output` (SimpleA): True
- `system_lookback` (ComplexN): Optimizable parameter
- `system_top_n` (ComplexN): Optimizable parameter

#### [data_params] Section Parameters
- `tickers` (AlphaList): Reference to Group1ETFBase
- `start_date` (SimpleA): "2020-01-01"
- `end_date` (SimpleA): "2025-07-21"
- `price_field` (SimpleA): "Close"
- `data_storage_mode` (SimpleA): "Save"
- `enable_date_filtering` (SimpleA): False
- `filter_start_date` (SimpleA): "2020-01-01"
- `filter_end_date` (SimpleA): "2024-12-31"

### 1.3 Data Paths
- `MODULE_DIR`: Path(os.path.dirname(os.path.abspath(__file__)))
- `SETTINGS_FILE`: MODULE_DIR / 'settings_parameters_v4.ini'
- Output paths: "output", "v4_trace_outputs/", "logs/"

## 2. Master Parameter → Consumer(s) Flow Table

| Parameter | Section | Type | Consumer Module(s) | Consumption Method | Usage Details |
|-----------|---------|------|--------------------|--------------------|---------------|
| **CORE SYSTEM CONSTANTS** | | | | | |
| `RUN_MODE` | Module | SimpleA | `run_unified_pipeline.py` | Direct import | Controls pipeline execution mode |
| `LOG_LEVEL` | Module | SimpleA | `run_unified_pipeline.py`, logging modules | Direct import | Sets logging verbosity |
| `SETTINGS_FILE` | Module | Path | `settings_CPS_v4.py` | Direct access | Location of INI file |
| **DATA LOADING PARAMETERS** | | | | | |
| `tickers` | data_params | AlphaList | `data_loader_v4.py` | `current_settings.get('data_params', {}).get('tickers')` | Determines which assets to load |
| `start_date` | data_params | SimpleA | `data_loader_v4.py` | `data_params.get('start_date')` | Data loading start date |
| `end_date` | data_params | SimpleA | `data_loader_v4.py` | `data_params.get('end_date')` | Data loading end date |
| `price_field` | data_params | SimpleA | `data_loader_v4.py` | `data_params.get('price_field', 'Close')` | Which price column to use |
| `data_storage_mode` | data_params | SimpleA | `data_loader_v4.py` | `data_params.get('data_storage_mode', 'Read')` | How to handle data files |
| **BACKTEST ENGINE PARAMETERS** | | | | | |
| `initial_capital` | backtest | SimpleN | `backtest_v4.py` | `settings['backtest']['initial_capital']` | Starting portfolio value |
| `commission_rate` | backtest | SimpleN | `backtest_v4.py` | `settings['backtest']['commission_rate']` | Transaction cost rate |
| `slippage_rate` | backtest | SimpleN | `backtest_v4.py` | `settings['backtest']['slippage_rate']` | Market impact cost rate |
| `rebalance_freq` | backtest | AlphaList | `backtest_v4.py` | `settings.get('backtest', {}).get('rebalance_freq')` | How often to rebalance |
| `execution_delay` | strategy | SimpleN | `backtest_v4.py` | `settings.get('strategy', {}).get('execution_delay')` | Days delay between signal and execution |
| `deviation_threshold` | backtest | SimpleN | `trade_filter.py` | `settings.get('backtest', {}).get('deviation_threshold', 0.02)` | Trade execution threshold |
| **STRATEGY PARAMETERS** | | | | | |
| `strategy_name` | strategy | SimpleA | `main_v4_production_run.py` | `settings.get('strategy', {}).get('strategy_name', 'equal_weight')` | Which strategy to run |
| `signal_algo` | strategy | AlphaList | Signal generators | Via strategy selection | Algorithm selection |
| `min_weight` | strategy | SimpleN | `ema_allocation_model_v4.py` | `_get_param('min_weight')` | Minimum asset allocation |
| `max_weight` | strategy | SimpleN | `ema_allocation_model_v4.py` | `_get_param('max_weight')` | Maximum asset allocation |
| **EMA MODEL PARAMETERS** | | | | | |
| `st_lookback` | strategy | ComplexN | `ema_allocation_model_v4.py` | `_get_param('st_lookback')` | Short-term EMA period |
| `mt_lookback` | strategy | ComplexN | `ema_allocation_model_v4.py` | `_get_param('mt_lookback')` | Medium-term EMA period |
| `lt_lookback` | strategy | ComplexN | `ema_allocation_model_v4.py` | `_get_param('lt_lookback')` | Long-term EMA period |
| `short_period` | ema_model | SimpleN | `ema_allocation_model_v4.py` | Via parameter hierarchy | EMA calculation period |
| `medium_period` | ema_model | SimpleN | `ema_allocation_model_v4.py` | Via parameter hierarchy | EMA calculation period |
| `long_period` | ema_model | SimpleN | `ema_allocation_model_v4.py` | Via parameter hierarchy | EMA calculation period |
| **REPORTING PARAMETERS** | | | | | |
| `create_excel` | report | SimpleA | `v4_performance_report.py` | `reporting_settings.get('create_excel', False)` | Whether to generate Excel reports |
| `export_simple_validation_files` | report | SimpleA | `v4_performance_report.py` | `reporting_settings.get('export_simple_validation_files', False)` | Whether to export validation files |
| `output_directory` | report | SimpleA | `v4_performance_report.py` | `reporting_settings.get('output_directory', 'output')` | Where to save reports |
| `excel_format` | report | AlphaList | `v4_performance_report.py` | Via report settings | Output file format |
| **PERFORMANCE PARAMETERS** | | | | | |
| `risk_free_rate` | performance | SimpleN | `data_loader_v4.py` | `current_settings.get('performance', {}).get('risk_free_rate', 0.0)` | Risk-free rate for calculations |
| `include_benchmark` | performance | SimpleA | Performance modules | Via performance settings | Whether to include benchmark |
| `benchmark_strategy` | performance | AlphaList | Benchmark modules | Via performance settings | Which benchmark to use |
| **SYSTEM PARAMETERS** | | | | | |
| `log_level` | system | SimpleA | `run_unified_pipeline.py` | `settings.get('system', {}).get('log_level', 'INFO')` | Logging configuration |
| `integration_flag` | system | SimpleA | Pipeline modules | Via system settings | Unified vs decoupled mode |
| `system_lookback` | system | ComplexN | `ema_allocation_model_v4.py` | Via `_get_param()` hierarchy | System-wide lookback period |
| `system_top_n` | system | ComplexN | Various modules | Via system settings | Top N asset selection |

## 3. Parameter Access Patterns

### 3.1 Direct Import Pattern
**Used by**: Module-level constants  
**Example**: `from v4.settings.settings_CPS_v4 import load_settings`

### 3.2 Settings Dictionary Access Pattern  
**Used by**: Most consumers  
**Example**: `settings = load_settings(); value = settings.get('section', {}).get('param')`

### 3.3 Module-Level Loading Pattern
**Used by**: `backtest_v4.py`, `ema_allocation_model_v4.py`, `trade_filter.py`  
**Example**: Settings loaded once at module level, parameters extracted to variables

### 3.4 Parameter Hierarchy Pattern
**Used by**: `ema_allocation_model_v4.py`  
**Example**: `_get_param()` searches ema_model → strategy → system sections

### 3.5 Function Argument Pattern
**Used by**: Main execution functions  
**Example**: `load_data_for_backtest(current_settings=settings)`

## 4. Parameter Type Processing

### 4.1 SimpleA (Simple Alphanumeric)
- **Processing**: Direct string/boolean extraction
- **Example**: `create_excel = reporting_settings.get('create_excel', False)`

### 4.2 SimpleN (Simple Numeric)
- **Processing**: Direct numeric extraction
- **Example**: `initial_capital = backtest_params['initial_capital']`

### 4.3 ComplexN (Complex Numeric)
- **Processing**: Extract `default_value` from dictionary
- **Example**: `_extract_value_from_complexn_dict(param_value_from_settings)`

### 4.4 AlphaList (Alpha List)
- **Processing**: Extract `default` from dictionary or resolve list reference
- **Example**: `tickers_raw['default'] if isinstance(tickers_raw, dict) else tickers_raw`

## 5. Critical Dependencies

### 5.1 Required Settings Sections
- `[data_params]`: Required for all data operations
- `[backtest]`: Required for backtest engine
- `[strategy]`: Required for signal generation
- `[report]`: Required for output generation

### 5.2 Parameter Validation Points
- `data_loader_v4.py`: Validates data_params completeness
- `backtest_v4.py`: Validates backtest parameters at module load
- `ema_allocation_model_v4.py`: Hard fails on missing parameters

### 5.3 Error Handling Patterns
- **ValueError**: Raised for missing required parameters
- **KeyError**: Raised for malformed parameter sections
- **RuntimeError**: Raised for critical system failures

## 6. Parameter Flow Summary

**Total Parameters Tracked**: 45+ parameters across 8 sections  
**Primary Consumer Modules**: 15+ modules  
**Access Patterns**: 5 distinct patterns identified  
**Parameter Types**: 4 types (SimpleA, SimpleN, ComplexN, AlphaList)

This parameter flow extraction provides a complete mapping of how configuration flows from the central `settings_CPS_v4.py` system through all consumer modules in the v4 architecture.
