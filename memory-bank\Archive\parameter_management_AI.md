# Parameter Management System (AI-Optimized)
<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 Parameter System Reference
Tags: [parameter_system, parameter_types, v3_system]
-->

> **Last Updated**: 2025-05-14
> **Purpose**: Comprehensive reference for the V3 parameter system
> **Related Files**:
> - [System Files + Flow](systemFiles+Flow_AI.md)
> - [V3 Module Functions List](v3_module+functions_list_AI.md)

## 🔍 Quick Reference

| Component | Key Files | Purpose |
|-----------|-----------|---------|
| **Parameter Registry** | `v3_engine/parameter_registry.py` | Central parameter management |
| **Parameter Types** | `v3_engine/parameters.py` | Parameter class definitions |
| **Strategy Parameters** | `v3_engine/strategy_parameter_set.py` | Strategy-specific parameters |
| **Parameter Optimizer** | `v3_engine/parameter_optimizer.py` | Parameter optimization logic |

## 📊 Parameter Flow Diagram

```mermaid
flowchart LR
    %% Parameter Flow
    GUI[v3_gui_core.py] -->|User Input| ParamWidgets[v3_parameter_widgets.py]
    ParamWidgets -->|Update Values| ParamMgr[gui_parameter_manager.py]
    ParamMgr -->|Register/Update| Registry[parameter_registry.py]
    Registry -->|Define Types| Params[parameters.py]
    Registry -->|Group Parameters| StratParams[strategy_parameter_set.py]
    Registry -->|Optimize| Optimizer[parameter_optimizer.py]
    Registry -->|Parameters| Strategy[ema_v3_adapter.py]
    Registry -->|Parameters| Backtest[backtest.py]
    Registry -->|Parameter Values| Reports[v3_performance_report.py]
    
    %% Styling
    classDef gui fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef engine fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;
    
    class GUI,ParamWidgets,ParamMgr gui;
    class Registry,Params,StratParams,Optimizer param;
    class Strategy,Backtest engine;
    class Reports report;
```

## 🔄 Parameter Flow Process

```text
GUI → Parameter Registry → Strategy → Backtest Engine → Reports
```

**Key Components**:

- `v3_engine/parameter_registry.py`: Central registry for all parameters
- `v3_engine/parameters.py`: Parameter class definitions
- `v3_engine/strategy_parameter_set.py`: Strategy-specific parameter grouping
- `v3_engine/parameter_optimizer.py`: Parameter optimization logic

## 📝 Parameter Registry API

| Method | Purpose | Example Usage |
|--------|---------|---------------|
| `register_parameter(group, parameter)` | Register single parameter | `registry.register_parameter('ema', lookback_period)` |
| `register_parameter_list(group, parameters)` | Register multiple parameters | `registry.register_parameter_list('core', [initial_capital, commission_rate])` |
| `get_parameter(name, group=None)` | Get parameter by name | `lookback = registry.get_parameter('lookback_period', 'ema')` |
| `get_parameters(group)` | Get all parameters in group | `ema_params = registry.get_parameters('ema')` |
| `get_all_parameters()` | Get all registered parameters | `all_params = registry.get_all_parameters()` |
| `get_parameter_values(group=None)` | Get parameter values as dict | `param_dict = registry.get_parameter_values()` |

## 🧪 Parameter Classes

| Class | Purpose | Optimizable | GUI Visible | Example |
|-------|---------|:-----------:|:-----------:|---------|
| `NumericParameter` | Integer/float values | ✅ | ✅ | `lookback_period = NumericParameter('lookback_period', 30, 10, 100, 5)` |
| `CategoricalParameter` | Selection from options | ✅ | ✅ | `rebalance_freq = CategoricalParameter('rebalance_frequency', 'monthly', ['daily', 'weekly', 'monthly'])` |
| `ConfigParameter` | Configuration values | ❌ | ❌ | `data_dir = ConfigParameter('data_directory', 'data/')` |
| `BaseParameter` | Base class for all parameters | ❌ | ❌ | Not used directly |

## 📋 Parameter Class Hierarchy

```text
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
└── ConfigParameter
```

## 🔄 Parameter Registration Process

### 1. Define Parameters

```python
# In v3_register_parameters.py
def register_ema_parameters():
    # Create parameter objects
    lookback_period = NumericParameter(
        name='lookback_period',
        default=30,
        min_val=10,
        max_val=100,
        step=5,
        show_in_gui=True,
        optimizable=True
    )
    
    # Return list of parameters
    return [lookback_period]
```

### 2. Register with Registry

```python
# In v3_gui_core.py
def initialize_parameters():
    # Create registry
    registry = ParameterRegistry()
    
    # Register core parameters
    core_params = register_core_parameters()
    registry.register_parameter_list('core', core_params)
    
    # Register strategy parameters
    ema_params = register_ema_parameters()
    registry.register_parameter_list('ema', ema_params)
    
    return registry
```

### 3. Connect to GUI

```python
# In v3_parameter_widgets.py
def create_parameter_widgets(registry):
    widgets = {}
    
    # Create widgets for each parameter
    for param in registry.get_all_parameters():
        if param.show_in_gui:
            widget = create_widget_for_parameter(param)
            widgets[param] = widget
            
    return widgets
```

### 4. Update from GUI

```python
# In v3_gui_actions.py
def update_parameters_from_gui(registry, widgets):
    # Update parameter values from widgets
    for param, widget in widgets.items():
        value = get_value_from_widget(widget)
        param.value = value
```

### 5. Use in Backtest

```python
# In ema_v3_adapter.py
def generate_signal(registry, price_data):
    # Get parameter values
    lookback = registry.get_parameter('lookback_period', 'ema').value
    
    # Use in calculations
    signals = calculate_ema_signals(price_data, lookback)
    return signals
```

### 6. Include in Reports

```python
# In v3_performance_report.py
def generate_report(registry, results):
    # Get all parameter values as dict
    params = registry.get_parameter_values()
    
    # Include in report
    report_data = {
        'parameters': params,
        'results': results
    }
    
    return report_data
```

## 🔄 Parameter Optimization

### 1. Define Optimization Ranges

```python
# In v3_register_parameters.py
lookback_period = NumericParameter(
    name='lookback_period',
    default=30,
    min_val=10,
    max_val=100,
    step=5,
    show_in_gui=True,
    optimizable=True  # Mark as optimizable
)
```

### 2. Generate Combinations

```python
# In v3_engine/parameter_optimizer.py
def get_optimization_combinations(registry):
    # Get optimizable parameters
    optimizable = [p for p in registry.get_all_parameters() if p.optimizable]
    
    # Generate combinations
    combinations = []
    for param in optimizable:
        if isinstance(param, NumericParameter):
            values = range(param.min_val, param.max_val + 1, param.step)
        elif isinstance(param, CategoricalParameter):
            values = param.options
            
        combinations.append(values)
        
    # Return product of all combinations
    return itertools.product(*combinations)
```

### 3. Run Optimization

```python
# In run_backtest_v3.py
def run_optimization(registry):
    # Get parameter combinations
    combinations = get_optimization_combinations(registry)
    
    # Run backtest for each combination
    results = []
    for combo in combinations:
        # Update parameter values
        for param, value in zip(get_optimizable_parameters(registry), combo):
            param.value = value
            
        # Run backtest with current parameters
        result = run_backtest(registry)
        results.append(result)
        
    return results
```

## 🔍 Recent Parameter System Changes (May 2025)

1. **Execution Delay Parameter**:
   - Fixed optimization handling for execution_delay parameter
   - Added proper type checking in backtest engine
   - Ensured consistent handling in performance reports

2. **Categorical Parameters**:
   - Improved validation for categorical parameters
   - Added support for categorical parameter optimization
   - Fixed GUI display for categorical parameters

3. **Type Safety**:
   - Enhanced parameter validation throughout the system
   - Added clear error messages for parameter type mismatches
   - Implemented consistent parameter conversion between components

## 📋 Implementation Status

| Component | Status | Notes |
|-----------|:------:|-------|
| Parameter Registry | ✅ | Complete with all parameter types |
| Parameter Classes | ✅ | All parameter types implemented |
| GUI Integration | ✅ | Fully functional parameter widgets |
| Parameter Optimization | ✅ | Working with all parameter types |
| Parameter Validation | ⚠️ | Basic validation implemented, needs expansion |
| Parameter Persistence | 🔄 | Planned for future implementation |

---

*This document is maintained as the central reference for the V3 parameter system. Update as the system evolves.*
