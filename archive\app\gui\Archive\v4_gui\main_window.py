"""
V4 Main Window

Main window for the V4 GUI, using CPS v4 settings for configuration.
"""

import sys
import os
from pathlib import Path
from typing import Dict, Any, Optional

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox,
    QPushButton, QMessageBox, QScrollArea, QGroupBox, QSizePolicy
)
from PySide6.QtCore import Qt

# Import CPS v4 settings
from CPS_v4.settings_CPS_v4 import load_settings

# Import GUI components
from . import widgets
from .parameter_groups import CoreParameterGroup, StrategyParameterGroup

# Configure logging
import logging
logger = logging.getLogger(__name__)

class MainWindowV4(QMainWindow):
    """
    Main window for the V4 GUI, using CPS v4 settings.
    
    This window provides a clean interface for configuring and running backtests
    using the CPS v4 parameter system.
    """
    
    def __init__(self):
        """Initialize the main window with CPS v4 settings."""
        super().__init__()
        
        # Load CPS v4 settings
        self.settings = load_settings()
        
        # Set up the UI
        self._init_ui()
        
        # Initialize parameter groups
        self._init_parameter_groups()
        
        # Connect signals
        self._connect_signals()
    
    def _init_ui(self) -> None:
        """Initialize the user interface."""
        self.setWindowTitle("WTP Backtesting Engine - V4")
        self.resize(1000, 700)
        
        # Create central widget and main layout
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QVBoxLayout(central)
        
        # Add title
        title_widget = self._create_title_widget()
        main_layout.addWidget(title_widget)
        
        # Create scroll area for the form
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        main_layout.addWidget(scroll, stretch=1)
        
        # Create form widget and layout
        self.form_widget = QWidget()
        self.form_layout = QVBoxLayout(self.form_widget)
        scroll.setWidget(self.form_widget)
        
        # Add run button at the bottom
        run_btn = QPushButton("Run Backtest with V4 Parameters")
        run_btn.setMinimumHeight(40)
        run_btn.clicked.connect(self.run_backtest)
        main_layout.addWidget(run_btn)
    
    def _create_title_widget(self) -> QWidget:
        """Create the title widget with logo placeholder."""
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        
        title_label = QLabel("WTP Backtesting Engine - V4")
        font = title_label.font()
        font.setPointSize(20)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignCenter)
        
        # Add spacer for logo (placeholder)
        spacer = QWidget()
        spacer.setFixedSize(200, 50)
        
        title_layout.addWidget(title_label, stretch=1)
        title_layout.addWidget(spacer)
        
        return title_widget
    
    def _init_parameter_groups(self) -> None:
        """Initialize parameter groups."""
        # Core parameters group
        self.core_group = CoreParameterGroup(self.settings, self)
        self.form_layout.addWidget(self.core_group)
        
        # Strategy parameters group
        self.strategy_group = StrategyParameterGroup(self.settings, self)
        self.form_layout.addWidget(self.strategy_group)
        
        # Debug options group
        self._add_debug_options()
    
    def _add_debug_options(self) -> None:
        """Add debug options to the form."""
        debug_group = QGroupBox("Debug Options")
        debug_layout = QVBoxLayout(debug_group)
        
        self.debug_checkbox = QCheckBox("Debug mode (more logging)")
        self.debug_paramflow_checkbox = QCheckBox("Debug parameter flow")
        self.track_ema_checkbox = QCheckBox("Track EMA calculations (creates detailed XLSX file)")
        
        debug_layout.addWidget(self.debug_checkbox)
        debug_layout.addWidget(self.debug_paramflow_checkbox)
        debug_layout.addWidget(self.track_ema_checkbox)
        
        self.form_layout.addWidget(debug_group)
    
    def _connect_signals(self) -> None:
        """Connect signals and slots."""
        # Connect any signals here if needed
        pass
    
    def get_parameters(self) -> Dict[str, Any]:
        """
        Get all parameters from the UI.
        
        Returns:
            Dict containing all parameter values
        """
        params = {}
        
        # Get core parameters
        params.update(self.core_group.get_parameters())
        
        # Get strategy parameters
        params.update(self.strategy_group.get_parameters())
        
        # Add debug options
        params.update({
            'debug': self.debug_checkbox.isChecked(),
            'debug_paramflow': self.debug_paramflow_checkbox.isChecked(),
            'track_ema_calcs': self.track_ema_checkbox.isChecked()
        })
        
        return params
    
    def run_backtest(self) -> None:
        """Run the backtest with current parameters."""
        try:
            # Get parameters from UI
            params = self.get_parameters()
            
            # Show a message that the backtest is running
            QMessageBox.information(
                self, 
                "Backtest Started",
                "Running backtest with V4 parameters. This may take a few minutes.\n"
                "Check the console for progress."
            )
            
            # TODO: Implement backtest execution with V4 parameters
            # This will be implemented in a separate module
            logger.info("Starting backtest with parameters: %s", params)
            
            # For now, just show a success message
            QMessageBox.information(
                self,
                "Backtest Complete",
                "Backtest completed successfully.\n"
                "This is a placeholder - actual backtest execution will be implemented soon."
            )
            
        except Exception as e:
            # Show error message
            import traceback
            error_msg = f"Error running backtest: {str(e)}\n\n{traceback.format_exc()}"
            logger.error(error_msg)
            QMessageBox.critical(self, "Error", error_msg)

def main():
    """Main entry point for the V4 GUI."""
    from PySide6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    window = MainWindowV4()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
