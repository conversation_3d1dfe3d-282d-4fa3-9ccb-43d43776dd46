"""
trace_ema_signal_breakdown.py
-----------------------------
Traces the EMA signal generation process by breaking it down into three distinct
stages: Ratios, Ranks, and Signals. It calls the production model in a special
'trace_mode' to capture and save the intermediate data for each step.

Outputs:
1.  trace_ratios.csv: The raw EMAXAvg values used for ranking.
2.  trace_ranks.csv: The ordinal ranks of assets based on the ratios.
3.  trace_signal_allocation.csv: The final allocation weights (the signal).
4.  A detailed .txt log file of the entire process.
"""

import logging
import sys
import os
from pathlib import Path
import pandas as pd
from datetime import datetime

print(f"Initial PYTHONPATH: {os.environ.get('PYTHONPATH')}")
print(f"Initial sys.path: {sys.path}")

# --- Path Setup ---
import numpy as np

# Determine project root
_script_path = Path(__file__).resolve()
# This script is now in PROJECT_ROOT/, so its parent directory is PROJECT_ROOT.
_project_root = _script_path.parent 

# Ensure project root is in sys.path at the beginning
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))
    print(f"PROJECT_ROOT '{_project_root}' added to sys.path.")

print(f"sys.path after ensuring PROJECT_ROOT: {sys.path}")

# Attempt to import config.paths for further path configurations
try:
    from config import paths
    print(f"Successfully imported config.paths. PROJECT_ROOT according to paths.py: {paths.PROJECT_ROOT}")
    # paths.py should handle adding CUSTOM_LIB_PATH and other necessary paths
except ImportError as e:
    print(f"CRITICAL ERROR: Could not import config.paths. Error: {e}")
    print("Ensure config/paths.py exists and PROJECT_ROOT is correctly in sys.path before this import.")
    # Fallback or error handling if config.paths is essential and not found

# --- Settings Import ---
try:
    from v4.settings.settings_CPS_v4 import load_settings
    settings = load_settings()
    if not settings:
        raise RuntimeError("load_settings() returned empty or None.")
    print("Successfully loaded settings.")
except (ImportError, RuntimeError) as e:
    print(f"CRITICAL ERROR: Could not load settings. Error: {e}")
    sys.exit(1)

# --- Configuration & Output Paths ---
trace_config = settings.get('trace_ema_production_flow', {})
START_DATE_STR = trace_config.get('start_date', "2021-01-20")
END_DATE_STR = trace_config.get('end_date', "2021-02-28")

# User-specified output directory. All logs and CSVs will be saved here.
OUTPUT_BASE_DIR = Path(r"S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\test_logs")
OUTPUT_BASE_DIR.mkdir(exist_ok=True, parents=True)

run_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
file_suffix = f"{START_DATE_STR.replace('-', '')}_{END_DATE_STR.replace('-', '')}_{run_timestamp}"

# Define output file paths for each of the three trace stages
RATIOS_CSV = OUTPUT_BASE_DIR / f"trace_ratios_{file_suffix}.csv"
RANKS_CSV = OUTPUT_BASE_DIR / f"trace_ranks_{file_suffix}.csv"
SIGNAL_ALLOCATION_CSV = OUTPUT_BASE_DIR / f"trace_signal_allocation_{file_suffix}.csv"
POST_TRADE_ALLOCATION_CSV = OUTPUT_BASE_DIR / f"trace_post_trade_allocations_{file_suffix}.csv"
TRACE_LOG_TXT = OUTPUT_BASE_DIR / f"signal_breakdown_trace_{file_suffix}.txt"

# --- Logger Setup ---
console_logger = logging.getLogger("console_trace")
if not console_logger.handlers:
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(console_formatter)
    console_logger.addHandler(console_handler)
    console_logger.setLevel(logging.INFO)

trace_file_logger = logging.getLogger("file_trace")
if not trace_file_logger.handlers:
    file_handler = logging.FileHandler(TRACE_LOG_TXT, mode='w')
    file_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(file_formatter)
    trace_file_logger.addHandler(file_handler)
    trace_file_logger.setLevel(logging.INFO)

# --- Module Imports ---
import v4.models.ema_allocation_model_v4 as ema_model_module
from v4.engine.backtest_v4 import BacktestEngine
from v4.engine.portfolio_v4 import Portfolio # Though BacktestEngine creates its own portfolio, direct interaction might be needed for initial setup or specific access
from v4.engine.allocation import calculate_rebalance_orders
from v4.engine.orders_v4 import Order # Required by calculate_rebalance_orders
from v4.settings.settings_CPS_v4 import load_settings # Already imported, but ensure it's used for backtest params
from v4.config.allocation_rules_v4 import get_allocation_weights
from v4.engine.data_loader_v4 import load_data_for_backtest

console_logger.info("Script setup complete. Settings loaded and loggers configured.")
trace_file_logger.info("Script setup complete. Settings loaded and loggers configured.")

def _fetch_setting(settings_dict, path_list, default_value):
    """Helper to fetch a nested value from a dictionary."""
    current_level = settings_dict
    for key in path_list:
        if isinstance(current_level, dict) and key in current_level:
            current_level = current_level[key]
        else:
            return default_value
    return current_level

def get_ema_parameters(settings: dict) -> dict:
    """Constructs the 'parameters' dictionary for EMA calculations based on settings."""
    params = {}
    params['st_lookback'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'st_lookback'], _fetch_setting(settings, ['strategy', 'st_lookback'], 15)))
    params['mt_lookback'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'mt_lookback'], _fetch_setting(settings, ['strategy', 'mt_lookback'], 70)))
    params['lt_lookback'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'lt_lookback'], _fetch_setting(settings, ['strategy', 'lt_lookback'], 100)))
    params['min_weight'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'min_weight'], _fetch_setting(settings, ['strategy', 'min_weight'], 0.0)))
    params['max_weight'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'max_weight'], _fetch_setting(settings, ['strategy', 'max_weight'], 1.0)))
    # This parameter is strategy-specific for EMA model, not system_top_n
    params['top_n_strat_specific'] = ema_model_module._to_numeric(_fetch_setting(settings, ['ema_model', 'top_n'], _fetch_setting(settings, ['strategy', 'top_n'], 3)))
    return params

def main():
    global settings # Ensure we are using the globally loaded settings
    console_logger.info("Starting EMA Signal Breakdown trace.")
    try:
        trace_file_logger.info(f"EMA Signal Breakdown Trace Log - {run_timestamp}")
        trace_file_logger.info(f"Period: {START_DATE_STR} to {END_DATE_STR}")

        # --- Initialize Backtest Engine Components ---
        console_logger.info("Initializing Backtest Engine components...")
        trace_file_logger.info("Initializing Backtest Engine components...")

        backtest_settings = settings.get('backtest', {})
        strategy_settings = settings.get('strategy', {})

        initial_capital = float(backtest_settings.get('initial_capital', 1000000.0))
        # Ensure execution_delay is correctly fetched and is an integer
        raw_execution_delay = strategy_settings.get('execution_delay', 0)
        if isinstance(raw_execution_delay, dict):
            execution_delay = int(raw_execution_delay.get('default',0))
        elif isinstance(raw_execution_delay, (int, float, str)):
            execution_delay = int(raw_execution_delay)
        else:
            execution_delay = 0 # Default if type is unexpected
            console_logger.warning(f"Unexpected type for execution_delay: {type(raw_execution_delay)}. Defaulting to 0.")

        backtest_engine = BacktestEngine() # Uses CPS_v4 settings internally
        # The portfolio is managed within backtest_engine.execution_engine.portfolio
        # Initialize portfolio with initial capital if not done by BacktestEngine's __init__ sufficiently for standalone use
        # For this tracing, we'll rely on the portfolio state as managed by the execution engine part of BacktestEngine.
        # We need to ensure the execution engine's portfolio starts with the correct capital.
        # BacktestEngine's __init__ should set this up via its internal ExecutionEngine.
        # We can access it via backtest_engine.execution_engine.portfolio
        # Let's explicitly set initial capital for the portfolio used by the execution engine
        backtest_engine.execution_engine.portfolio.initial_capital = initial_capital
        backtest_engine.execution_engine.portfolio.cash = initial_capital
        backtest_engine.execution_engine.portfolio.current_holdings = {asset: 0 for asset in settings.get('data_loader',{}).get('tickers',[])}
        backtest_engine.execution_engine.portfolio.market_value_history = []
        backtest_engine.execution_engine.portfolio.cash_history = []
        backtest_engine.execution_engine.portfolio.total_value_history = []

        console_logger.info(f"Backtest Engine initialized with initial capital: ${initial_capital:,.2f} and execution delay: {execution_delay} days.")
        trace_file_logger.info(f"Backtest Engine initialized with initial capital: ${initial_capital:,.2f} and execution delay: {execution_delay} days.")

        # --- Load Data and Settings ---
        console_logger.info("Loading price data...")
        all_data = load_data_for_backtest()
        price_df = all_data["price_data"].copy()
        console_logger.info(f"Price data loaded: {price_df.shape[0]} rows, {price_df.shape[1]} assets.")
        trace_file_logger.info(f"Price data loaded. Shape: {price_df.shape}")

        console_logger.info("Loading CPS V4 settings...")
        settings = load_settings()
        ema_params = get_ema_parameters(settings)
        # Refactored to use new parameter paths: settings['system']['system_top_n']['default_value']
        from v4.settings.settings_utils import _extract_value_from_complex_dict
        system_top_n_raw = _fetch_setting(settings, ['system', 'system_top_n'], None)
        if system_top_n_raw is not None:
            system_top_n = ema_model_module._to_numeric(_extract_value_from_complex_dict(system_top_n_raw))
        else:
            system_top_n = None
        # Correctly extract the signal algorithm name from settings
        signal_algo_setting = _fetch_setting(settings, ['strategy', 'signal_algo'], {})
        if isinstance(signal_algo_setting, dict):
            signal_algo = signal_algo_setting.get('default', 'ema')
        else:
            signal_algo = signal_algo_setting or 'ema'
        
        trace_file_logger.info("--- Effective Settings for Trace ---")
        trace_file_logger.info(f"EMA Lookbacks: ST={ema_params['st_lookback']}, MT={ema_params['mt_lookback']}, LT={ema_params['lt_lookback']}")
        trace_file_logger.info(f"EMA Weights: Min={ema_params['min_weight']}, Max={ema_params['max_weight']}")
        trace_file_logger.info(f"EMA Strategy Top N: {ema_params['top_n_strat_specific']}")
        trace_file_logger.info(f"System Top N (for allocation rules): {system_top_n}")
        trace_file_logger.info(f"Signal Algorithm for Rules: {signal_algo}")
        trace_file_logger.info("-----------------------------------\n")

        # --- Prepare Date Range ---
        price_df = price_df.sort_index()
        start_date_dt = pd.to_datetime(START_DATE_STR)
        end_date_dt = pd.to_datetime(END_DATE_STR)
        analysis_dates = price_df.loc[start_date_dt:end_date_dt].index

        if analysis_dates.empty:
            console_logger.error("No price data available in the requested date range.")
            return

        console_logger.info(f"Tracing {len(analysis_dates)} business days from {START_DATE_STR} to {END_DATE_STR}.")

        # Lists to hold daily data for each trace step
        all_ratios_data = []
        all_ranks_data = []
        all_signals_data = []
        all_post_trade_allocations_data = [] # For post-trade allocations

        for current_date in analysis_dates:
            trace_file_logger.info(f"\n========== Tracing for Date: {current_date.strftime('%Y-%m-%d')} ==========")
            current_price_slice = price_df.loc[:current_date]

            # Set module-level parameters for the production model call
            ema_model_module.st_lookback = ema_params['st_lookback']
            ema_model_module.mt_lookback = ema_params['mt_lookback']
            ema_model_module.lt_lookback = ema_params['lt_lookback']
            ema_model_module.min_weight = ema_params['min_weight']
            ema_model_module.max_weight = ema_params['max_weight']

            # --- Call Production Model in Trace Mode ---
            trace_file_logger.info("--- Calling Production Model with trace_mode=True ---")
            
            model_output = ema_model_module.ema_allocation_model_updated(
                price_data=current_price_slice, 
                trace_mode=True
            )
            
            if not model_output or len(model_output) != 4:
                console_logger.error(f"ERROR: Trace mode call failed for date {current_date.strftime('%Y-%m-%d')}. Expected 4 return values.")
                trace_file_logger.error(f"ERROR: Trace mode call failed for date {current_date.strftime('%Y-%m-%d')}. Model output was: {model_output}")
                continue

            final_weights_dict, ratios_series, ranks_df, signal_series = model_output
            
            trace_file_logger.info("--- Step 1: Ratios (EMAXAvg) ---")
            trace_file_logger.info(f"Ratios received:\n{ratios_series.to_string()}")
            ratios_series.name = current_date
            all_ratios_data.append(ratios_series)

            trace_file_logger.info("--- Step 2: Ranks ---")
            trace_file_logger.info(f"Ranks received:\n{ranks_df.to_string()}")
            ranks_df['Date'] = current_date
            all_ranks_data.append(ranks_df)

            trace_file_logger.info("--- Step 3: Signal (Allocation) ---")
            trace_file_logger.info(f"Signal received:\n{signal_series.to_string()}")
            signal_series.name = current_date
            all_signals_data.append(signal_series)

            # --- Backtest Engine Interaction for Post-Trade Allocations ---
            trace_file_logger.info(f"--- Processing Backtest Engine for date: {current_date.strftime('%Y-%m-%d')} ---")
            raw_prices_for_date = price_df.loc[current_date] # Use a new variable name for clarity
            
            if isinstance(raw_prices_for_date, pd.DataFrame):
                # This can happen if price_df.index has duplicate dates. Take the first row.
                trace_file_logger.warning(f"Duplicate index found for date {current_date}. Using first row of prices.")
                current_prices_for_engine = raw_prices_for_date.iloc[0].to_dict()
            elif isinstance(raw_prices_for_date, pd.Series):
                current_prices_for_engine = raw_prices_for_date.to_dict()
            else:
                trace_file_logger.error(f"Unexpected type for raw_prices_for_date: {type(raw_prices_for_date)} for date {current_date}. Skipping this date for engine processing.")
                # Fallback to assign raw_prices_for_date, which might not be ideal but allows logging its type.
                current_prices_for_engine = raw_prices_for_date 

            # IMMEDIATE DIAGNOSTIC AFTER CONVERSION ATTEMPT
            trace_file_logger.info(f"Trace Script DIAGNOSTIC: Type of current_prices_for_engine IMMEDIATELY AFTER conversion: {type(current_prices_for_engine)}")

            # 1. Process Pending Orders for current_date
            # --- Part 1: Process orders from previous signals scheduled for today ---
            orders_from_previous_signals = []
            if current_date in backtest_engine.pending_orders:
                orders_from_previous_signals = backtest_engine.pending_orders.pop(current_date, []) # Use pop with default
                trace_file_logger.info(f"Found {len(orders_from_previous_signals)} orders from previous signals pending for {current_date.strftime('%Y-%m-%d')}")
            
            if orders_from_previous_signals:
                # Mark to market before executing these, to use current_date's prices for execution
                backtest_engine.execution_engine.portfolio.mark_to_market(current_date, current_prices_for_engine)
                executed_trades_part1 = backtest_engine.execution_engine.execute_orders(orders_from_previous_signals, execution_date=current_date, prices=current_prices_for_engine)
                trace_file_logger.info(f"Executed {len(executed_trades_part1)} trades from previous signals for {current_date.strftime('%Y-%m-%d')}")
                for trade in executed_trades_part1:
                    # Access order_type via trade.order, and use execution_price
                    trace_file_logger.debug(f"  Executed Trade: {trade.symbol} {trade.quantity} @ {trade.execution_price:.2f} ({trade.order_type}) - Value: {trade.amount:.2f}, Commission: {trade.commission:.2f}")
            else:
                trace_file_logger.info(f"No orders from previous signals to execute for {current_date.strftime('%Y-%m-%d')}")

            # --- Part 2: Generate today's signal and prepare new orders ---
            # signal_series is the pre-trade target allocation from the EMA model for current_date (already generated earlier in the loop)
            target_weights_for_new_orders = signal_series 
            new_orders_from_today_signal = []

            if not target_weights_for_new_orders.empty:
                # Mark to market *before* calculating rebalance orders for today's signal.
                # This ensures rebalance orders are based on the portfolio state *after* any trades from previous signals were executed.
                backtest_engine.execution_engine.portfolio.mark_to_market(current_date, current_prices_for_engine)
                
                new_orders_from_today_signal = calculate_rebalance_orders(
                    backtest_engine.execution_engine.portfolio, 
                    target_weights_for_new_orders.to_dict(), 
                    current_prices_for_engine
                )
                if new_orders_from_today_signal:
                    trace_file_logger.info(f"Generated {len(new_orders_from_today_signal)} new orders based on today's signal for {current_date.strftime('%Y-%m-%d')}")
                else:
                    trace_file_logger.info(f"No new orders generated from today's signal for {current_date.strftime('%Y-%m-%d')}")
            else:
                trace_file_logger.info(f"Today's signal is empty, no new orders generated for {current_date.strftime('%Y-%m-%d')}")

            # --- Part 3: Execute or Schedule today's new orders ---
            if new_orders_from_today_signal:
                if execution_delay == 0:
                    # Execute T+0 orders immediately
                    trace_file_logger.info(f"T+0 Execution: Processing {len(new_orders_from_today_signal)} orders generated from today's signal.")
                    # Mark to market again before T+0 execution, ensuring it uses latest state.
                    backtest_engine.execution_engine.portfolio.mark_to_market(current_date, current_prices_for_engine)
                    executed_trades_t0 = backtest_engine.execution_engine.execute_orders(new_orders_from_today_signal, execution_date=current_date, prices=current_prices_for_engine)
                    trace_file_logger.info(f"Executed {len(executed_trades_t0)} T+0 trades from today's signal for {current_date.strftime('%Y-%m-%d')}")
                    for trade in executed_trades_t0:
                        trace_file_logger.debug(f"  Executed (T+0) Trade: {trade.symbol} {trade.quantity} @ {trade.execution_price:.2f} ({trade.order_type})")
                else:
                    # Schedule for a future date
                    execution_date_for_new_orders = current_date + pd.Timedelta(days=execution_delay)
                    if execution_date_for_new_orders not in backtest_engine.pending_orders:
                        backtest_engine.pending_orders[execution_date_for_new_orders] = []
                    backtest_engine.pending_orders[execution_date_for_new_orders].extend(new_orders_from_today_signal)
                    trace_file_logger.info(f"Scheduled {len(new_orders_from_today_signal)} new orders (from today's signal) for execution on {execution_date_for_new_orders.strftime('%Y-%m-%d')}")

            # --- DIAGNOSTIC LOGGING ---
            trace_file_logger.info(f"Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE mark_to_market: {type(current_prices_for_engine)}")
            
            # --- Part 4: Capture final Post-Trade Allocation for current_date ---
            # This must happen *after* all executions for current_date, including T+0 trades.
            backtest_engine.execution_engine.portfolio.mark_to_market(current_date, current_prices_for_engine) 
            
            # --- DIAGNOSTIC LOGGING ---
            trace_file_logger.info(f"Trace Script DIAGNOSTIC: Type of current_prices_for_engine BEFORE direct get_weights call: {type(current_prices_for_engine)}")
            
            final_post_trade_weights = backtest_engine.execution_engine.portfolio.get_weights(current_prices_for_engine)
            final_post_trade_weights_series = pd.Series(final_post_trade_weights, name=current_date).fillna(0.0)
            all_post_trade_allocations_data.append(final_post_trade_weights_series) # Ensure this list is correctly named
            trace_file_logger.info(f"Final post-trade allocations for {current_date.strftime('%Y-%m-%d')}:\n{final_post_trade_weights_series.to_string() if not final_post_trade_weights_series.empty else 'Empty'}")

        # --- Aggregate and Save Trace Files ---
        console_logger.info("\nAggregating and saving trace files...")

        # --- Data Aggregation and CSV Output ---
        if not all_ratios_data:
            console_logger.warning("No data was generated to save.")
            trace_file_logger.warning("No data was generated to save.")
        else:
            # 1. Ratios CSV (Full History)
            ratios_df = pd.concat(all_ratios_data, axis=1).T
            ratios_df.index.name = "Date"
            ratios_df.to_csv(RATIOS_CSV)
            console_logger.info(f"Full history ratios saved to: {RATIOS_CSV}")
            trace_file_logger.info(f"Full history ratios saved to: {RATIOS_CSV}")

            # 2. Ranks CSV (Matrix Format, Full History)
            ranks_long_df = pd.concat(all_ranks_data, ignore_index=True)
            ranks_matrix_df = ranks_long_df.pivot(index='Date', columns='Asset', values='Rank_Ordinal')
            ranks_matrix_df.to_csv(RANKS_CSV)
            console_logger.info(f"Ranks matrix saved to: {RANKS_CSV}")
            trace_file_logger.info(f"Ranks matrix saved to: {RANKS_CSV}")

            # 3. Signal Allocation CSV (Full History)
            signals_df = pd.concat(all_signals_data, axis=1).T
            signals_df.index.name = "Date"
            signals_df.to_csv(SIGNAL_ALLOCATION_CSV)
            console_logger.info(f"Signal allocations saved to: {SIGNAL_ALLOCATION_CSV}")
            trace_file_logger.info(f"Signal allocations saved to: {SIGNAL_ALLOCATION_CSV}")

            # 4. Post-Trade Allocation CSV (Full History)
            if all_post_trade_allocations_data:
                post_trade_df = pd.concat(all_post_trade_allocations_data, axis=1).T.fillna(0.0)
                post_trade_df.index.name = "Date"
                post_trade_df.to_csv(POST_TRADE_ALLOCATION_CSV)
                console_logger.info(f"Post-trade allocations saved to: {POST_TRADE_ALLOCATION_CSV}")
                trace_file_logger.info(f"Post-trade allocations saved to: {POST_TRADE_ALLOCATION_CSV}")
            else:
                console_logger.warning("No post-trade allocation data was generated to save.")
                trace_file_logger.warning("No post-trade allocation data was generated to save.")

        console_logger.info(f"\nDetailed trace log saved to: {TRACE_LOG_TXT}")
        trace_file_logger.info("Trace script finished.")

    except Exception as e:
        console_logger.error(f"An unexpected error occurred: {e}", exc_info=True)
        trace_file_logger.error(f"An unexpected error occurred: {e}", exc_info=True)
    finally:
        console_logger.info("EMA signal breakdown trace finished.")
        trace_file_logger.info("EMA signal breakdown trace finished.")

if __name__ == "__main__":
    main()
