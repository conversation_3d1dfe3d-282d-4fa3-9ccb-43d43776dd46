---
trigger: glob
globs: *.py
---

NEVER create ANY Python module over 450 lines of code. When working with larger functionality:

1. Split functionality into multiple smaller modules with clear responsibilities
2. Create utility modules for shared functionality
3. Use a shell/facade pattern where a main module imports and orchestrates functionality from smaller modules
4. Document module relationships clearly in docstrings
5. When editing large existing files, make minimal targeted changes and avoid full-file replacements
6. For analysis tools like ai_context_generator.py, split into separate modules for each analysis type

This rule prevents AI editing issues with large files and improves maintainability.
