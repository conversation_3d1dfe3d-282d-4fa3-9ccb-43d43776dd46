#!/usr/bin/env python3
"""
v4/tests/test_trade_filter.py
Test script for trade_filter module functionality.

Tests the fetch_current_allocation, compute_percent_diff, and filter_trades functions.
"""

import sys
from pathlib import Path
import pandas as pd
from datetime import date

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

from v4.engine.portfolio_v4 import Portfolio
from v4.utils.trade_filter import fetch_current_allocation, compute_percent_diff, filter_trades, is_business_day


def test_fetch_current_allocation():
    """Test fetch_current_allocation function."""
    print("Testing fetch_current_allocation...")
    
    # Create a portfolio with some positions
    portfolio = Portfolio()
    
    # Add some positions (simulate having bought some assets)
    test_prices = {'SPY': 400.0, 'TLT': 100.0, 'GLD': 180.0}
    portfolio.add_position('SPY', 100, 400.0)  # $40,000
    portfolio.add_position('TLT', 200, 100.0)  # $20,000
    portfolio.cash = 40000  # $40,000 in cash
    # Total portfolio value: $100,000
    
    # Fetch current allocation
    allocation = fetch_current_allocation(portfolio)
    print(f"Current allocation: {allocation}")
    
    # Verify allocations sum to 1.0
    total_allocation = allocation.sum()
    print(f"Total allocation: {total_allocation:.4f}")
    assert abs(total_allocation - 1.0) < 0.01, f"Allocations should sum to 1.0, got {total_allocation}"
    
    print("✓ fetch_current_allocation test passed\n")


def test_compute_percent_diff():
    """Test compute_percent_diff function."""
    print("Testing compute_percent_diff...")
    
    # Create test allocations
    current = pd.Series({'SPY': 0.4, 'TLT': 0.2, 'GLD': 0.1, 'Cash': 0.3})
    proposed = pd.Series({'SPY': 0.5, 'TLT': 0.25, 'GLD': 0.05, 'Cash': 0.2})
    
    # Compute percentage differences
    percent_diff = compute_percent_diff(current, proposed)
    print(f"Current allocation: {current.to_dict()}")
    print(f"Proposed allocation: {proposed.to_dict()}")
    print(f"Percentage differences: {percent_diff.to_dict()}")
    
    # Check some expected values
    # SPY: (0.5 - 0.4) / 0.4 = 0.25 (25% increase)
    expected_spy = abs(0.5 - 0.4) / 0.4
    assert abs(percent_diff['SPY'] - expected_spy) < 0.01, f"SPY diff should be {expected_spy}, got {percent_diff['SPY']}"
    
    print("✓ compute_percent_diff test passed\n")


def test_filter_trades():
    """Test filter_trades function."""
    print("Testing filter_trades...")
    
    # Create test allocations with some changes that exceed threshold
    current = pd.Series({'SPY': 0.4, 'TLT': 0.2, 'GLD': 0.1, 'Cash': 0.3})
    # SPY change: 0.5 - 0.4 = 0.1, percent diff = 0.1/0.4 = 0.25 (25% > 2% threshold)
    # TLT change: 0.25 - 0.2 = 0.05, percent diff = 0.05/0.2 = 0.25 (25% > 2% threshold)
    # GLD change: 0.05 - 0.1 = -0.05, percent diff = 0.05/0.1 = 0.5 (50% > 2% threshold)
    proposed = pd.Series({'SPY': 0.5, 'TLT': 0.25, 'GLD': 0.05, 'Cash': 0.2})
    
    # Test basic filtering
    result = filter_trades(current, proposed)
    
    print(f"Current allocation: {current.to_dict()}")
    print(f"Proposed allocation: {proposed.to_dict()}")
    print(f"Allowed trades: {result['allowed'].to_dict()}")
    print(f"Filtered trades: {result['filtered'].to_dict()}")
    
    # With default 2% threshold, all these changes should be filtered out
    assert len(result['filtered']) > 0, "Some trades should be filtered with large changes"
    
    print("✓ filter_trades test passed\n")


def test_business_day_check():
    """Test business day check functionality."""
    print("Testing business day check...")
    
    # Create mock price data for a few dates
    dates = pd.date_range('2023-01-01', periods=10, freq='D')  # Include weekends
    price_data = pd.DataFrame({
        'SPY': [400] * 10,
        'TLT': [100] * 10
    }, index=dates)
    
    # Remove weekends to simulate business days only
    price_data = price_data[price_data.index.weekday < 5]  # Monday=0, Friday=4
    
    # Test a business day
    business_date = price_data.index[0].date()
    is_biz_day = is_business_day(business_date, price_data)
    print(f"Date {business_date} is business day: {is_biz_day}")
    assert is_biz_day, f"Date {business_date} should be a business day"
    
    # Test a weekend (assuming it's not in price data)
    weekend_date = date(2023, 1, 7)  # Saturday
    is_weekend = is_business_day(weekend_date, price_data)
    print(f"Date {weekend_date} is business day: {is_weekend}")
    # This might pass or fail depending on whether the date is in price data
    
    print("✓ business day check test completed\n")


def main():
    """Run all tests."""
    print("=== Trade Filter Module Tests ===\n")
    
    try:
        test_fetch_current_allocation()
        test_compute_percent_diff()
        test_filter_trades()
        test_business_day_check()
        
        print("=== All Tests Passed! ===")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
