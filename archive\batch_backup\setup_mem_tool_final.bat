@echo off
SETLOCAL

echo === DEBUG MODE: MEMORY TOOL SETUP ===
echo.

:: Set Python executable path
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe

:: Verify Python exists
echo Checking Python executable...
if not exist "%PYTHON_EXE%" (
    echo [X] ERROR: Python not found at %PYTHON_EXE%
    pause
    exit /b 1
)
echo [✓] Python found at: %PYTHON_EXE%
echo.

:: Show Python version
echo Python Version:
"%PYTHON_EXE%" --version
echo.

:: setup_mem_tool_final.bat
:: Installs all dependencies for the AI Context Generator tool (no admin required)

echo [*] Setting up AI Context Generator dependencies...

:: Set Python environment
echo [*] Using Python environment...
set PYTHON=python
set PIP=pip

:: Install required Python packages
echo [*] Installing Python packages...
"%PYTHON_EXE%" -m pip install --upgrade pip
"%PYTHON_EXE%" -m pip install graphviz astunparse networkx

:: Portable Graphviz setup (no installation required)
echo [*] Setting up Graphviz...
set GRAPHVIZ_DIR=graphviz-portable
set GRAPHVIZ_URL=https://github.com/tschoonj/GTK4-Runtime-Portable/releases/download/4.14.0-4.1.1/gtk4-runtime-4.14.0-4.1.1-2024-03-21-ts-win64.zip
set GRAPHVIX_ZIP=graphviz-portable.zip

if not exist "%GRAPHVIZ_DIR%" (
    mkdir "%GRAPHVIZ_DIR%"
    echo [*] Downloading Graphviz portable...
    powershell -Command "(New-Object System.Net.WebClient).DownloadFile('%GRAPHVIZ_URL%', '%GRAPHVIX_ZIP%')"
    
    echo [*] Extracting Graphviz...
    powershell -Command "Expand-Archive -Path '%GRAPHVIX_ZIP%' -DestinationPath '%GRAPHVIZ_DIR%' -Force"
    del /f /q %GRAPHVIX_ZIP%
    
    echo [*] Setting up environment...
    setx GRAPHVIZ_DOT "%CD%\%GRAPHVIZ_DIR%\bin\dot.exe"
    set PATH=%CD%\%GRAPHVIZ_DIR%\bin;%PATH%
) else (
    echo [*] Graphviz portable already exists, skipping download
    set PATH=%CD%\%GRAPHVIZ_DIR%\bin;%PATH%
)

:: Verify Graphviz
echo [*] Verifying Graphviz...
where dot >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Graphviz not found in PATH. Trying alternative method...
    if exist "%CD%\%GRAPHVIZ_DIR%\bin\dot.exe" (
        set "PATH=%CD%\%GRAPHVIZ_DIR%\bin;%PATH%"
    ) else (
        echo [ERROR] Could not set up Graphviz. Please install it manually.
    )
)

:: Create required directories
echo [*] Creating required directories...
if not exist "memory-bank\code_analysis\graphviz" (
    mkdir "memory-bank\code_analysis\graphviz"
)

echo [*] Setup complete!
echo.
echo [*] To use the AI Context Generator, run:
echo     python docs\memory_tool\ai_context_gen_v2.py
echo.
echo [*] If you encounter any issues, please restart your terminal.
echo.

pause

(
echo import sys
echo print('Python executable:', sys.executable)
echo print('Python version:', sys.version)
echo print('\nPython Path:')
echo for p in sys.path:^
echo ^    print(' ', p)
echo.
echo try:^
echo     import pyan3^
echo     print('pyan3:', pyan3.__file__)^
echo except Exception as e:^
echo     print('pyan3 import failed:', str(e))
echo.
echo try:^
echo     import pylint^
echo     print('pylint:', pylint.__file__)^
echo except Exception as e:^
echo     print('pylint import failed:', str(e))
echo.
echo try:^
echo     import graphviz^
echo     print('graphviz:', graphviz.__file__)^
echo except Exception as e:^
echo     print('graphviz import failed:', str(e))
) > check_imports.py

"%PYTHON_EXE%" check_imports.py
del check_imports.py

echo.
pause
