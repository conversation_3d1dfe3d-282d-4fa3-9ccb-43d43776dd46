# Project Brief

This project is a modular, auditable framework for backtesting financial asset allocation strategies. It supports multiple allocation models (including EMA, equal weight, momentum, min variance, risk parity), parameter optimization, and detailed performance and trade reporting. The v2 engine emphasizes strict separation of concerns, cost basis tracking, and compliance with Custom Function Library standards.

- Modular design: swap strategies easily
- Comprehensive reporting and audit trails
- Strict path/parameter management (per user rules)
- Output: XLSX files with required naming conventions

_Expand with additional project goals or requirements as needed._

**Current status (April 2025):**
- In the middle of V3 GUI testing phase (run_ema_v3_gui_test.bat)
- Parameter audit and benchmark grouping complete
- No V3 backtests or reports have been generated yet
- Major concern: V3 reporting is untested
