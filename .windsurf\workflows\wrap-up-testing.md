---
description: Testing Flow - Update Docs before new sesssion
---

1. Update
 'S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\Problem_Changes_Log_CPS_v4.md' with:
   - What was completed, with changes made in detail

2. Update
 'S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\Task_List_CPS_v4.md'
at the top with:
   - A 'Current Task & Subtask Status' section showing the exact current task and next actionable subtask
   - What is in process (task + subtask)
   - Recommended bat file and suggestions
3. If new modules or production functions were created, update 'systemFiles+Flow_AI.md'
4. Do not delete existing content unless it is a direct replacement
5. Confirm all documentation updates before proceeding
