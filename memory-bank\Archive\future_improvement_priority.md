# Future Improvement Priority

This document outlines proposed improvements, prioritized for iterative implementation.

## Future Improvement Roadmap

- Automate batch test approval (user should not have to manually approve each run)
- Resolve all legacy parameter group warnings in reporting
- Refactor chart generation for robustness and to eliminate warnings
- Address all deprecation warnings in pandas/matplotlib

## 1. High Priority
- Consolidate overlapping docs in `/docs`:
  - Merge `parameter_opt.md` + `parameter_optimization.md` into a single, comprehensive guide.
  - Remove or archive redundant `README_Backtest_engine_flow.md` after confirming coverage in `backtest_engine_v2.md`.
- Create dedicated Memory Bank context files (in `memory-bank/`):
  - `dateHandlingContext.md` (based on `README_DATE_HANDLING.md`)
  - `reportingContext.md` (based on `performance_reporting_standards.md`)
  - `optimizationContext.md` (based on parameter optimization docs)
  - `moduleMappingContext.md` (based on `module_function_name_comparison.md`)

## 2. Medium Priority
- Archive or relocate low-value docs to `docs/archive/`:
  - `GUI notes 042625.txt`
  - `3rd party_Backtest_Engine.md`
- Add `handoffContext.md` to Memory Bank summarizing `handoff.md` for maintainers.

## 3. Low Priority
- Integrate historical evolution (`Financial Backtesting Toolset Evolution - Grok.md`) as reference or appendix.
- Establish a periodic review process for Memory Bank and docs consistency.

---

**Next Steps**:
1. Review and approve high-priority actions.
2. Assign owners and timelines.
3. Implement and update Memory Bank accordingly.
