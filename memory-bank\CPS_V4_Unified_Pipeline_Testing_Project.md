# CPS V4 Unified Pipeline Testing Strategy - Project Documentation

**Status:** ACTIVE TOP PRIORITY OPEN PROJECT  
**Date Created:** 2025-06-30  
**Last Updated:** 2025-06-30  

## Project Overview

This project implements Step 9 of the broader CPS V4 plan: "Automated Testing Strategy" for the unified pipeline. The goal is to create comprehensive tests that validate the complete signal generation and trading workflow using full production code.

## Key Requirements (from original task)

1. **Use Full Production Code**: No mocks, no synthetic test harnesses - tests must use the actual production pipeline
2. **Small Historical Date Range**: Test on a real but limited historical period defined in settings test profile
3. **Schema Validation**: Assert existence and schema of all four output files
4. **Baseline Metrics**: Compare summary metrics against thresholds from baseline runs
5. **Clean Teardown**: Tests must clean up artifacts after execution

## Files Created/Modified

### 1. Test Settings File
**File:** `tests/v4/test_settings_small_range.ini`
- **Purpose:** Isolated test configuration with small date range (2020-01-01 to 2020-03-31)
- **Key Features:**
  - 3-month historical window for fast execution
  - Disabled chart generation and Excel output for speed
  - Same tickers as production (SPY, SHV, EFA, TLT, PFF)
  - EMA strategy with non-optimized parameters
  - Retry settings: max_retries=1, delay=2 seconds

### 2. Unified Pipeline Test
**File:** `tests/v4/test_unified_pipeline.py`
- **Purpose:** Main test file that executes the unified pipeline and validates outputs
- **Test Approach:** 
  - Runs `run_unified_pipeline()` with test settings
  - Validates existence of four key output files
  - Checks DataFrame schemas and shapes
  - Validates no null values in critical outputs
  - Clean teardown with pytest fixtures

## Expected Output Files (3 primary files to validate)

1. **signals_output_{timestamp}.csv** - Generated signals with Date index and ticker columns (timestamped)
2. **allocation_history_{timestamp}.csv** - Portfolio allocation over time (timestamped)
3. **trade_log_{timestamp}.csv** - Complete trade execution log (timestamped)

**Note:** Optimized for unified pipeline - single timestamped signal file eliminates duplicate outputs

## Key Architecture Decisions

### What We KEPT from existing approach:
- **Standardized Logging:** Consistent milestone logging across phases for observability
- **Production Code Flow:** Using actual `run_unified_pipeline()` function
- **Configuration-Driven:** All settings controlled via test-specific .ini file

### What We REJECTED based on feedback:
- **Retry/Fallback Logic:** No automatic retries on failures - if it fails, we trace and fix
- **Transient Connectivity Handling:** Not applicable to this setup
- **Complex Error Recovery:** No fallbacks - failures should be investigated, not masked

## Test Execution Flow

```
1. Load test settings (tests/v4/test_settings_small_range.ini)
2. Clean any existing output files
3. Execute run_unified_pipeline(custom_settings_file=test_settings)
4. Validate 3 timestamped output files exist
5. Load and validate DataFrame schemas:
   - signals_output_{timestamp}.csv: shape[1] == 5 (Date + 4 tickers)
   - allocation_history_{timestamp}.csv: shape[1] == 6 (Date + 5 allocations)
   - trade_log_{timestamp}.csv: shape[1] == 11 (trade execution fields)
6. Check for null values in critical columns
7. Clean up test artifacts
```

## Schema Expectations (optimized for unified pipeline)

### Signals Output
- **Format:** CSV (timestamped)
- **Columns:** Date, SPY, SHV, EFA, TLT, PFF
- **Validation:** All values sum to 1.0 per row (within tolerance)

### Allocation History  
- **Format:** CSV
- **Columns:** Date, SPY, SHV, TLT, PFF, EFA
- **Validation:** No null values, reasonable allocation ranges

### Trade Log
- **Format:** CSV  
- **Columns:** trade_id, order_id, symbol, quantity, order_date, order_type, execution_date, execution_price, commission, amount, pnl
- **Validation:** All trades have required fields populated

## Current Status

### COMPLETED ✅
- Created test settings file with small historical range
- Created basic unified pipeline test structure
- Identified expected output files and schemas
- Established clean teardown approach

### TODO (Next Session Priorities)
1. **Enhance Test Coverage:** Add baseline metric comparisons (row counts, value ranges)
2. **Performance Assertions:** Add timing/performance thresholds
3. **Edge Case Testing:** Test with missing data periods, market holidays
4. **Integration with CI:** Make tests runnable in automated environment

### BLOCKERS/ISSUES
- Need to run baseline execution to establish metric thresholds
- May need to adjust date ranges based on available historical data
- Test execution time needs to be validated (should be < 30 seconds)

## Next Session Action Plan

1. **Run baseline test execution** to establish expected metrics
2. **Add baseline comparison logic** to test file
3. **Validate test execution time** meets performance requirements
4. **Add error condition testing** (what happens when data is missing)
5. **Document test execution instructions** for other developers

## File Locations

```
tests/v4/
├── test_settings_small_range.ini    # Test configuration
├── test_unified_pipeline.py         # Main test file
├── test_integration_pipeline.py     # Existing integration tests
└── production_modules/              # Copied production modules

v4_trace_outputs/                    # Test output directory
├── signals_output.parquet
├── allocation_history.csv
├── trade_log.csv
└── Signalwtg_output.parquet
```

## Key Principles Applied

1. **Use Full Production Code:** Tests import and execute actual pipeline modules
2. **Configuration-Driven:** Test behavior controlled via .ini files, not hardcoded
3. **Fast Execution:** Small date range ensures quick test cycles
4. **Clean Environment:** Proper setup/teardown prevents test pollution
5. **Clear Assertions:** Specific, actionable test failures
6. **No Magic Fallbacks:** If something fails, we want to know why and fix it

---

**Next Session Goals:**
1. Execute baseline run to establish thresholds
2. Complete test implementation with metric comparisons
3. Validate end-to-end test execution
4. Document test execution procedures
