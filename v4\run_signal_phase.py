"""
v4/run_signal_phase.py
Signal generation phase script for the decoupled backtest architecture.
Part of the CPS v4 compliant backtest system.

# Note: Uses CSV format only (<PERSON><PERSON><PERSON> eliminated for performance)
This script handles the signal generation process and saves the output to standardized file formats.
It loads price data using the existing data loader and generates signals using the configured
signal generator, then saves the signals in CSV format for optimal loading performance.
"""

import os
import sys
from pathlib import Path
import pandas as pd
import logging
from datetime import datetime

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import core V4 components
from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.signal_generator_v4 import create_signal_generator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

def run_signal_phase():
    """Run signal generation phase and save output."""
    logger.info("[MILESTONE] Starting Signal Generation Phase")
    
    # Load settings and data
    settings = load_settings()
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    
    # Log data information
    logger.info(f"Loaded price data with shape {price_data.shape}")
    logger.info(f"Date range: {price_data.index.min()} to {price_data.index.max()}")
    logger.info(f"Tickers: {', '.join(price_data.columns)}")
    
    # Create and run signal generator
    model_name = settings.get('model', {}).get('name', 'equal_weight')
    logger.info(f"Using signal model: {model_name}")
    
    signal_generator = create_signal_generator(
        strategy_name=model_name,
        settings=settings
    )
    
    # Generate signals
    logger.info("Generating signals...")
    signals_dict = signal_generator.generate_signals(price_data)
    
    # Convert dictionary of signals to DataFrame
    if isinstance(signals_dict, dict):
        # Check if it's a dictionary of dictionaries (timestamped signals)
        if signals_dict and isinstance(next(iter(signals_dict.values())), dict):
            # Create a DataFrame with dates as index and tickers as columns
            signals = pd.DataFrame()
            for date, allocations in signals_dict.items():
                signals.loc[date] = pd.Series(allocations)
        else:
            # Single date signals, create a DataFrame with a single row
            signals = pd.DataFrame([signals_dict], index=[price_data.index[-1]])
    elif isinstance(signals_dict, pd.DataFrame):
        # Already a DataFrame
        signals = signals_dict
    else:
        logger.error(f"Unexpected signal format: {type(signals_dict)}")
        raise ValueError(f"Unexpected signal format: {type(signals_dict)}")
    
    # Ensure signals align with price data
    if signals.empty:
        logger.warning("Generated signals are empty!")
    else:
        logger.info(f"Generated signals with shape {signals.shape}")
    
    # Save outputs
    output_dir = _project_root / "v4_trace_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # Add timestamp to filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save as CSV only with timestamp (optimized for unified pipeline)
    output_file = output_dir / f"signals_output_{timestamp}.csv"
    signals.to_csv(str(output_file))
    
    logger.info(f"Signal generation complete. Outputs saved to:")
    logger.info(f"  - {output_file} (CSV)")
    
    return str(output_file)

if __name__ == "__main__":
    run_signal_phase()
