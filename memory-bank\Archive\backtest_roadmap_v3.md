# Backtest V3 Roadmap

## Purpose

This roadmap outlines the architecture, priorities, and next steps for the V3 backtest engine and parameter system. It is intended for use in the memory-bank for persistent project context and planning.

---

## Core Objectives

- Unified parameter handling from GUI → engine → reports
- Plug-and-play strategy support with standardized parameter interfaces
- Robust type safety and validation across all boundaries
- Comprehensive documentation and traceability
- Backward compatibility with V2 config and workflows

---

## Current State (as of 2025-04-29)

- V3 parameter registry and class hierarchy implemented
- GUI integration via `v3_gui_core.py` and `GuiParameterManager`
- Parameter optimization flows through `ParameterOptimizer`
- Strategy modularity via `StrategyParameterSet`
- Legacy issues (e.g., execution_delay tuple handling) isolated and documented
- Documentation in `parameter_system_v3.md` and memory-bank is current

---

## Next Steps

1. **Finalize and enforce unified parameter interfaces**
2. **Expand test coverage for parameter optimization and edge cases**
3. **Document parameter flow and extensibility guidelines**
4. **Implement additional plug-and-play strategies**
5. **Audit for legacy/confusing code and refactor as needed**
6. **Keep memory-bank and documentation in sync with codebase**

---

## Architectural Priorities

- All parameter definitions live in the registry and are grouped by core/strategy
- Strategies self-register and expose required parameters
- GUI and reporting layers are fully decoupled from parameter internals
- All optimizable parameters are type-checked and validated before use
- All configuration changes are reflected in the memory-bank for future reference

---

## Module Classification

### Active in V3 Main Data Engine Output Flow

#### v3_engine

| Module                                    | Description                               |
| ----------------------------------------- | ----------------------------------------- |
| v3_engine/parameter_registry.py           | Central parameter registry for V3         |
| v3_engine/parameters.py                   | Parameter type definitions and validation |
| v3_engine/parameter_optimizer.py          | Parameter optimization for V3             |
| v3_engine/strategy_parameter_set.py       | Strategy parameter management             |
| v3_engine/gui_parameter_manager.py        | GUI parameter control management          |
| v3_engine/performance_reporter_adapter.py | Adapter for performance reporting         |
| v3_engine/__init__.py                     | Package initialization                    |

#### app/gui

| Module                            | Description                       |
| --------------------------------- | --------------------------------- |
| app/gui/v3_gui_core.py            | V3 GUI main window implementation |
| app/gui/v3_parameter_widgets.py   | Widget creation for parameters    |
| app/gui/v3_gui_actions.py         | GUI action handlers               |
| app/gui/v3_register_parameters.py | Parameter registration for GUI    |

#### data

| Module              | Description                |
| ------------------- | -------------------------- |
| data/data_loader.py | Data loading for backtests |
| data/__init__.py    | Package initialization     |

#### reporting

| Module                             | Description                   |
| ---------------------------------- | ----------------------------- |
| reporting/performance_reporting.py | Performance report generation |
| reporting/allocation_report.py     | Allocation report generation  |
| reporting/__init__.py              | Package initialization        |

#### utils

| Module              | Description             |
| ------------------- | ----------------------- |
| utils/date_utils.py | Date handling utilities |
| utils/trade_log.py  | Trade logging utilities |

#### visualization

| Module                              | Description                      |
| ----------------------------------- | -------------------------------- |
| visualization/performance_charts.py | Chart generation for performance |
| visualization/__init__.py           | Package initialization           |

#### config

| Module                         | Description                                    |
| ------------------------------ | ---------------------------------------------- |
| config/config_v3.py            | **Primary user-editable configuration for V3** |
| config/paths.py                | Path definitions for the project               |
| config/ticker_lists.py         | Ticker list definitions                        |
| config/transaction_log_spec.py | Transaction log specifications                 |

#### Root Scripts

| Module                          | Description                                   |
| ------------------------------- | --------------------------------------------- |
| run_v3_backtest_with_metrics.py | Main entry point for V3 backtest with metrics |
| test_ema_v3.py                  | EMA V3 test implementation                    |
| **DEPRECATED** run_backtest.py  | Legacy backtest runner (no longer maintained) |
| **DEPRECATED** run_backtest_v2.py| V2 backtest runner (no longer maintained)     |
| **DEPRECATED** run_backtest_v2_with_metrics.py | V2 backtest runner with metrics (no longer maintained) |

### Legacy

#### backtest

| Module                      | Description                           |
| --------------------------- | ------------------------------------- |
| backtest/backtest_engine.py | Legacy backtest engine implementation |

#### models

| Module                                          | Description                            |
| ----------------------------------------------- | -------------------------------------- |
| models/allocation_model.py                      | Legacy allocation model implementation |
| models/ema_allocation_model.py                  | Legacy EMA allocation model            |
| models/strategy_registry.py                     | Legacy strategy registry               |
| models/ema_allocation_model_v2_temp.py          | Temporary EMA model implementation     |
| models/ema_allocation_model - BU_040925_0722.py | Backup of EMA allocation model         |

#### engine

| Module               | Description                     |
| -------------------- | ------------------------------- |
| engine/allocation.py | Legacy allocation engine        |
| engine/backtest.py   | Legacy backtest implementation  |
| engine/benchmark.py  | Legacy benchmark implementation |
| engine/execution.py  | Legacy execution engine         |
| engine/orders.py     | Legacy order handling           |
| engine/portfolio.py  | Legacy portfolio management     |
| engine/__init__.py   | Package initialization          |

#### config

| Module                                 | Description                  |
| -------------------------------------- | ---------------------------- |
| config/config.py                       | Legacy configuration         |
| config/local_parameter_optimization.py | Local parameter optimization |
| config/param_adapter.py                | Parameter adapter            |
| config/BU_Config/*.py                  | Backup configuration files   |

#### app/gui

| Module                      | Description                    |
| --------------------------- | ------------------------------ |
| app/gui/config_interface.py | Legacy configuration interface |
| app/gui/main.py             | Legacy main GUI                |
| app/gui/main_v3.py          | Transitional V3 GUI            |

#### optimization

| Module                                | Description                          |
| ------------------------------------- | ------------------------------------ |
| optimization/bayesian_optimization.py | Bayesian optimization implementation |
| optimization/grid_search.py           | Grid search implementation           |
| optimization/random_search.py         | Random search implementation         |
| optimization/__init__.py              | Package initialization               |

#### Root Scripts

| Module                           | Description                      |
| -------------------------------- | -------------------------------- |
| backtest_original.py             | Original backtest implementation |
| **DEPRECATED** run_backtest.py   | Legacy backtest runner           |
| **DEPRECATED** run_backtest_v2.py| V2 backtest runner               |
| **DEPRECATED** run_backtest_v2_with_metrics.py | V2 backtest runner with metrics  |
| **DEPRECATED** run_config_ema_backtest_v2.py    | EMA V2 backtest configuration    |
| **DEPRECATED** run_parameter_optimization_v2.py | V2 parameter optimization        |
| test_backtest.py                 | Backtest test implementation     |
| test_data_loading.py             | Data loading test                |
| debug_data_inspection.py         | Debug data inspection            |
| run_backtest - 633pm.py          | Backup of backtest runner        |
| run_backtest - BU 040925_0719.py | Backup of backtest runner        |
| run_backtest v0407_701.py        | Backup of backtest runner        |
| run_backtest_temp.py             | Temporary backtest runner        |

### Uncertain

#### special_test

| Module                                | Description                                  |
| ------------------------------------- | -------------------------------------------- |
| special_test/test_new_engine.py       | Test for new engine - status uncertain       |
| special_test/test_v2_core.py          | Test for V2 core - status uncertain          |
| special_test/test_v2_engine_simple.py | Simple test for V2 engine - status uncertain |

#### tests

| Module                                | Description                                         |
| ------------------------------------- | --------------------------------------------------- |
| tests/debug_execution_delay.py        | Debug for execution delay - status uncertain        |
| tests/debug_execution_delay_csv.py    | CSV debug for execution delay - status uncertain    |
| tests/debug_execution_delay_simple.py | Simple debug for execution delay - status uncertain |
| tests/find_execution_delay_error.py   | Find execution delay error - status uncertain       |
| tests/fix_execution_delay.py          | Fix for execution delay - status uncertain          |
| tests/simple_verify.py                | Simple verification - status uncertain              |
| tests/simulate_gui_optimization.py    | GUI optimization simulation - status uncertain      |
| tests/trace_trade_none.py             | Trace trade none - status uncertain                 |
| tests/verify_execution_delay_fix.py   | Verify execution delay fix - status uncertain       |

---

## V3 Parameter System Data Flow

```
[v3_gui_core.py (MainWindowV3)]
          |
          v
 [GuiParameterManager]
          |
          v
 [ParameterRegistry] <--> [StrategyParameterSet]
      |         |               ^
      v         v               |
[Numeric/   [Config/            |
Categorical] CategoricalList]   |
      |         |               |
      v         v               |
 [ParameterOptimizer] --------> |
          |                    |
          v                    |
   [Strategy Classes (e.g., EMA)]
```

**Legend:**

- Arrows (→, ↓) indicate data/control flow.
- Double arrows (<-->) indicate two-way registration or lookup.
- Each block is a key module or class in the V3 parameter system.

---

## Reporting Architecture (V3)

```mermaid
flowchart LR
    E[Engine] --> P[v3_performance_report.py]
    E --> A[v3_allocation_report.py]
    P & A --> V[v3_visualization.py]
    V --> O[(Output Files)]
    
    classDef report fill:#E3F2FD,stroke:#2196F3;
    class P,A,V report;
```

### Key Features
- **Modular Components**: Each report type has dedicated module
- **Standardized Outputs**: Consistent Excel/chart formats
- **Traceability**: All reports include version/parameter metadata
- **Validation**: Inputs validated before processing

---

## New Reporting Architecture

The new reporting architecture will be based on the following components:

* `reporting/performance_reporting.py`: This module will be responsible for generating performance reports.
* `reporting/allocation_report.py`: This module will be responsible for generating allocation reports.
* `reporting/__init__.py`: This module will serve as the package initialization for the reporting package.

The new reporting architecture will be integrated with the existing V3 parameter system, and will provide a more robust and flexible reporting framework.

```
[reporting/performance_reporting.py]
          |
          v
[reporting/allocation_report.py]
          |
          v
[v3_engine/performance_reporter_adapter.py]
          |
          v
[v3_engine/parameter_registry.py]
```

**Legend:**

- Arrows (→, ↓) indicate data/control flow.
- Each block is a key module or class in the new reporting architecture.

---

## Reporting Architecture (V3)

```mermaid
flowchart LR
    E[Engine] --> P[v3_performance_report.py]
    E --> A[v3_allocation_report.py]
    P & A --> V[v3_visualization.py]
    V --> O[(Output Files)]
    
    classDef report fill:#E3F2FD,stroke:#2196F3;
    class P,A,V report;
```

### Key Features

- **Modular Components**: Each report type has dedicated module
- **Standardized Outputs**: Consistent Excel/chart formats
- **Traceability**: All reports include version/parameter metadata
- **Validation**: Inputs validated before processing

---

*This file is maintained as the single source of truth for the V3 backtest roadmap. Update as project evolves.*

*This file is maintained as the single source of truth for the V3 backtest roadmap. Update as project evolves.*

---

## New Reporting Architecture

The new reporting architecture will be based on the following components:

* `reporting/performance_reporting.py`: This module will be responsible for generating performance reports.
* `reporting/allocation_report.py`: This module will be responsible for generating allocation reports.
* `reporting/__init__.py`: This module will serve as the package initialization for the reporting package.

The new reporting architecture will be integrated with the existing V3 parameter system, and will provide a more robust and flexible reporting framework.

```
[reporting/performance_reporting.py]
          |
          v
[reporting/allocation_report.py]
          |
          v
[v3_engine/performance_reporter_adapter.py]
          |
          v
[v3_engine/parameter_registry.py]
```

**Legend:**

- Arrows (→, ↓) indicate data/control flow.
- Each block is a key module or class in the new reporting architecture.

---
