#!/usr/bin/env python3
"""
Test script to demonstrate the smart logging system.

This script simulates processing historical data across different date ranges
to show how the smart logging system reduces verbosity for older dates.
"""

import sys
from pathlib import Path
from datetime import date, timedelta
import pandas as pd

# Add project root to path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from v4.utils.smart_logging import create_smart_logger

def main():
    """Demonstrate smart logging with different date scenarios."""
    
    # Create smart logger
    logger = create_smart_logger("smart_logging_test")
    
    print("=" * 80)
    print("SMART LOGGING DEMONSTRATION")
    print("=" * 80)
    
    # Test scenarios with different dates
    current_date = date.today()
    recent_date = current_date - timedelta(days=7)    # Within 2 weeks (detailed)
    old_date = current_date - timedelta(days=30)      # Older than 2 weeks (summary only)
    very_old_date = current_date - timedelta(days=365) # Very old (minimal)
    
    print(f"\nCurrent date: {current_date}")
    print(f"Recent date (7 days ago): {recent_date}")
    print(f"Old date (30 days ago): {old_date}")
    print(f"Very old date (365 days ago): {very_old_date}")
    
    print("\n" + "=" * 40)
    print("TESTING RECENT DATE LOGGING")
    print("=" * 40)
    
    logger.debug(f"Debug message for recent date", target_date=recent_date)
    logger.info(f"Info message for recent date", target_date=recent_date)
    logger.rebalance_event(f"Rebalancing on {recent_date}", target_date=recent_date)
    logger.trade_event(f"Trade executed on {recent_date}", target_date=recent_date)
    
    print("\n" + "=" * 40)
    print("TESTING OLD DATE LOGGING")
    print("=" * 40)
    
    logger.debug(f"Debug message for old date (should NOT appear)", target_date=old_date)
    logger.info(f"Info message for old date (should NOT appear)", target_date=old_date)
    logger.rebalance_event(f"Rebalancing on {old_date}", target_date=old_date)
    logger.trade_event(f"Trade executed on {old_date}", target_date=old_date)
    
    print("\n" + "=" * 40)
    print("TESTING VERY OLD DATE LOGGING")  
    print("=" * 40)
    
    logger.debug(f"Debug message for very old date (should NOT appear)", target_date=very_old_date)
    logger.info(f"Info message for very old date (should NOT appear)", target_date=very_old_date)
    logger.rebalance_event(f"Rebalancing on {very_old_date}", target_date=very_old_date)
    logger.trade_event(f"Trade executed on {very_old_date}", target_date=very_old_date)
    
    print("\n" + "=" * 40)
    print("TESTING FORCED AND MILESTONE LOGGING")
    print("=" * 40)
    
    # These should always appear regardless of date
    logger.info(f"Forced info message for old date", target_date=old_date, force=True)
    logger.milestone(f"Important milestone reached")
    logger.performance_summary(f"Final CAGR: 15.2%")
    logger.error(f"Error message (always shown)", target_date=very_old_date)
    logger.warning(f"Warning message (always shown)", target_date=very_old_date)
    
    print("\n" + "=" * 40)
    print("TESTING DAILY PROGRESS LOGGING")
    print("=" * 40)
    
    # Simulate processing multiple days
    test_dates = [recent_date - timedelta(days=i) for i in range(5)]
    test_dates += [old_date - timedelta(days=i) for i in range(5)]
    test_dates += [very_old_date - timedelta(days=i) for i in range(5)]
    
    total_days = len(test_dates)
    for i, test_date in enumerate(test_dates, 1):
        portfolio_value = 1000000 + (i * 1000)  # Simulate growing portfolio
        logger.daily_progress(i, total_days, test_date, portfolio_value, target_date=test_date)
    
    print("\n" + "=" * 80)
    print("SMART LOGGING DEMONSTRATION COMPLETE")
    print("=" * 80)
    print("\nKey observations:")
    print("- Recent dates (last 14 days): Show ALL logging levels")
    print("- Older dates: Show only important events (trades, rebalances, errors)")
    print("- Special events: Always shown regardless of date")
    print("- Daily progress: Smart frequency based on date recency")

if __name__ == "__main__":
    main()
