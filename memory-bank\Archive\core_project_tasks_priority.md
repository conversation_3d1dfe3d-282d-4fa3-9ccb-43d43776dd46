# Core Project Task Priorities

## Recently Completed ✅

### Trade Filter Implementation (2025-06-29)
- ✅ **COMPLETED**: Trade filter with 2% deviation threshold
- ✅ **COMPLETED**: Integration with backtest engine
- ✅ **COMPLETED**: Verification framework and testing
- ✅ **COMPLETED**: 90% reduction in unnecessary trades
- **Status**: Production ready and working correctly

## High Priority

- Flesh out and setup backtest graph outputs (e.g. cumulative returns, drawdowns, asset weights over time)
- Allocation report improvements are coded but not yet working: generate_rebalance_report now prints clear warnings, output paths, and uses improved naming and tab conventions.
- Integrate detailed EMA debug outputs into performance reports and GUI/batch flows
- Define standards and inputs/outputs and documentation for:
  - new Algos
  - new Benchmarks
  - alternative Tickers lists
- Create rebalance period signal/asset allocation list report:
  - XLSX file with:
    - Tab 1: keyed to date of signal (rows) and allocation signal by ticker (columns)
    - Tab 2: allocation % held by ticker by closing date (columns are tickers)
- Ensure performance tab (row 1) of all XLSX outputs writes variables and default parameter settings:
  - Implement as a well-documented function
  - Enable by default for all project XLSX output files
- Produce weekly high-quality image file:
  - Frequency controlled by config rebalance frequency
  - File name includes date, time, and strategy

## Medium Priority

- Implement categorical grouping for signal algorithms to support strategy-set optimization
- Add support for additional allocation strategies (minimum variance, risk parity, hierarchical risk parity)
- Enhance CLI options for backtest runs (strategy selection, EMA debug flag, output path specification)

## Low Priority

- Develop automated unit and integration tests for core modules (engine, allocation, execution, portfolio)
- Create interactive dashboards for performance visualization (e.g. Plotly/Dash or Streamlit)
- Research and integrate advanced optimization techniques (Bayesian, genetic algorithms)
- Set up CI/CD pipeline with code coverage and continuous testing

_Add or reprioritize tasks as project needs evolve._
