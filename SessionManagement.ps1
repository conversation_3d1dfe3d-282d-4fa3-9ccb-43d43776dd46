# SessionManagement.ps1
# PowerShell functions for AI-driven session management
# Add these to your PowerShell profile ($PROFILE) for persistence

# SESSION START - Type: start-session
function start-session {
    Write-Host "Please read these files in order to understand the current context:
    1. memory-bank/current_session_context.md
    2. memory-bank/execution_reference.md
    3. memory-bank/codebase_map.md
    
    Then provide a summary of current priorities and blockers."
}

# MID-SESSION UPDATE - Type: mid-update  
function mid-update {
    Write-Host "Update memory-bank/current_session_context.md with current progress:
    - <PERSON> completed tasks with ✅
    - Add any new issues discovered
    - Update current priorities if they changed
    - Note any new learnings in the LEARNED THIS SESSION section"
}

# PLAN REVIEW - Type: review-plan
function review-plan {
    Write-Host "Open and review the current plan file located at:
    - PLAN_TRACKER.md (main project plan)
    - memory-bank/current_session_context.md (current session status)
    
    Compare completed work against plan steps and update status.
    Mark any completed steps as ✅ COMPLETED.
    Identify the next open task that should be worked on."
}

# RESUME WORK - Type: resume-work
function resume-work {
    Write-Host "Review memory-bank/current_session_context.md and PLAN_TRACKER.md:
    1. Identify the current or next open task from the plan
    2. Update task status to show it's now IN PROGRESS
    3. Begin working on that specific task
    4. Provide a brief recap of what task you're starting and why"
}

# END SESSION - Type: end-session
function end-session {
    Write-Host "Perform these documentation actions:
    1. Open memory-bank/current_session_context.md
    2. Move all ✅ completed tasks to a new file: memory-bank/session_updates/$(Get-Date -Format 'yyyy-MM-dd')_completed.md
    3. Update current_session_context.md with today's progress
    4. Set goals and priorities for next session
    5. Save and close all files"
}

# QUICK STATUS - Type: quick-status
function quick-status {
    Write-Host "Provide a quick status update by reading:
    - memory-bank/current_session_context.md
    - PLAN_TRACKER.md
    
    Report: What's completed today, what's in progress, what's next"
}

Write-Host "Session Management functions loaded. Available commands:"
Write-Host "- start-session"
Write-Host "- mid-update" 
Write-Host "- review-plan"
Write-Host "- resume-work"
Write-Host "- end-session"
Write-Host "- quick-status"
