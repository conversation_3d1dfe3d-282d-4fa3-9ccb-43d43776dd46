#!/usr/bin/env python3
# tests/v4/run_equivalence_test.py

"""
Simple script to run the equivalence test between unified and decoupled pipelines.

Usage:
    python run_equivalence_test.py

This script can be used for manual testing or CI/CD integration.
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(Path(__file__).parent.parent.parent / 'v4_trace_outputs' / 'equivalence_test.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Run the equivalence test."""
    logger.info("=" * 70)
    logger.info("CPS V4 Pipeline Equivalence Test Runner")
    logger.info("=" * 70)
    logger.info(f"Started at: {datetime.now()}")
    
    try:
        # Import and run the equivalence test
        from test_equivalence_assertions import test_unified_vs_decoupled_equivalence
        
        # Create a mock cleanup fixture for standalone execution
        class MockCleanupFixture:
            def __enter__(self):
                logger.info("Test cleanup fixture initialized")
                return self
            def __exit__(self, exc_type, exc_val, exc_tb):
                logger.info("Test cleanup fixture finished")
        
        # Execute the test
        with MockCleanupFixture() as cleanup_fixture:
            test_unified_vs_decoupled_equivalence(cleanup_fixture)
        
        logger.info("=" * 70)
        logger.info("✅ EQUIVALENCE TEST COMPLETED SUCCESSFULLY")
        logger.info("=" * 70)
        return 0
        
    except ImportError as e:
        logger.error(f"❌ Import Error: {e}")
        logger.error("Make sure all required modules are available")
        return 1
        
    except AssertionError as e:
        logger.error(f"❌ EQUIVALENCE TEST FAILED: {e}")
        logger.error("The unified and decoupled pipelines produced different results!")
        return 1
        
    except Exception as e:
        logger.error(f"❌ UNEXPECTED ERROR: {e}")
        logger.exception("Full traceback:")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
