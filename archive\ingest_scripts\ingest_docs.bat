@echo off
REM Documentation ingestion tool that uses the Custom Function Library
echo ==========================================
echo DOCUMENTATION INGEST TOOL
echo ==========================================

REM Set Python executable path - using isolated virtual environment
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe

REM Verify Python exists
if not exist "%PYTHON_EXE%" (
    echo [X] ERROR: Python not found at %PYTHON_EXE%
    pause
    exit /b 1
)

echo [i] Using Python: %PYTHON_EXE%

REM Run the ingest script using the library version
"%PYTHON_EXE%" "%~dp0ingest_docs_runner.py"

REM Check exit code and log success
if %ERRORLEVEL% EQU 0 (
    echo [✓] Documentation ingested successfully into AI memory.
) else (
    echo [X] ERROR: Ingestion failed. Check logs.
)

pause
