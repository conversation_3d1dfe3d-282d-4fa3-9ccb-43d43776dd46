===== V4 DECOUPLED PIPELINE =====
2025-07-22 12:49:10,036 - INFO - [MILESTONE] Starting V4 Decoupled Pipeline
2025-07-22 12:49:10,037 - INFO - [MILESTONE] Starting Signal Generation Phase
2025-07-22 12:49:10,687 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\AI_Library\my_quant_env\Lib\site-packages\win32
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[MILESTONE] 12:49:10 - --- Starting Signal Generation Phase ---
[MILESTONE] 12:49:10 - 
Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[INFO] 12:49:10 - Settings loaded successfully.
[MILESTONE] 12:49:10 - 
Step 2: Loading market data...
2025-07-22 12:49:10,700 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 12:49:11,503 - INFO - Fetching data for SPY (attempt 1)
Exception ignored from cffi callback <function buffer_callback at 0x000001BC8BE35800>:
Traceback (most recent call last):
  File "F:\AI_Library\my_quant_env\Lib\site-packages\curl_cffi\curl.py", line 67, in buffer_callback
    @ffi.def_extern()
KeyboardInterrupt: 
2025-07-22 12:49:12,021 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:49:12,022 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 1/4): Empty data returned for ticker SPY
2025-07-22 12:49:12,022 - INFO - Retrying SPY data fetch in 2.99 seconds (attempt 2/4)
2025-07-22 12:49:15,010 - INFO - Fetching data for SPY (attempt 2)
2025-07-22 12:49:15,394 - INFO - Successfully fetched 8175 rows for SPY
2025-07-22 12:49:15,399 - INFO - Fetching data for QQQ (attempt 1)
2025-07-22 12:49:15,642 - INFO - Successfully fetched 6633 rows for QQQ
2025-07-22 12:49:15,646 - INFO - Fetching data for IWM (attempt 1)
2025-07-22 12:49:15,873 - INFO - Successfully fetched 6325 rows for IWM
2025-07-22 12:49:15,878 - INFO - Fetching data for GLD (attempt 1)
2025-07-22 12:49:16,031 - INFO - Successfully fetched 5200 rows for GLD
2025-07-22 12:49:16,034 - INFO - Fetching data for TLT (attempt 1)
2025-07-22 12:49:16,274 - INFO - Successfully fetched 5782 rows for TLT
2025-07-22 12:49:16,461 - INFO - [TickerData] Saved data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
[INFO] 12:49:16 - Price data loaded for 5 assets.
[MILESTONE] 12:49:16 - 
Step 3: Generating signals...
[INFO] 12:49:16 -    - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'execution_delay': 1, 'ema_short_period': {'optimize': False, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': False, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': False, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': False, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': False, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
2025-07-22 12:49:16,464 - INFO - Running EMA model with tracing enabled
2025-07-22 12:49:16,464 - CRITICAL - Parameter 'system_top_n' not found in '[ema_model]' or '[strategy]' sections of the settings file.
[ERROR] 12:49:16 - Signal generation failed. Error: "Parameter 'system_top_n' not found in '[ema_model]' or '[strategy]' sections of the settings file."
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\Algo_signal_phase.py", line 82, in run_signal_phase
    signals_df = run_ema_model_with_tracing(
        price_data=price_data,
        **strategy_params
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_signal_bridge.py", line 33, in run_ema_model_with_tracing
    trace_results = ema_allocation_model_updated(
        price_data=price_data,
        trace_mode=True,
        **params
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 371, in ema_allocation_model_updated
    raw_system_top_n = _get_param('system_top_n')
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 95, in _get_param
    raise KeyError(error_msg)
KeyError: "Parameter 'system_top_n' not found in '[ema_model]' or '[strategy]' sections of the settings file."
V4 decoupled pipeline completed with exit code 
