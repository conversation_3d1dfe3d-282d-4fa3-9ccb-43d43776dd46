# Task ID: 14
# Title: Fix Excessive Logging in Portfolio and Execution Modules
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Reduce verbosity by changing log levels for trade/portfolio messages from INFO to DEBUG
# Details:
This task addresses the excessive logging issue identified in memory 3ee8ef69:
1. Update all logging calls in portfolio.py to use DEBUG level for trade-related messages
2. Change log level from INFO to DEBUG for all trade-related messages in execution.py
3. Preserve BACKTEST_LOG_LEVEL environment variable for runtime control (per memory 7623fc1f)
4. Specifically target log messages like "[date] Updated portfolio from trade" and "New cash balance"
5. Implement proper logging configuration that can be applied to all modules
6. Maintain backward compatibility with existing logging mechanisms
7. Test logging output at different log levels to ensure proper verbosity control
8. Follow proper module size limitations (max 450 lines per file)
9. Document logging changes and control mechanisms

# Test Strategy:

