"""
Documentation Ingestion Runner Script

This script uses the documentation ingestion library from the Custom Function Library
to process and combine markdown documentation files.
"""

import sys
import os
import configparser

# Add the Custom Function Library to path
CUSTOM_LIB_PATH = "S:/Dropbox/Scott Only Internal/Quant_Python_24/Custom Function Library"
if CUSTOM_LIB_PATH not in sys.path:
    sys.path.append(CUSTOM_LIB_PATH)

# Import from the library
from documentation.ingest_docs import run_ingestion

def get_project_root():
    """Get the project root from config.ini or use the parent directory of this script"""
    config = configparser.ConfigParser()
    config_path = os.path.join(os.path.dirname(__file__), 'config.ini')
    
    if os.path.exists(config_path):
        config.read(config_path)
        if 'PATHS' in config and 'project_root' in config['PATHS']:
            return config['PATHS']['project_root']
    
    # Default to parent directory of the script
    return os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))

if __name__ == "__main__":
    # Get the project root
    project_root = get_project_root()
    print(f"Using project root: {project_root}")
    
    # Run the ingestion process
    combined_file = run_ingestion(project_root)
    
    if combined_file:
        print(f"Documentation ingested successfully: {combined_file}")
        sys.exit(0)
    else:
        print("Documentation ingestion failed.")
        sys.exit(1)
