@echo off
echo Running EMA Parameter Optimization with v2 Engine...
echo.

:: Activate the Python environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

:: Set Python path to include the Custom Function Library
set PYTHONPATH=%PYTHONPATH%;S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library

:: Run the parameter optimization script
python run_parameter_optimization_v2.py

:: Pause to see the output
pause
