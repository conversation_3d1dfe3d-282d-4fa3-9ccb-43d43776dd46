# Build-AIInstruction.ps1 v2
# Generate AI instruction block from current priorities and blockers

function Build-AIInstruction {
    Set-Location "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"
    
    Write-Host "🔧 Generating AI Instruction Block..." -ForegroundColor Cyan
    
    # Check if required files exist
    if (!(Test-Path "memory-bank/current_session_context.md")) {
        Write-Host "❌ Error: memory-bank/current_session_context.md not found" -ForegroundColor Red
        return
    }
    
    # Read context file
    $contextContent = Get-Content "memory-bank/current_session_context.md" -Raw
    
    # Extract priorities (simple pattern matching)
    $priorities = @()
    $lines = $contextContent -split "`n"
    $inPrioritySection = $false
    
    foreach ($line in $lines) {
        if ($line -match "CURRENT PRIORITIES") { $inPrioritySection = $true; continue }
        if ($line -match "^##" -and $inPrioritySection) { $inPrioritySection = $false; break }
        
        if ($inPrioritySection -and $line -match '^\d+\. \*\*(.+?)\*\*(.*)') {
            $priorities += @{
                Title = $matches[1].Trim()
                Context = $matches[2].Trim()
            }
        }
    }
    
    # Extract blockers
    $blockers = @()
    $inBlockerSection = $false
    
    foreach ($line in $lines) {
        if ($line -match "BLOCKERS") { $inBlockerSection = $true; continue }
        if ($line -match "^##" -and $inBlockerSection) { $inBlockerSection = $false; break }
        
        if ($inBlockerSection -and $line -match '^- (.+)' -and $line -notmatch 'None currently') {
            $blockers += $matches[1].Trim()
        }
    }
    
    # Build the instruction block
    $instruction = ""
    $instruction += "# 🎯 AI INSTRUCTION BLOCK - $(Get-Date -Format 'yyyy-MM-dd HH:mm')`n`n"
    $instruction += "## TOP PRIORITIES`n`n"
    
    # Add top 3 priorities
    $topPriorities = $priorities | Select-Object -First 3
    $priorityNum = 1
    
    foreach ($priority in $topPriorities) {
        $instruction += "### Priority ${priorityNum}: $($priority.Title)`n"
        $instruction += "**Context:** $($priority.Context)`n`n"
        
        # Add relevant file references
        $instruction += "**Key Files:**`n"
        $title = $priority.Title.ToLower()
        
        if ($title -match "test|validate") {
            $instruction += "- \`v4/tests/test_backtest_v4.py\``n"
            $instruction += "- \`v4/tests/enhanced_engine_test.py\``n"
            $instruction += "- \`memory-bank/codebase_map.md\``n"
        }
        elseif ($title -match "powershell|profile") {
            $instruction += "- \`Global_Configs/Microsoft.PowerShell_profile.ps1\``n"
            $instruction += "- \`memory-bank/current_session_context.md\``n"
        }
        elseif ($title -match "signal|trading|pipeline") {
            $instruction += "- \`main_v4_production_run.py\``n"
            $instruction += "- \`v4/run_signal_phase.py\``n"
            $instruction += "- \`v4/run_trading_phase.py\``n"
            $instruction += "- \`v4/settings/settings_CPS_v4.py\``n"
        }
        else {
            $instruction += "- \`memory-bank/codebase_map.md\``n"
            $instruction += "- \`v4/settings/settings_CPS_v4.py\``n"
        }
        
        # Add execution commands
        $instruction += "`n**Suggested Commands:**`n"
        
        if ($title -match "test|validate") {
            $instruction += "\`\`\`batch`n"
            $instruction += "# Test full pipeline`n"
            $instruction += "run_main_v4_prod2.bat`n`n"
            $instruction += "# Test signal generation only`n"
            $instruction += "run_main_v4_signalalgo.bat`n`n"
            $instruction += "# Verify outputs`n"
            $instruction += "dir v4_trace_outputs\*.csv`n"
            $instruction += "\`\`\``n"
        }
        elseif ($title -match "signal") {
            $instruction += "\`\`\`batch`n"
            $instruction += "# Generate signals only`n"
            $instruction += "run_main_v4_signalalgo.bat`n`n"
            $instruction += "# Verify signal output`n"
            $instruction += "type v4_trace_outputs\signals_output_*.csv`n"
            $instruction += "\`\`\``n"
        }
        else {
            $instruction += "\`\`\`batch`n"
            $instruction += "# Run full production pipeline`n"
            $instruction += "run_main_v4_prod2.bat`n`n"
            $instruction += "# Check system status`n"
            $instruction += "dir v4_trace_outputs\*.csv`n"
            $instruction += "\`\`\``n"
        }
        
        $instruction += "`n---`n"
        $priorityNum++
    }
    
    # Add blockers if any
    if ($blockers.Count -gt 0) {
        $instruction += "`n## 🚫 CURRENT BLOCKERS`n`n"
        foreach ($blocker in $blockers) {
            $instruction += "- $blocker`n"
        }
        $instruction += "`n"
    }
    
    # Add footer
    $instruction += "`n## 📝 NOTES`n"
    $instruction += "- Focus on completing priorities in order`n"
    $instruction += "- Validate each step before proceeding`n"
    $instruction += "- Update session context as tasks complete`n"
    $instruction += "- Check execution reference for exact commands`n`n"
    $instruction += "---`n"
    $instruction += "*Generated by Build-AIInstruction at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')*`n"
    
    # Output to console
    Write-Host $instruction -ForegroundColor White
    
    # Save to file
    $outputPath = "memory-bank/next_ai_instructions.md"
    $instruction | Out-File -FilePath $outputPath -Encoding UTF8
    Write-Host "`n✅ AI Instruction Block saved to: $outputPath" -ForegroundColor Green
    
    return $instruction
}
