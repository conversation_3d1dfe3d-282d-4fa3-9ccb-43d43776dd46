# Trade Filter Component Design

## Overview
This document outlines the design for a `trade-filter` component that integrates into the existing CPS v4 rebalancing pipeline. The component provides filtering logic to determine which trades should be executed based on target vs. current allocations and configurable thresholds.

## Current Pipeline Analysis
The existing rebalancing pipeline operates as follows:

1. **Signal Generation Phase** (`run_signal_phase.py`) - Generates allocation signals
2. **Trading Phase** (`run_trading_phase.py`) - Executes trades based on signals
3. **Order Generation** (`allocation_v4.py`) - Current logic in `calculate_rebalance_orders()`

The current system directly generates orders without threshold-based filtering, making every rebalance signal result in immediate trade generation.

## Input Data Requirements

The trade-filter component requires these inputs:

### Primary Inputs
- **Target Allocations** (`dict`): Desired allocation weights per asset {symbol: weight}
- **Current MTM Allocations** (`dict`): Current portfolio allocation weights {symbol: weight}  
- **Portfolio Value** (`float`): Total portfolio value for dollar change calculations
- **Current Prices** (`dict`): Current asset prices {symbol: price}

### Configuration from Settings
All parameters sourced from `v4/settings/settings_parameters_v4.ini` via `v4/settings/settings_CPS_v4.py`:
- **Deviation Threshold** (`float`): `backtest.deviation_threshold` - Minimum percentage point deviation required to trigger rebalancing (default: 0.02)
- **Business Calendar Check**: Automatic via price data availability - no separate configuration needed

## Function Signatures

### Core Filter Function
```python
def filter_rebalance_trades(
    target_allocations: dict, 
    current_allocations: dict,
    portfolio_value: float,
    prices: dict,
    rebalance_date: date
) -> dict:
    """
    Determines if rebalancing should occur and returns filtered dollar changes.
    
    Args:
        target_allocations: Target allocation weights {symbol: weight}
        current_allocations: Current allocation weights {symbol: weight}
        portfolio_value: Total portfolio value
        prices: Current prices {symbol: price}
        rebalance_date: Date for rebalancing decision
        
    Returns:
        dict: Filtered dollar changes {symbol: dollar_amount} or empty dict if no rebalancing
    """
```

### Business Calendar Validation
```python
def validate_business_calendar(rebalance_date: date, prices: dict) -> bool:
    """
    Validates if trading should occur based on price data availability.
    
    Args:
        rebalance_date: Proposed rebalancing date
        prices: Available price data {symbol: price}
        
    Returns:
        bool: True if trading should proceed, False to skip
    """
```

### Integration Wrapper
```python
def calculate_filtered_rebalance_orders(
    portfolio, 
    target_allocations: dict, 
    prices: dict, 
    order_date: date = None
) -> list:
    """
    Drop-in replacement for existing calculate_rebalance_orders() with filtering.
    
    Args:
        portfolio: Portfolio object
        target_allocations: Target allocations {symbol: weight}
        prices: Current prices {symbol: price}
        order_date: Date for the orders
        
    Returns:
        list: Order objects (empty list if no rebalancing needed)
    """
```

## Business Calendar Validation Strategy

### Price Data Availability Check
- **Primary Check**: Verify all target assets have valid price data
- **Fallback Strategy**: Use last available price if within configurable tolerance (e.g., 3 days)
- **Skip Condition**: If critical assets lack recent pricing, skip rebalancing for the day

### Implementation Approach
```python
# Inject validation at the beginning of trade filtering
if not validate_business_calendar(rebalance_date, prices):
    logger.info(f"Skipping rebalancing on {rebalance_date} - insufficient price data")
    return {}  # Return empty changes = no trades
```

## Module Placement and Structure

### File Location
`v4/trading/trade_filter.py` (new directory structure)

### Module Organization
```
v4/trading/
├── __init__.py
└── trade_filter.py          # <450 lines
    ├── filter_rebalance_trades()
    ├── validate_business_calendar() 
    ├── calculate_filtered_rebalance_orders()
    └── _calculate_allocation_deviations()  # Private helper
```

## Integration with Existing Pipeline

### Current Integration Point
The filter integrates at the order generation stage in `allocation_v4.py`:

**Before (current)**:
```python
# In backtest_v4.py
orders = calculate_rebalance_orders(portfolio, target_allocations, prices, current_date)
```

**After (with filter)**:
```python
# In backtest_v4.py  
from v4.trading.trade_filter import calculate_filtered_rebalance_orders
orders = calculate_filtered_rebalance_orders(portfolio, target_allocations, prices, current_date)
```

### Backward Compatibility
- The new function maintains the same interface as `calculate_rebalance_orders()`
- Existing code continues to work unchanged
- Filter behavior controlled by settings (can be disabled for testing)

### Settings Integration
New parameters in `settings_parameters_v4.ini`:

```ini
[Trading]
; Rebalancing threshold parameters
rebalance_threshold = 0.05          ; 5% deviation triggers rebalancing
min_trade_size = 1000               ; Minimum $1000 trade size
business_calendar_check = True      ; Enable price data validation
price_staleness_days = 3            ; Max days for stale price tolerance
```

## Testing Strategy

### Unit Tests
- Test threshold logic with various deviation scenarios
- Test business calendar validation with missing/stale data
- Test integration wrapper maintains existing behavior when filter disabled

### Integration Tests
- Run full pipeline with filter enabled/disabled
- Verify no trades generated when under threshold
- Verify normal operation when above threshold
- Test with missing price data scenarios

## Implementation Priority

1. **Phase 1**: Core filtering logic without business calendar
2. **Phase 2**: Business calendar validation
3. **Phase 3**: Settings integration and testing
4. **Phase 4**: Performance optimization and edge case handling

This design ensures the trade-filter component integrates seamlessly into the existing pipeline while providing the flexibility to control rebalancing behavior through configuration.

