import importlib
import configparser
import os

# Import raw configuration from config_v2.py
config_mod = importlib.import_module('config.config_v2')
# Extract parameter sections
_data_params = getattr(config_mod, 'data_params', {})
_backtest_params = getattr(config_mod, 'backtest_params', {})
_strategy_params = getattr(config_mod, 'strategy_params', {})


def _parse_param(v):
    """
    Parse a parameter definition.
    Returns (optimize: bool, default, min, max, step).
    """
    if isinstance(v, tuple) and len(v) == 5 and v[0] in ['Y', 'N']:
        return (v[0] == 'Y', v[1], v[2], v[3], v[4])
    return (False, v, None, None, None)


def load_params():
    """
    Return a flat dict of all parameters:
    { name: { 'optimize', 'default', 'min', 'max', 'step' } }
    """
    params = {}
    for section in (_data_params, _backtest_params, _strategy_params):
        for name, val in section.items():
            opt, default, mn, mx, step = _parse_param(val)
            params[name] = {
                'optimize': opt,
                'default': default,
                'min': mn,
                'max': mx,
                'step': step
            }
    return params


def save_to_ini(path, values):
    """
    Save a dict of parameter values to an INI file at `path`.
    """
    cp = configparser.ConfigParser()
    cp['parameters'] = {k: str(v) for k, v in values.items()}
    os.makedirs(os.path.dirname(path), exist_ok=True)
    with open(path, 'w') as f:
        cp.write(f)
