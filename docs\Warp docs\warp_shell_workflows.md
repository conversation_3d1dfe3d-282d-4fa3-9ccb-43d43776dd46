# Warp Shell Workflows - Working Solutions

## Problem
Warp workflows expect shell commands, not AI instructions.

## Solution: Echo Commands + Manual AI Trigger

### START SESSION Workflow
**Warp Workflow Command:**
```powershell
echo "=== SESSION START ===" && echo "Files to read:" && echo "1. memory-bank/current_session_context.md" && echo "2. memory-bank/execution_reference.md" && echo "3. memory-bank/codebase_map.md" && echo "" && echo "AI: Please read these files and summarize current priorities, blockers, and session goals"
```

### END SESSION Workflow  
**Warp Workflow Command:**
```powershell
echo "=== SESSION END ===" && echo "Actions needed:" && echo "1. Create session_updates/$(Get-Date -Format 'yyyy-MM-dd')_completed.md" && echo "2. Update current_session_context.md" && echo "3. Update codebase_map.md if needed" && echo "4. Set next session goals" && echo "" && echo "AI: Please perform these end session actions"
```

### MID UPDATE Workflow
**Warp Workflow Command:**
```powershell
echo "=== MID SESSION UPDATE ===" && echo "AI: Please update memory-bank/current_session_context.md with completed tasks, new blockers, and discoveries"
```

## Alternative: Use Warp AI Chat Directly

**Better approach - Just type these in Warp AI chat:**

### Session Start Message:
```
START SESSION - Read memory-bank/current_session_context.md, execution_reference.md, and codebase_map.md. Summarize current priorities, blockers, and session goals.
```

### Session End Message:
```
END SESSION - Create today's completed.md, update current_session_context.md, update codebase_map.md if needed, set next session goals.
```

## Recommended Implementation

1. **Don't use Warp workflows** for AI instructions
2. **Save the messages above** as text snippets in Warp
3. **Copy/paste into AI chat** when needed
4. **Use workflows only** for actual shell commands (like checking file status)

## Working Shell Commands for Workflows

### Check Project Status
```powershell
echo "=== PROJECT STATUS ===" && dir memory-bank\*.md && echo "" && dir v4_trace_outputs\*.csv
```

### List Recent Outputs
```powershell
echo "=== RECENT OUTPUTS ===" && Get-ChildItem v4_trace_outputs\*.csv | Sort-Object LastWriteTime -Descending | Select-Object -First 5
```

### Quick File Check
```powershell
echo "=== MEMORY BANK STATUS ===" && Get-Content memory-bank\current_session_context.md | Select-Object -First 10
```
