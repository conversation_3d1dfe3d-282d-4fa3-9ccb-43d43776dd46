@echo off
REM Run EMA backtest with the improved v2 engine
REM Activate Python environment and run the backtest script

echo Starting EMA backtest with v2 engine...
echo.

REM Activate Python environment
call F:\AI_Library\my_quant_env\Scripts\activate.bat

REM Run the backtest script
python run_backtest_v2.py --strategy ema --rebalance weekly --delay 1 --st-lookback 10 --mt-lookback 50 --lt-lookback 150

REM Pause to see results
pause
