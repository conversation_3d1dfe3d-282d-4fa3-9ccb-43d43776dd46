#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
tests/v4/test_milestone_and_retry.py

Test script to verify milestone logging functionality.

This test validates the functionality of the milestone logging helper
utilities for the CPS v4 system.

Author: AI Assistant
Date: 2025-01-18
"""

import sys
import os
import logging
import time
from pathlib import Path
from typing import Dict, Any

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import the utilities we're testing
from v4.utils.log_milestone import (
    log_milestone, log_phase_start, log_phase_complete, 
    log_error_milestone, create_milestone_logger, log_data_milestone
)

def test_milestone_logging():
    """Test milestone logging functionality."""
    print("\n" + "="*60)
    print("TESTING MILESTONE LOGGING")
    print("="*60)
    
    # Create a test logger
    logger = create_milestone_logger("test_milestone", log_level="INFO")
    
    # Test basic milestone logging
    log_milestone(logger, "Testing basic milestone logging")
    
    # Test phase logging
    log_phase_start(logger, "Test Phase")
    log_milestone(logger, "Doing some work in the test phase")
    log_phase_complete(logger, "Test Phase")
    
    # Test data milestone logging
    log_data_milestone(
        logger, 
        "Sample data loaded",
        data_shape=(1000, 5),
        date_range=("2020-01-01", "2025-01-01")
    )
    
    # Test error milestone logging
    log_error_milestone(logger, "This is a test error - not a real error!")
    
    print("✓ Milestone logging tests completed successfully")


def run_all_tests():
    """Run all milestone logging tests."""
    print("Starting milestone logging tests...")
    
    try:
        test_milestone_logging()
        
        print("\n" + "="*60)
        print("ALL TESTS PASSED SUCCESSFULLY!")
        print("✓ Milestone logging helper is working correctly")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
