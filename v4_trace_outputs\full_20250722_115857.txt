===== V4 DECOUPLED PIPELINE =====
2025-07-22 11:58:58,037 - INFO - [MILESTONE] Starting V4 Decoupled Pipeline
2025-07-22 11:58:58,037 - INFO - [MILESTONE] Starting Signal Generation Phase
2025-07-22 11:59:02,076 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
  F:\AI_Library\my_quant_env\Lib\site-packages\win32
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[MILESTONE] 11:59:02 - --- Starting Signal Generation Phase ---
[MILESTONE] 11:59:02 - 
Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[INFO] 11:59:02 - Settings loaded successfully.
[MILESTONE] 11:59:02 - 
Step 2: Loading market data...
2025-07-22 11:59:02,090 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 11:59:04,773 - INFO - Fetching data for SPY (attempt 1)
2025-07-22 11:59:12,552 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 11:59:12,552 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 1/4): Empty data returned for ticker SPY
2025-07-22 11:59:12,552 - INFO - Retrying SPY data fetch in 2.06 seconds (attempt 2/4)
2025-07-22 11:59:14,615 - INFO - Fetching data for SPY (attempt 2)
2025-07-22 11:59:18,825 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 11:59:18,826 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 2/4): Empty data returned for ticker SPY
2025-07-22 11:59:18,826 - INFO - Retrying SPY data fetch in 4.13 seconds (attempt 3/4)
2025-07-22 11:59:22,961 - INFO - Fetching data for SPY (attempt 3)
2025-07-22 11:59:25,178 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 11:59:25,179 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 3/4): Empty data returned for ticker SPY
2025-07-22 11:59:25,179 - INFO - Retrying SPY data fetch in 8.01 seconds (attempt 4/4)
2025-07-22 11:59:33,185 - INFO - Fetching data for SPY (attempt 4)
2025-07-22 11:59:40,337 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 11:59:40,337 - ERROR - Final attempt failed for SPY after 4 attempts: Empty data returned for ticker SPY
2025-07-22 11:59:40,338 - ERROR - Failed to fetch stock data for SPY after 4 attempts. Last error: Empty data returned for ticker SPY
2025-07-22 11:59:40,338 - ERROR - Error retrieving data for SPY: Failed to fetch stock data for SPY after 4 attempts. Last error: Empty data returned for ticker SPY
2025-07-22 11:59:40,338 - INFO - Fetching data for QQQ (attempt 1)
2025-07-22 11:59:40,819 - ERROR - $QQQ: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 11:59:40,820 - WARNING - Non-retryable error fetching QQQ, but will retry anyway (attempt 1/4): Empty data returned for ticker QQQ
2025-07-22 11:59:40,820 - INFO - Retrying QQQ data fetch in 2.60 seconds (attempt 2/4)
2025-07-22 11:59:43,424 - INFO - Fetching data for QQQ (attempt 2)
2025-07-22 11:59:48,407 - ERROR - $QQQ: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 11:59:48,408 - WARNING - Non-retryable error fetching QQQ, but will retry anyway (attempt 2/4): Empty data returned for ticker QQQ
2025-07-22 11:59:48,408 - INFO - Retrying QQQ data fetch in 4.26 seconds (attempt 3/4)
2025-07-22 11:59:52,671 - INFO - Fetching data for QQQ (attempt 3)
2025-07-22 11:59:53,457 - INFO - Successfully fetched 6632 rows for QQQ
2025-07-22 11:59:53,461 - INFO - Fetching data for IWM (attempt 1)
2025-07-22 11:59:54,151 - INFO - Successfully fetched 6325 rows for IWM
2025-07-22 11:59:54,157 - INFO - Fetching data for GLD (attempt 1)
2025-07-22 11:59:57,340 - INFO - Successfully fetched 5200 rows for GLD
2025-07-22 11:59:57,344 - INFO - Fetching data for TLT (attempt 1)
2025-07-22 12:00:00,670 - ERROR - $TLT: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:00:00,670 - WARNING - Non-retryable error fetching TLT, but will retry anyway (attempt 1/4): Empty data returned for ticker TLT
2025-07-22 12:00:00,670 - INFO - Retrying TLT data fetch in 2.79 seconds (attempt 2/4)
2025-07-22 12:00:03,463 - INFO - Fetching data for TLT (attempt 2)
2025-07-22 12:00:07,939 - INFO - Successfully fetched 5781 rows for TLT
2025-07-22 12:00:08,351 - INFO - [TickerData] Saved data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
[INFO] 12:00:08 - Price data loaded for 4 assets.
[MILESTONE] 12:00:08 - 
Step 3: Generating signals...
[INFO] 12:00:08 -    - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'system_top_n': {'optimize': False, 'default_value': 2, 'min_value': 1, 'max_value': 5, 'increment': 1}, 'execution_delay': 1, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
2025-07-22 12:00:08,354 - INFO - Running EMA model with tracing enabled
2025-07-22 12:00:09,743 - INFO - Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
2025-07-22 12:00:09,768 - INFO - Saved Short-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_short_20250722_120009.csv
2025-07-22 12:00:09,780 - INFO - Saved Medium-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_medium_20250722_120009.csv
2025-07-22 12:00:09,792 - INFO - Saved Long-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_long_20250722_120009.csv
2025-07-22 12:00:09,827 - INFO - Saved Asset Ranking History (Matrix Format) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_20250722_120009.csv
2025-07-22 12:00:09,857 - INFO - Saved Combined EMA Averages History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\02_ema_average_history_20250722_120009.csv
2025-07-22 12:00:09,886 - INFO - Saved Raw Algorithm Calculation History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_history_20250722_120009.csv
2025-07-22 12:00:09,886 - INFO - Successfully saved detailed signal history data to CSV files including individual EMA files
2025-07-22 12:00:09,887 - INFO - Initialized signal_history DataFrame with shape (1394, 4)
2025-07-22 12:00:09,887 - INFO - Signal history date range: 2020-01-02 00:00:00 to 2025-07-21 00:00:00
2025-07-22 12:00:09,887 - INFO - Ranks DataFrame shape: (5576, 4)
2025-07-22 12:00:09,887 - INFO - Ranks date range: 0 to 5575
2025-07-22 12:00:09,887 - INFO - Initial ranks_df info:
2025-07-22 12:00:09,887 - INFO - Index type: <class 'pandas.core.indexes.range.RangeIndex'>
2025-07-22 12:00:09,887 - INFO - Columns: ['Date', 'Asset', 'EMAXAvg_Value', 'Rank_Ordinal']
2025-07-22 12:00:09,899 - INFO - Sample data:
        Date Asset  EMAXAvg_Value  Rank_Ordinal
0 2020-01-02   QQQ            1.0             1
1 2020-01-02   IWM            1.0             2
2025-07-22 12:00:09,908 - INFO - Pivoted ranks DataFrame shape: (1394, 4)
2025-07-22 12:00:09,908 - INFO - Pivoted columns: ['GLD', 'IWM', 'QQQ', 'TLT']
2025-07-22 12:00:09,910 - INFO - 2020-01-02 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:09,937 - INFO - 2020-03-16 00:00:00: Allocated 60% to TLT, 40% to GLD
2025-07-22 12:00:09,964 - INFO - 2020-05-27 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:00:09,992 - INFO - 2020-08-06 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:00:10,021 - INFO - 2020-10-16 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,049 - INFO - 2020-12-29 00:00:00: Allocated 60% to IWM, 40% to QQQ
2025-07-22 12:00:10,078 - INFO - 2021-03-12 00:00:00: Allocated 60% to IWM, 40% to QQQ
2025-07-22 12:00:10,105 - INFO - 2021-05-24 00:00:00: Allocated 60% to IWM, 40% to GLD
2025-07-22 12:00:10,134 - INFO - 2021-08-04 00:00:00: Allocated 60% to QQQ, 40% to TLT
2025-07-22 12:00:10,163 - INFO - 2021-10-14 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,192 - INFO - 2021-12-27 00:00:00: Allocated 60% to QQQ, 40% to TLT
2025-07-22 12:00:10,221 - INFO - 2022-03-09 00:00:00: Allocated 60% to GLD, 40% to TLT
2025-07-22 12:00:10,247 - INFO - 2022-05-19 00:00:00: Allocated 60% to GLD, 40% to IWM
2025-07-22 12:00:10,275 - INFO - 2022-08-02 00:00:00: Allocated 60% to IWM, 40% to QQQ
2025-07-22 12:00:10,304 - INFO - 2022-10-12 00:00:00: Allocated 60% to GLD, 40% to IWM
2025-07-22 12:00:10,333 - INFO - 2022-12-22 00:00:00: Allocated 60% to GLD, 40% to TLT
2025-07-22 12:00:10,361 - INFO - 2023-03-08 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,389 - INFO - 2023-05-18 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:00:10,418 - INFO - 2023-08-01 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,449 - INFO - 2023-10-11 00:00:00: Allocated 60% to QQQ, 40% to GLD
2025-07-22 12:00:10,478 - INFO - 2023-12-21 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,506 - INFO - 2024-03-06 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,534 - INFO - 2024-05-16 00:00:00: Allocated 60% to GLD, 40% to QQQ
2025-07-22 12:00:10,562 - INFO - 2024-07-30 00:00:00: Allocated 60% to IWM, 40% to GLD
2025-07-22 12:00:10,590 - INFO - 2024-10-09 00:00:00: Allocated 60% to GLD, 40% to QQQ
2025-07-22 12:00:10,619 - INFO - 2024-12-19 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,647 - INFO - 2025-03-06 00:00:00: Allocated 60% to GLD, 40% to TLT
2025-07-22 12:00:10,677 - INFO - 2025-05-16 00:00:00: Allocated 60% to GLD, 40% to QQQ
2025-07-22 12:00:10,703 - INFO - 2025-07-21 00:00:00: Allocated 60% to QQQ, 40% to IWM
2025-07-22 12:00:10,703 - INFO - Signal generation complete. Made 1394 allocations.
2025-07-22 12:00:10,703 - INFO - Final signal_history shape: (1394, 4)
2025-07-22 12:00:10,704 - INFO - Non-zero allocations: 2788
INFO: Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
TRACE: Saved Short-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_short_20250722_120009.csv
TRACE: Saved Medium-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_medium_20250722_120009.csv
TRACE: Saved Long-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_long_20250722_120009.csv
TRACE: Saved Asset Ranking History (Matrix Format) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_20250722_120009.csv
TRACE: Saved Combined EMA Averages History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\02_ema_average_history_20250722_120009.csv
TRACE: Saved Raw Algorithm Calculation History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_history_20250722_120009.csv
[INFO] 12:00:10 - Signals saved to timestamped CSV: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250722_120010.csv
[MILESTONE] 12:00:10 - Signals saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250722_120010.csv
[MILESTONE] 12:00:10 - Signal generation complete.
2025-07-22 12:00:10,743 - INFO - Signal phase completed successfully, signals saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250722_120010.csv
2025-07-22 12:00:10,743 - INFO - [MILESTONE] Starting Trading Phase
2025-07-22 12:00:10,768 - INFO - [PHASE_START] 12:00:10 - Starting Trading Phase
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[PHASE_START] 12:00:10 - Starting Trading Phase
2025-07-22 12:00:10,772 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 12:00:10,773 - INFO - Fetching data for SPY (attempt 1)
2025-07-22 12:00:15,924 - ERROR - $SPY: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:00:15,924 - WARNING - Non-retryable error fetching SPY, but will retry anyway (attempt 1/4): Empty data returned for ticker SPY
2025-07-22 12:00:15,924 - INFO - Retrying SPY data fetch in 2.37 seconds (attempt 2/4)
2025-07-22 12:00:18,293 - INFO - Fetching data for SPY (attempt 2)
2025-07-22 12:00:23,718 - INFO - Successfully fetched 8174 rows for SPY
2025-07-22 12:00:23,723 - INFO - Fetching data for QQQ (attempt 1)
2025-07-22 12:00:30,229 - ERROR - $QQQ: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:00:30,230 - WARNING - Non-retryable error fetching QQQ, but will retry anyway (attempt 1/4): Empty data returned for ticker QQQ
2025-07-22 12:00:30,230 - INFO - Retrying QQQ data fetch in 2.43 seconds (attempt 2/4)
2025-07-22 12:00:32,663 - INFO - Fetching data for QQQ (attempt 2)
2025-07-22 12:00:33,143 - ERROR - $QQQ: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:00:33,144 - WARNING - Non-retryable error fetching QQQ, but will retry anyway (attempt 2/4): Empty data returned for ticker QQQ
2025-07-22 12:00:33,144 - INFO - Retrying QQQ data fetch in 4.76 seconds (attempt 3/4)
2025-07-22 12:00:37,909 - INFO - Fetching data for QQQ (attempt 3)
2025-07-22 12:00:47,779 - ERROR - $QQQ: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:00:47,779 - WARNING - Non-retryable error fetching QQQ, but will retry anyway (attempt 3/4): Empty data returned for ticker QQQ
2025-07-22 12:00:47,780 - INFO - Retrying QQQ data fetch in 8.35 seconds (attempt 4/4)
2025-07-22 12:00:56,134 - INFO - Fetching data for QQQ (attempt 4)
2025-07-22 12:01:00,554 - INFO - Successfully fetched 6632 rows for QQQ
2025-07-22 12:01:00,559 - INFO - Fetching data for IWM (attempt 1)
2025-07-22 12:01:00,999 - ERROR - $IWM: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:01:00,999 - WARNING - Non-retryable error fetching IWM, but will retry anyway (attempt 1/4): Empty data returned for ticker IWM
2025-07-22 12:01:00,999 - INFO - Retrying IWM data fetch in 2.61 seconds (attempt 2/4)
2025-07-22 12:01:03,614 - INFO - Fetching data for IWM (attempt 2)
2025-07-22 12:01:04,098 - INFO - Successfully fetched 6324 rows for IWM
2025-07-22 12:01:04,102 - INFO - Fetching data for GLD (attempt 1)
2025-07-22 12:01:08,820 - ERROR - $GLD: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:01:08,820 - WARNING - Non-retryable error fetching GLD, but will retry anyway (attempt 1/4): Empty data returned for ticker GLD
2025-07-22 12:01:08,820 - INFO - Retrying GLD data fetch in 2.34 seconds (attempt 2/4)
2025-07-22 12:01:11,162 - INFO - Fetching data for GLD (attempt 2)
2025-07-22 12:01:11,541 - ERROR - $GLD: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:01:11,542 - WARNING - Non-retryable error fetching GLD, but will retry anyway (attempt 2/4): Empty data returned for ticker GLD
2025-07-22 12:01:11,542 - INFO - Retrying GLD data fetch in 5.00 seconds (attempt 3/4)
2025-07-22 12:01:16,540 - INFO - Fetching data for GLD (attempt 3)
2025-07-22 12:01:22,298 - INFO - Successfully fetched 5199 rows for GLD
2025-07-22 12:01:22,307 - INFO - Fetching data for TLT (attempt 1)
2025-07-22 12:01:27,000 - ERROR - $TLT: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:01:27,000 - WARNING - Non-retryable error fetching TLT, but will retry anyway (attempt 1/4): Empty data returned for ticker TLT
2025-07-22 12:01:27,001 - INFO - Retrying TLT data fetch in 2.97 seconds (attempt 2/4)
2025-07-22 12:01:29,973 - INFO - Fetching data for TLT (attempt 2)
2025-07-22 12:01:37,923 - ERROR - $TLT: possibly delisted; no price data found  (1d 1926-08-16 -> 2025-07-22)
2025-07-22 12:01:37,924 - WARNING - Non-retryable error fetching TLT, but will retry anyway (attempt 2/4): Empty data returned for ticker TLT
2025-07-22 12:01:37,924 - INFO - Retrying TLT data fetch in 4.46 seconds (attempt 3/4)
2025-07-22 12:01:42,389 - INFO - Fetching data for TLT (attempt 3)
2025-07-22 12:01:46,023 - INFO - Successfully fetched 5782 rows for TLT
2025-07-22 12:01:46,442 - INFO - [TickerData] Saved data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_QQQ_IWM_GLD_TLT_2020-01-01_None.xlsx
2025-07-22 12:01:46,447 - INFO - Loaded price data with shape (1394, 5)
2025-07-22 12:01:46,448 - INFO - Date range: 2020-01-02 00:00:00 to 2025-07-21 00:00:00
2025-07-22 12:01:46,448 - INFO - Tickers: SPY, QQQ, IWM, GLD, TLT
2025-07-22 12:01:46,448 - ERROR - Trading phase failed with error: 'str' object has no attribute 'shape'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\run_v4_decoupled_pipeline.py", line 69, in run_decoupled_pipeline
    trading_results = trading_module.run_trading_phase(signals_file)
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\run_trading_phase.py", line 140, in run_trading_phase
    logger.info(f"Using provided signals DataFrame with shape {signals.shape}")
                                                               ^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'shape'
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
V4 decoupled pipeline completed with exit code 
