import pytest
import sys
from pathlib import Path

# Add the v4/settings directory to the path
project_root = Path('.').resolve()
v4_settings_path = str(project_root / 'v4' / 'settings')
sys.path.insert(0, v4_settings_path)
from settings_CPS_v4 import load_settings

@pytest.fixture
def settings():
    # Load settings for testing
    return load_settings()


def test_complexn_parsing(settings):
    complex_param = settings['system']['system_lookback']
    expected_structure = {
        'optimize': True,
        'default_value': 60,
        'min_value': 20,
        'max_value': 120,
        'increment': 5
    }
    assert complex_param == expected_structure, "ComplexN parameter parsing failed."


def test_default_value_propagation(settings):
    system_top_n = settings['system']['system_top_n']['default_value']
    assert system_top_n == 2, "Default value propagation failed."


def test_strategy_complexn_parameters(settings):
    """Test ComplexN parameters in the Strategy section"""
    # Test EMA short period
    ema_short = settings['strategy']['ema_short_period']
    assert isinstance(ema_short, dict), "EMA short period should be a dictionary"
    assert 'default_value' in ema_short, "ComplexN should have default_value"
    assert ema_short['default_value'] == 12, "EMA short period default should be 12"
    assert ema_short['optimize'] == False, "EMA short period optimize should be False"
    
    # Test MT lookback
    mt_lookback = settings['strategy']['mt_lookback']
    assert isinstance(mt_lookback, dict), "MT lookback should be a dictionary"
    assert mt_lookback['default_value'] == 70, "MT lookback default should be 70"
    assert mt_lookback['min_value'] == 30, "MT lookback min should be 30"
    assert mt_lookback['max_value'] == 100, "MT lookback max should be 100"
    assert mt_lookback['increment'] == 5, "MT lookback increment should be 5"


def test_core_complexn_parameters(settings):
    """Test ComplexN parameters in the Core section"""
    # Test max allocation
    max_alloc = settings['core']['max_allocation']
    assert isinstance(max_alloc, dict), "Max allocation should be a dictionary"
    assert max_alloc['default_value'] == 0.25, "Max allocation default should be 0.25"
    assert max_alloc['optimize'] == False, "Max allocation optimize should be False"
    assert max_alloc['min_value'] == 0.1, "Max allocation min should be 0.1"
    assert max_alloc['max_value'] == 0.5, "Max allocation max should be 0.5"
    assert max_alloc['increment'] == 0.05, "Max allocation increment should be 0.05"


def test_all_complexn_have_required_keys(settings):
    """Test that all ComplexN parameters have required keys"""
    required_keys = {'optimize', 'default_value', 'min_value', 'max_value', 'increment'}
    
    # Find all ComplexN parameters across all sections
    for section_name, section_data in settings.items():
        if section_name == 'lists':
            continue
        for param_name, param_value in section_data.items():
            if isinstance(param_value, dict) and 'optimize' in param_value:
                # This is a ComplexN parameter
                actual_keys = set(param_value.keys())
                missing_keys = required_keys - actual_keys
                assert not missing_keys, f"ComplexN parameter {section_name}.{param_name} missing keys: {missing_keys}"


def test_complexn_data_types(settings):
    """Test that ComplexN parameters have correct data types"""
    system_lookback = settings['system']['system_lookback']
    
    assert isinstance(system_lookback['optimize'], bool), "optimize should be boolean"
    assert isinstance(system_lookback['default_value'], (int, float)), "default_value should be numeric"
    assert isinstance(system_lookback['min_value'], (int, float)), "min_value should be numeric"
    assert isinstance(system_lookback['max_value'], (int, float)), "max_value should be numeric"
    assert isinstance(system_lookback['increment'], (int, float)), "increment should be numeric"


def test_complexn_default_value_extraction(settings):
    """Test that default values can be properly extracted from ComplexN parameters for production use"""
    # Test that we can extract default values for production code usage
    strategy_params = settings.get('strategy', {})
    
    # Extract default values for EMA parameters
    ema_short_default = strategy_params['ema_short_period']['default_value']
    ema_long_default = strategy_params['ema_long_period']['default_value']
    st_lookback_default = strategy_params['st_lookback']['default_value']
    mt_lookback_default = strategy_params['mt_lookback']['default_value']
    lt_lookback_default = strategy_params['lt_lookback']['default_value']
    
    # Verify defaults are what we expect
    assert ema_short_default == 12
    assert ema_long_default == 26
    assert st_lookback_default == 15
    assert mt_lookback_default == 70
    assert lt_lookback_default == 100
    
    # Test system defaults
    system_params = settings.get('system', {})
    system_lookback_default = system_params['system_lookback']['default_value']
    system_top_n_default = system_params['system_top_n']['default_value']
    
    assert system_lookback_default == 60
    assert system_top_n_default == 2


def test_complexn_parameters_ready_for_optimization(settings):
    """Test that ComplexN parameters are correctly configured for optimization scenarios"""
    # Find parameters marked for optimization
    optimization_ready = []
    
    for section_name, section_data in settings.items():
        if section_name == 'lists':
            continue
        for param_name, param_value in section_data.items():
            if isinstance(param_value, dict) and param_value.get('optimize') == True:
                optimization_ready.append(f"{section_name}.{param_name}")
    
    # Should have at least one parameter ready for optimization (system_lookback)
    assert len(optimization_ready) > 0, "Should have at least one parameter marked for optimization"
    assert 'system.system_lookback' in optimization_ready, "system_lookback should be marked for optimization"
    
    # Test that optimization parameters have valid ranges
    system_lookback = settings['system']['system_lookback']
    assert system_lookback['min_value'] < system_lookback['default_value'] < system_lookback['max_value'], \
        "Optimization range should be valid: min < default < max"
    assert system_lookback['increment'] > 0, "Optimization increment should be positive"

