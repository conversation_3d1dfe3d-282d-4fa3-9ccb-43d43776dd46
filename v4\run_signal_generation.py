"""
v4/run_signal_generation.py
Standalone script to generate and save the full signal history and data for CPS V4.
"""
import sys
import os
from pathlib import Path
from datetime import datetime
import pandas as pd

# Ensure project root is on path
_script_path = Path(__file__).resolve()
# Project root is parent of the v4 directory
_project_root = _script_path.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Imports
from v4.settings.settings_CPS_v4 import load_settings
from v4.engine.data_loader_v4 import load_data_for_backtest
from v4.engine.signal_generator_v4 import create_signal_generator


def main():
    # 1. Load settings
    print("Loading settings...")
    settings = load_settings()

    # 2. Load data
    print("Loading market data...")
    data_bundle = load_data_for_backtest(settings)
    price_data = data_bundle.get('price_data')
    returns_data = data_bundle.get('returns_data')
    rf = data_bundle.get('risk_free_rate')
    print(f"Price data: {price_data.shape}, returns data: {returns_data.shape}")

    # 3. Prepare signal generator
    strategy_section = settings.get('strategy', {})
    strategy_name = strategy_section.get('strategy_name', 'equal_weight')
    signal_params = {k: v for k, v in strategy_section.items() if k != 'strategy_name'}
    print(f"Creating signal generator: {strategy_name} with params {signal_params}")
    generator = create_signal_generator(strategy_name, **signal_params)

    # 4. Generate full signal history
    print("Generating full signal history...")
    signal_history = {}
    for current_date in price_data.index:
        subset = price_data.loc[:current_date]
        sig = generator.generate_signals(subset, **signal_params)
        # generator.generate_signals returns {timestamp: {symbol: weight}}
        # unwrap to dict of weights
        # Handle empty signals gracefully
        if isinstance(sig, dict):
            weights = sig
        else:
            weights = {}
        signal_history[current_date] = weights

    # Create final signal DataFrame
    df_signals = pd.DataFrame(0, index=price_data.index, columns=price_data.columns)

    # --- Calculate and save EMAs ---
    st_lb = int(signal_params['st_lookback']['default_value']) if isinstance(signal_params.get('st_lookback'), dict) else int(signal_params.get('st_lookback', 10))
    mt_lb = int(signal_params['mt_lookback']['default_value']) if isinstance(signal_params.get('mt_lookback'), dict) else int(signal_params.get('mt_lookback', 50))
    lt_lb = int(signal_params['lt_lookback']['default_value']) if isinstance(signal_params.get('lt_lookback'), dict) else int(signal_params.get('lt_lookback', 150))
    print(f"Calculating EMAs (spans: {st_lb}, {mt_lb}, {lt_lb})...")
    st_ema = price_data.ewm(span=st_lb).mean()
    mt_ema = price_data.ewm(span=mt_lb).mean()
    lt_ema = price_data.ewm(span=lt_lb).mean()
    st_ema.to_csv(_project_root / 'v4_trace_outputs' / f"ema_short_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    mt_ema.to_csv(_project_root / 'v4_trace_outputs' / f"ema_medium_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    lt_ema.to_csv(_project_root / 'v4_trace_outputs' / f"ema_long_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")

    # --- Calculate and save ranking matrix ---
    print("Calculating ranking matrix...")
    # Calculate trend strength from EMAs
    raw_trend = (st_ema.div(mt_ema) - 1) + (mt_ema.div(lt_ema) - 1)
    raw_trend = raw_trend.clip(lower=0)
    
    # Rank assets by trend strength (rank 1 = strongest trend)
    ranking = raw_trend.rank(axis=1, ascending=False, method='first').astype(int)
    ranking.to_csv(_project_root / 'v4_trace_outputs' / f"ranking_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
    
    # Refactored to use new parameter paths: settings['system']['system_top_n']['default_value']
    from v4.settings.settings_utils import _extract_value_from_complex_dict
    system_section = settings.get('system', {})
    if 'system_top_n' not in system_section:
        raise ValueError("Required parameter 'system_top_n' not found in settings['system'] section")
    
    system_top_n_raw = system_section['system_top_n']
    if not isinstance(system_top_n_raw, dict) or 'default_value' not in system_top_n_raw:
        raise ValueError("system_top_n must be an optimizable parameter dict with 'default_value'")
    
    # Extract the numeric value using helper function
    system_top_n_value = int(_extract_value_from_complex_dict(system_top_n_raw))
    
    # Create dict for compatibility with existing code
    system_top_n = {'default_value': system_top_n_value}
    
    print(f"Selecting top {system_top_n['default_value']} assets from ranking...")
    
    print("Creating signal DataFrame...")
    # Create signal DataFrame with equal weights for top N assets
    # The mask selects assets where rank <= N (i.e., the N highest ranked assets)
    mask = ranking <= system_top_n['default_value']
    print(f"Created mask with shape {mask.shape}, sum per row: {mask.sum(axis=1).value_counts().to_dict()}")
    
    df_signals = pd.DataFrame(0.0, index=price_data.index, columns=price_data.columns, dtype=float)
    print(f"Created empty signal DataFrame with shape {df_signals.shape} and dtype {df_signals.dtypes.iloc[0]}")
    
    df_signals[mask] = 1.0 / system_top_n['default_value']
    print(f"Assigned weights of {1.0 / system_top_n['default_value']} to selected assets")

    # Verify signal DataFrame
    print("Starting signal DataFrame validation...")
    
    # Each row should have exactly system_top_n non-zero values
    non_zero_counts = (df_signals != 0).sum(axis=1)
    print(f"Non-zero counts per row: {non_zero_counts.value_counts().to_dict()}")
    if not all(non_zero_counts == system_top_n['default_value']):
        print("ERROR: Some rows have incorrect number of non-zero values")
        print(f"Expected {system_top_n['default_value']} non-zero values per row")
        print(f"Found rows with these counts: {non_zero_counts.value_counts().to_dict()}")
        raise ValueError(f"Signal DataFrame validation failed: some rows have incorrect number of non-zero values")
    print("[OK] All rows have correct number of non-zero values")
    
    # All non-zero values should be equal to 1/system_top_n
    equal_weight_value = 1.0 / system_top_n['default_value']
    weight_check = (df_signals == 0) | (df_signals == equal_weight_value)
    if not weight_check.all(axis=1).all():
        print("ERROR: Some weights are not equal to the expected value")
        print(f"Expected weights to be either 0 or {equal_weight_value}")
        print("Found these unique values in df_signals:")
        print(df_signals.values.flatten().unique())
        raise ValueError(f"Signal DataFrame validation failed: non-zero weights are not equal to {equal_weight_value}")
    print("[OK] All weights are correct")

    print(f"Generated signal history DataFrame: {df_signals.shape}")
    print(f"Verified: each row has exactly {system_top_n['default_value']} assets with weight {equal_weight_value}")


    # 5. Save outputs
    trace_dir = _project_root / 'v4_trace_outputs'
    trace_dir.mkdir(exist_ok=True)
    ts = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save price and returns
    price_data.to_csv(trace_dir / f"price_data_{ts}.csv")
    returns_data.to_csv(trace_dir / f"returns_data_{ts}.csv")
    pd.DataFrame(rf).to_csv(trace_dir / f"risk_free_rate_{ts}.csv")
    df_signals.to_csv(trace_dir / f"signal_history_full_{ts}.csv")

    print(f"Saved signal history to {trace_dir / f'signal_history_full_{ts}.csv'}")


if __name__ == '__main__':
    main()
