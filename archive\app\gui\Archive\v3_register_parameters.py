"""
V3 Parameter System - Parameter Registration

This module handles the registration of parameters with the V3 registry
for use in the GUI and backtest system.
"""

import logging
from v3_engine import (
    NumericParameter,
    ConfigParameter,
    CategoricalParameter,
    CategoricalListParameter,
    StrategyOptimizeParameter,
    register_strategy_parameters
)

# Import configuration
from config.config_v3 import config_v3 as config
from models.ema_allocation_model import get_ema_parameters

# Set up logging
logger = logging.getLogger(__name__)


def register_v3_parameters(registry):
    """
    Register all parameters with the V3 registry.
    
    Args:
        registry (ParameterRegistry): V3 parameter registry
    """
    # Clear any existing parameters
    for group in ['core', 'strategy_ema', 'reporting', 'visualization']:
        registry.clear_group(group)
    
    # Register core parameters
    register_core_parameters(registry)
    
    # Register EMA strategy parameters
    register_ema_parameters(registry)
    
    # Register reporting and visualization parameters
    try:
        from v3_reporting.parameter_registry_integration import register_all_reporting_parameters
        success = register_all_reporting_parameters(registry)
        if success:
            logger.info("Reporting and visualization parameters registered successfully")
        else:
            logger.warning("Some reporting parameters could not be registered")
    except Exception as e:
        logger.error(f"Error registering reporting parameters: {e}")
    
    logger.info("V3 parameters registered successfully")


def register_core_parameters(registry):
    """
    Register core engine parameters with the V3 registry.
    
    Args:
        registry (ParameterRegistry): V3 parameter registry
    """
    # Register data parameters first to ensure they're available for data loading
    registry.register_parameter_list('data', [
        ConfigParameter(
            name='data_storage_mode',
            default=config['data_params']['data_storage_mode'],
            description="Data storage mode: 'Save', 'Read', or 'New'"
        ),
        ConfigParameter(
            name='start_date',
            default=config['data_params']['start_date'],
            description="Start date for backtest data"
        ),
        ConfigParameter(
            name='end_date',
            default=config['data_params']['end_date'],
            description="End date for backtest data"
        ),
        ConfigParameter(
            name='price_field',
            default=config['data_params']['price_field'],
            description="Price field to use for calculations"
        ),
        ConfigParameter(
            name='risk_free_ticker',
            default=config['data_params']['risk_free_ticker'],
            description="Ticker for risk-free rate"
        )
    ])
    # Register config-only parameters (not shown in GUI)
    registry.register_parameter_list('core', [
        ConfigParameter(
            name='initial_capital',
            default=config['backtest_params']['initial_capital'],
            description="Initial capital for portfolio (config-only, not shown in GUI)"
        ),
        ConfigParameter(
            name='commission_rate',
            default=config['backtest_params']['commission_rate'],
            description="Commission rate for trades (config-only, not shown in GUI)"
        ),
        ConfigParameter(
            name='slippage_rate',
            default=config['backtest_params']['slippage_rate'],
            description="Slippage rate for trades (config-only, not shown in GUI)"
        )
    ])
    
    # Register optimizable parameters with StrategyOptimizeParameter
    strategy_params = [
        StrategyOptimizeParameter(
            name='execution_delay',
            param_type='numeric',
            default=1 if isinstance(config['backtest_params']['execution_delay'], tuple) else config['backtest_params']['execution_delay'],
            min_val=0,
            max_val=5,
            step=1,
            optimize=True,  # Set to True by default for optimization
            show_in_gui=True,
            show_in_report=True,
            group='core',
            description="Trade execution delay in days"
        ),
        StrategyOptimizeParameter(
            name='rebalance_frequency',
            param_type='categorical',
            default=config['backtest_params']['rebalance_freq'],
            # Only include user-friendly terms for consistency
            choices=['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
            optimize=False,
            show_in_gui=True,
            show_in_report=True,
            group='core',
            description="Portfolio rebalancing frequency (rebalancing occurs on the last day of each period)"
        )
    ]
    
    # Convert StrategyOptimizeParameters to regular parameters and register directly
    for param in strategy_params:
        if param.group == 'core':
            # Register directly to core group
            v3_param = param.to_v3_parameter()
            registry.register_parameter('core', v3_param)
        else:
            # Use the strategy registration for non-core parameters
            register_strategy_parameters(registry, param.group.replace('strategy_', ''), [param])


def register_ema_parameters(registry):
    """
    Register EMA strategy parameters with the V3 registry.
    
    Args:
        registry (ParameterRegistry): V3 parameter registry
    """
    # Get EMA parameters from existing model
    ema_params = get_ema_parameters()
    
    # Create strategy optimize parameters
    strategy_params = []
    
    # Short-term lookback
    st_param = ema_params['st_lookback']
    strategy_params.append(StrategyOptimizeParameter(
        name='st_lookback',
        param_type='numeric',
        default=st_param[1],  # Default value
        min_val=st_param[2],  # Min value
        max_val=st_param[3],  # Max value
        step=st_param[4],  # Step size
        optimize=False,  # Set to False by default
        show_in_gui=True,
        show_in_report=True,
        group='strategy_ema',
        description="Short-term EMA lookback period"
    ))
    
    # Medium-term lookback
    mt_param = ema_params['mt_lookback']
    strategy_params.append(StrategyOptimizeParameter(
        name='mt_lookback',
        param_type='numeric',
        default=mt_param[1],
        min_val=mt_param[2],
        max_val=mt_param[3],
        step=mt_param[4],
        optimize=False,  # Set to False by default
        show_in_gui=True,
        show_in_report=True,
        group='strategy_ema',
        description="Medium-term EMA lookback period"
    ))
    
    # Long-term lookback
    lt_param = ema_params['lt_lookback']
    strategy_params.append(StrategyOptimizeParameter(
        name='lt_lookback',
        param_type='numeric',
        default=lt_param[1],
        min_val=lt_param[2],
        max_val=lt_param[3],
        step=lt_param[4],
        optimize=False,  # Set to False by default
        show_in_gui=True,
        show_in_report=True,
        group='strategy_ema',
        description="Long-term EMA lookback period"
    ))
    
    # Add top_n parameter
    strategy_params.append(StrategyOptimizeParameter(
        name='top_n',
        param_type='numeric',
        default=config['strategy_params'].get('top_n', 2),
        min_val=1,
        max_val=5,
        step=1,
        optimize=False,
        show_in_gui=True,
        show_in_report=True,
        group='strategy_ema',
        description="Number of top assets to hold"
    ))
    
    # Add signal_algo parameter
    strategy_params.append(StrategyOptimizeParameter(
        name='signal_algo',
        param_type='categorical',
        default='ema_crossover',
        choices=['ema_crossover', 'momentum', 'trend_following'],
        optimize=False,
        show_in_gui=True,
        show_in_report=True,
        group='strategy_ema',
        description="Signal generation algorithm"
    ))
    
    # Add tickers parameter
    # Use the ticker list directly from data_params
    tickers = config.get('data_params', {}).get('tickers', [])
    
    # For categorical parameter, create a comma-separated string of tickers
    ticker_str = ', '.join(tickers)
    
    # Create choices for the dropdown - just one option showing all tickers
    ticker_choices = [ticker_str]
    
    strategy_params.append(StrategyOptimizeParameter(
        name='tickers',
        param_type='categorical',
        default=ticker_str,
        choices=ticker_choices,
        optimize=False,
        show_in_gui=True,
        show_in_report=True,
        group='strategy_ema',
        description="Ticker group to use for strategy"
    ))
    
    # Register strategy parameters
    register_strategy_parameters(registry, "ema", strategy_params)
