"""
engine/data_loader_v4.py (archived)
Original CPS v4 data_loader implementation archived on 2025-06-06
"""
import pandas as pd
import logging
import os
from pathlib import Path
from CPS_v4.settings_CPS_v4 import load_settings
from config.paths import DATA_DIR

# Load CPS v4 settings
settings = load_settings()
data_params = settings.get('data_params', {})
tickers = data_params.get('tickers', [])
start_date = data_params.get('start_date', None)
end_date = data_params.get('end_date', None)
price_field = data_params.get('price_field', 'Close')
mode = data_params.get('data_storage_mode', 'Save')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_adjusted_close_data(tickers, start_date, end_date=None, price_field='Close'):
    from utils.date_utils import (
        standardize_date, standardize_date_range,
        standardize_dataframe_index, filter_dataframe_by_dates
    )
    start_ts, end_ts = standardize_date_range(start_date, end_date)
    price_data = pd.DataFrame()
    for ticker in tickers:
        data = pd.DataFrame()
        try:
            from market_data import data_fetch_stock_data
            data = data_fetch_stock_data(ticker=ticker, period="max", interval="1d")
            if hasattr(data.index, 'tz') and data.index.tz is not None:
                data.index = data.index.tz_localize(None)
            data = standardize_dataframe_index(data)
            data = filter_dataframe_by_dates(data, start_ts, end_ts)
            for field in [price_field, 'Adj Close', 'Close']:
                if field in data.columns:
                    price_data[ticker] = data[field]
                    break
        except Exception as e:
            logger.error(f"Error retrieving data for {ticker}: {e}")
    price_data = price_data.fillna(method='ffill').fillna(0)
    return price_data

def load_ticker_data():
    DATA_DIR.mkdir(exist_ok=True)
    file_name = f"tickerdata_{'_'.join(tickers)}_{start_date}_{end_date}.xlsx"
    xlsx_path = DATA_DIR / file_name
    logger.info(f"[TickerData] Mode={mode}, file={xlsx_path}")
    if mode.lower() == 'read':
        if not xlsx_path.exists():
            raise FileNotFoundError(f"Ticker data file not found: {xlsx_path}")
        df = pd.read_excel(xlsx_path, index_col=0, parse_dates=True)
    else:
        df = get_adjusted_close_data(tickers, start_date, end_date, price_field)
        if mode.lower() == 'save':
            df.to_excel(xlsx_path)
            logger.info(f"[TickerData] Saved data to {xlsx_path}")
    return df

def get_returns_data(price_data):
    returns = price_data.pct_change().fillna(0)
    return returns

def load_data_for_backtest():
    df = load_ticker_data()
    rf = pd.Series(0.0, index=df.index)
    return {'price_data': df, 'returns_data': get_returns_data(df), 'risk_free_rate': rf}
