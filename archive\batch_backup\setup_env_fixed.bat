@echo off
call F:\AI_Library\my_quant_env\Scripts\activate.bat || (
  echo ERROR: Failed to activate virtual environment
  pause
  exit /b 1
)

pip install networkx graphviz || (
  echo ERROR: Package installation failed
  pause
  exit /b 1
)

python -c "try:
  import networkx as nx, graphviz
  print('NetworkX Version: ' + nx.__version__)
  print('Graphviz Version: ' + graphviz.__version__)
  print('SETUP SUCCESSFUL')
except Exception as e:
  print('ERROR: ' + str(e))
  exit(1)"

pause
