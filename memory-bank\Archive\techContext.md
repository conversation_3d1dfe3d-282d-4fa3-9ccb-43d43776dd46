# Tech Context

- Python (latest version)
- Uses Custom Function Library for core logic
- Environment isolation: batch file + virtualenv (never conda)
- Output: XLSX files with ticker, date, time in filename
- No timestamps/timezones in daily data (only in filenames/logs)
- All path fixes in config/paths.py only
- Parameter optimization via define_parameter/optimize_parameters

_Add further technical constraints or dependency notes as needed._

**Current status:**
- V3 testing phase in progress
- Parameter audit and benchmark grouping complete
- No V3 backtests or reports yet
- Reporting is a major concern for next milestone
