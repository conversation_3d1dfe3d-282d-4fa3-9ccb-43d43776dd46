# Trade Filter Implementation - CPS v4

## Overview

The trade filter module (`v4/utils/trade_filter.py`) implements trading constraints based on current portfolio allocations to prevent excessive trading and maintain stability.

## Core Features

### 1. Fetch Current Allocation
- **Function**: `fetch_current_allocation(portfolio: Portfolio) -> pd.Series`
- **Purpose**: Retrieves the current allocation percentages from the portfolio object
- **Returns**: Pandas Series with asset weights as percentages of total portfolio value

### 2. Compute Percentage Difference
- **Function**: `compute_percent_diff(current: pd.Series, proposed: pd.Series) -> pd.Series`
- **Purpose**: Calculates the percentage difference between current and proposed allocations
- **Formula**: `(proposed - current).abs() / current.clip(lower=1e-9)`

### 3. Filter Trades
- **Function**: `filter_trades(current_allocation, proposed_allocation, portfolio=None, price_data=None, trade_date=None) -> Dict[str, pd.Series]`
- **Purpose**: Enforces the 2% deviation rule and business calendar checks
- **Returns**: Dictionary with 'allowed' and 'filtered' trades

### 4. Business Calendar Check
- **Function**: `is_business_day(check_date: date, price_data: pd.DataFrame) -> bool`
- **Purpose**: Determines if a date is a business day based on price data availability
- **Logic**: Uses price-data availability as the sole truth for business calendar
- **Implementation**: Checks if the given date exists in the price data index
- **Calendar Source**: No external calendar needed - relies on actual market data availability

## Configuration

### Settings Parameters

#### Deviation Threshold
- **Parameter**: `backtest.deviation_threshold`
- **Default**: `0.02` (2%)
- **Location**: `v4/settings/settings_parameters_v4.ini`
- **Description**: Maximum allowed percentage point deviation for individual asset allocations. This is measured as absolute percentage of total portfolio equity.

```ini
[Backtest]
deviation_threshold = 0.02
```

#### How to Tune the Threshold

**Understanding the Threshold**:
- The threshold is measured in percentage points of total portfolio equity
- A 2% threshold means an asset allocation can deviate by up to 2 percentage points before triggering a rebalance
- Example: If SPY target is 50% and current is 47%, the deviation is 3 percentage points, exceeding the 2% threshold

**Tuning Guidelines**:
- **Lower threshold (1-2%)**: More frequent rebalancing, tighter control, higher transaction costs
- **Higher threshold (3-5%)**: Less frequent rebalancing, looser control, lower transaction costs
- **Very high threshold (10%+)**: Infrequent rebalancing, suitable for long-term strategies

**Recommended Starting Values**:
- Daily strategies: 1-2%
- Weekly strategies: 2-3%
- Monthly strategies: 3-5%
- Quarterly strategies: 5-10%

#### Calendar Check Configuration

**Business Day Validation**:
- **Method**: Price data availability check
- **Logic**: If price data exists for a date, it's considered a business day
- **No Configuration Required**: The system automatically uses the price data index as the business calendar
- **Behavior**: If no price data is available for a trade date, all trades are filtered (no rebalancing occurs)

**How It Works**:
1. When `trade_date` and `price_data` are provided to `filter_trades()`
2. The system checks if the trade date exists in the price data index
3. If not found, returns empty allowed trades with reason 'Non-business day'
4. If found, proceeds with normal deviation threshold filtering

**Benefits of Price-Data-Based Calendar**:
- Always accurate for the specific data source being used
- No need to maintain separate holiday calendars
- Automatically handles market closures, early closes, and data gaps
- Consistent with actual data availability

## Integration

The trade filter is integrated into the main backtest engine (`v4/engine/backtest_v4.py`) at the point where trading signals are converted to orders:

```python
# Use trade_filter to enforce 2% deviation rule
current_allocation = fetch_current_allocation(portfolio)
filtered_trades = filter_trades(
    current_allocation,
    signals,
    portfolio=portfolio,
    price_data=price_data,
    trade_date=current_date.date() if hasattr(current_date, 'date') else current_date
)

allowed_signals = filtered_trades['allowed']
# Convert back to dict for calculate_rebalance_orders
if isinstance(allowed_signals, pd.Series):
    allowed_signals = allowed_signals.to_dict()
```

## Example Usage

```python
from v4.utils.trade_filter import fetch_current_allocation, filter_trades
from v4.engine.portfolio_v4 import Portfolio
import pandas as pd

# Create portfolio and get current allocation
portfolio = Portfolio()
current_allocation = fetch_current_allocation(portfolio)

# Define proposed allocation
proposed_allocation = pd.Series({'SPY': 0.5, 'TLT': 0.3, 'GLD': 0.2})

# Filter trades based on deviation threshold
result = filter_trades(current_allocation, proposed_allocation)

print(f"Allowed trades: {result['allowed']}")
print(f"Filtered trades: {result['filtered']}")
```

## Trade Filtering Logic

1. **Business Day Check**: If a trade date and price data are provided, the system first checks if the date is a business day based on price data availability.

2. **Allocation Alignment**: Both current and proposed allocations are aligned to ensure they contain the same assets.

3. **Deviation Calculation**: For each asset, the percentage difference between current and proposed allocations is calculated.

4. **Threshold Enforcement**: Assets with deviations exceeding the threshold (default 2%) are filtered out.

5. **Logging**: The system logs detailed information about which trades were allowed or filtered.

## Testing

The module includes comprehensive tests in `v4/tests/test_trade_filter.py`:

- Test portfolio allocation fetching
- Test percentage difference calculations
- Test trade filtering with various scenarios
- Test business day validation

Run tests with:
```bash
python v4/tests/test_trade_filter.py
```

## Benefits

1. **Risk Management**: Prevents excessive concentration in any single asset
2. **Stability**: Reduces portfolio turnover and transaction costs
3. **Robustness**: Uses actual price data availability for business day determination
4. **Configurable**: Easy to adjust the deviation threshold via settings
5. **Transparent**: Comprehensive logging shows exactly which trades are filtered and why

## Future Enhancements

Potential improvements could include:
- Multiple threshold levels for different asset classes
- Time-based deviation rules (e.g., stricter limits during volatile periods)
- Position size-based thresholds
- Integration with volatility-based risk measures
