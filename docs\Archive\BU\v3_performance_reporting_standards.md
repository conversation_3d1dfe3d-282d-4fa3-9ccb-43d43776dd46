#Reporting Standards

This document outlines the required standards for performance reporting in the backtest engine. These standards must be strictly followed to maintain consistency and usability.

## performance_tables

Performance tables must be saved as: `EMA_V3_1_performance_tables_YYYYMMDD_HHMMSS.xlsx`

- ### Column Ordering
1. Parameter columns MUST appear on the left side in this order:
   
   - Strategy (containing either "Benchmark" or "Strategy" values)
   - st_lookback
   - mt_lookback
   - lt_lookback
   - execution_delay
   - top_n
   - [Other parameters if any]

2. Performance metric columns should appear after parameter columns:
   
   - CAGR
   - Sharpe
   - Sortino
   - Max Drawdown
   - Win Rate
   - Final Value
   - YTD returns
   - Annual returns (most recent first)

### Number Formatting

All numeric values must use Excel's native number formatting:

- Percentages (CAGR, Max Drawdown, Win Rate, Annual Returns): `0.00%`
- Ratios (Sharpe, Sortino): `0.00`
- Currency values (Final Value): `$#,##0.00`

### Benchmark Row

- The benchmark row must appear only ONCE at the top of the performance table
- The benchmark row must be fully populated with performance metrics
- Parameter columns should be empty for the benchmark row

## Signal and Allocation History Reports

### Signal History Report (Tab: "Signal History")

- Must show daily signals (dates only, no timestamps)
- Tickers as columns
- Values show % allocation signal at end of day when signal is generated
- Each row must sum to 100%
- Include a "Cash" column for unallocated balance
- Use percentage formatting (0.00%)

### Allocation History Report (Tab: "Allocation History")

- Same structure and format as Signal History
- Values show actual % allocation from trade log history (execution engine output)
- Typically shows 1-day lag from signal date due to allocation handling
- Each row must sum to 100%
- Include a "Cash" column for unallocated balance
- Use percentage formatting (0.00%)

## Implementation Notes

1. When creating performance tables:
   
   - Use Excel's native number formatting instead of string formatting
   - Maintain the correct column order with parameters on the left
   - Ensure the benchmark row appears only once at the top

2. When modifying the performance reporting code:
   
   - Do not change the column ordering
   - Do not modify the Excel formatting approach
   - Do not remove any existing functionality

## DO NOT CHANGE Warning

Sections of code related to performance reporting standards are marked with "WORKING ONLY Change with Permission" comments. These sections must not be modified without explicit approval.
