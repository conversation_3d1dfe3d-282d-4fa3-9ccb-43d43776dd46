@echo off
REM Batch file to run backtest with parameter flow debug enabled

REM Activate the Python environment (PC1 setup)
call F:\AI_Library\my_quant_env\Scripts\activate

REM Change to the project directory
cd /d "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"

REM Run the script with both debug flags for full diagnostics
python run_backtest_v2_with_metrics.py --debug --debug-paramflow

pause
