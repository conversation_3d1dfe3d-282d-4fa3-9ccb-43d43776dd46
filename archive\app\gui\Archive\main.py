import sys
import os
from PySide6.QtWidgets import (
    QA<PERSON>lication,
    QMainWindow,
    QWidget,
    QLabel,
    QCheckBox,
    QLineEdit,
    QComboBox,
    QPushButton,
    QMessageBox,
    QHBoxLayout,
    QVBoxLayout,
    QScrollArea,
    QSizePolicy
)
from PySide6.QtCore import Qt
from app.gui.config_interface import load_params, save_to_ini
import config.config_v2 as config_mod
from run_backtest_v2_with_metrics import run_backtest_with_metrics

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("WTP Backtesting Engine")
        self.resize(800, 600)
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QVBoxLayout(central)

        # Title and logo placeholder
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_label = QLabel("WTP Backtesting Engine")
        font = title_label.font()
        font.setPointSize(20)
        font.setBold(True)
        title_label.setFont(font)
        title_label.setAlignment(Qt.AlignCenter)
        spacer = QWidget()
        spacer.setFixedSize(200, 50)  # reserved for a logo
        title_layout.addWidget(title_label, stretch=1)
        title_layout.addWidget(spacer)
        main_layout.addWidget(title_widget)

        # Instruction label
        info_label = QLabel("Check box to optimize/loop on variable")
        info_font = info_label.font()
        info_font.setPointSize(10)
        info_label.setFont(info_font)
        info_label.setAlignment(Qt.AlignLeft)
        main_layout.addWidget(info_label)

        # Scrollable area for parameters
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        container = QWidget()
        param_layout = QVBoxLayout(container)
        param_layout.setSpacing(3)
        self.widgets = {}

        params = load_params()
        order = [
            'strategy', 'data_storage_mode', 'date_range', 'tickers',
            'st_lookback', 'mt_lookback', 'lt_lookback',
            'execution_delay', 'top_n', 'rebalance_freq'
        ]
        for name in order:
            p = params.get(name, {})
            # Special row: combined start/end date
            if name == 'date_range':
                row = QWidget()
                row_layout = QHBoxLayout(row)
                row_layout.setAlignment(Qt.AlignLeft)
                lbl = QLabel("Date Range")
                start_p = params.get('start_date', {})
                end_p = params.get('end_date', {})
                start_editor = QLineEdit(str(start_p.get('default', '')))
                end_editor = QLineEdit(str(end_p.get('default', '')))
                start_editor.setMaximumWidth(100)
                end_editor.setMaximumWidth(100)
                start_editor.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                end_editor.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                row_layout.setSpacing(8)
                row_layout.setAlignment(Qt.AlignLeft)
                row_layout.addWidget(lbl)
                row_layout.addWidget(QLabel("Start Date"))
                row_layout.addWidget(start_editor)
                row_layout.addWidget(QLabel("End Date"))
                row_layout.addWidget(end_editor)
                param_layout.addWidget(row)
                self.widgets['start_date'] = {'default': start_editor}
                self.widgets['end_date'] = {'default': end_editor}
                continue
            row = QWidget()
            row_layout = QHBoxLayout(row)
            row_layout.setAlignment(Qt.AlignLeft)
            row_layout.setSpacing(8)
            lbl = QLabel(name)
            chk = None
            if name in ['strategy', 'rebalance_freq', 'tickers'] or p.get('min') is not None:
                chk = QCheckBox()
                chk.setChecked(p.get('optimize', False))
            default_val = p.get('default', '')
            if name == 'tickers' and isinstance(default_val, (list, tuple)):
                default_val = ','.join(default_val)

            # Editor widget
            if name == 'strategy':
                editor = QComboBox()
                strategies = config_mod.config_v2.get('strategies', [])
                editor.addItems(strategies)
                if default_val:
                    editor.setCurrentText(default_val)
            elif name == 'rebalance_freq':
                editor = QComboBox()
                for freq in ['D', 'weekly', 'MS', 'QS', 'AS']:
                    editor.addItem(freq)
                if default_val:
                    editor.setCurrentText(default_val)
            elif name == 'data_storage_mode':
                editor = QComboBox()
                for mode in ['Save', 'Read', 'New']:
                    editor.addItem(mode)
                if default_val:
                    editor.setCurrentText(default_val)
            else:
                editor = QLineEdit(str(default_val))
                if name == 'tickers':
                    editor.setFixedWidth(1600)
                    editor.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
                else:
                    editor.setMaximumWidth(40)
                    editor.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

            mn = p.get('min')
            mx = p.get('max')
            step = p.get('step')
            mn_edit = QLineEdit(str(mn) if mn is not None else '')
            mn_edit.setMaximumWidth(40)
            mn_edit.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
            mx_edit = QLineEdit(str(mx) if mx is not None else '')
            mx_edit.setMaximumWidth(40)
            mx_edit.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
            step_edit = QLineEdit(str(step) if step is not None else '')
            step_edit.setMaximumWidth(40)
            step_edit.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

            row_layout.addWidget(lbl)
            if chk:
                row_layout.addWidget(chk)
            row_layout.addWidget(editor)
            if p.get('min') is not None:
                row_layout.addWidget(mn_edit)
                row_layout.addWidget(mx_edit)
                row_layout.addWidget(step_edit)
            row_layout.addStretch()
            param_layout.addWidget(row)

            self.widgets[name] = {
                'opt': chk,
                'default': editor,
                'min': mn_edit,
                'max': mx_edit,
                'step': step_edit
            }

        scroll.setWidget(container)
        main_layout.addWidget(scroll)

        # Options section
        options_widget = QWidget()
        options_layout = QHBoxLayout(options_widget)
        
        # EMA tracking checkbox
        self.track_ema_checkbox = QCheckBox("Track detailed EMA calculations")
        self.track_ema_checkbox.setToolTip("When checked, creates detailed EMA calculation files for all parameter combinations.\nWhen unchecked, only creates one EMA file with default parameters.")
        options_layout.addWidget(self.track_ema_checkbox)
        
        # Add options to main layout
        main_layout.addWidget(options_widget)
        
        # Run Backtest button
        run_btn = QPushButton("Run Backtest")
        run_btn.setStyleSheet("background-color: green; color: white; font-weight: bold; font-size: 14pt;")
        run_btn.clicked.connect(self.handle_run)
        main_layout.addWidget(run_btn)

        # Save button
        btn_save = QPushButton("Save")
        btn_save.clicked.connect(self.handle_save)
        main_layout.addWidget(btn_save)

    def handle_save(self):
        values = {}
        for name, w in self.widgets.items():
            editor = w['default']
            if isinstance(editor, QLineEdit):
                values[name] = editor.text()
            else:
                values[name] = editor.currentText()

        ini_path = os.path.join(os.path.dirname(__file__), 'user_config.ini')
        save_to_ini(ini_path, values)
        QMessageBox.information(self, "Saved", f"Configuration saved to {ini_path}")

    def handle_run(self):
        # Collect current GUI values
        values = {}
        for name, w in self.widgets.items():
            editor = w['default']
            text = editor.text() if isinstance(editor, QLineEdit) else editor.currentText()
            values[name] = text

        # Update configuration dicts
        data_params = config_mod.data_params
        backtest_params = config_mod.backtest_params
        strategy_params = config_mod.strategy_params

        # Data parameters
        ticker_str = values.get('tickers', '')
        tickers_list = [t.strip() for t in ticker_str.split(',') if t.strip()]
        if tickers_list:
            data_params['tickers'] = tickers_list
        if 'start_date' in values:
            data_params['start_date'] = values['start_date']
        if 'end_date' in values:
            data_params['end_date'] = values['end_date']

        # Backtest parameters
        if 'rebalance_freq' in values:
            backtest_params['rebalance_freq'] = values['rebalance_freq']
        if 'execution_delay' in values:
            try:
                # Treat execution_delay as optimizable parameter
                default_val = int(values['execution_delay'])
                w = self.widgets['execution_delay']
                optimize = w['opt'].isChecked() if w.get('opt') else False
                mn_text = w['min'].text() if w.get('min') else ''
                mx_text = w['max'].text() if w.get('max') else ''
                step_text = w['step'].text() if w.get('step') else ''
                min_val = int(mn_text) if mn_text else default_val
                max_val = int(mx_text) if mx_text else default_val
                step_val = int(step_text) if step_text else 1
                backtest_params['execution_delay'] = config_mod.define_parameter(optimize, default_val, min_val, max_val, step_val)
            except ValueError:
                pass

        # Strategy parameters
        for key in ['st_lookback', 'mt_lookback', 'lt_lookback', 'top_n']:
            if key in values:
                try:
                    default_val = int(values[key])
                    w = self.widgets[key]
                    # Determine optimize flag and ranges from GUI
                    optimize = w['opt'].isChecked() if w.get('opt') else False
                    mn_text = w['min'].text() if w.get('min') else ''
                    mx_text = w['max'].text() if w.get('max') else ''
                    step_text = w['step'].text() if w.get('step') else ''
                    min_val = int(mn_text) if mn_text else default_val
                    max_val = int(mx_text) if mx_text else default_val
                    step_val = int(step_text) if step_text else 1
                    strategy_params[key] = config_mod.define_parameter(optimize, default_val, min_val, max_val, step_val)
                except ValueError:
                    pass

        # Run backtest
        try:
            # Get EMA tracking option
            track_ema = self.track_ema_checkbox.isChecked()
            
            # Run backtest with parameters
            results = run_backtest_with_metrics(
                config=config_mod.config_v2,
                track_ema_calcs=track_ema
            )
            
            # Show completion message
            message = "Backtest completed successfully.\n\n"
            message += "Output files:\n"
            message += "- Performance tables: output/ema_performance_tables_*.xlsx\n"
            message += "- EMA calculations: output/ema_calculations/\n"
            
            QMessageBox.information(self, "Backtest Complete", message)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Backtest failed: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
