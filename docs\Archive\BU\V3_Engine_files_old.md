# V3 Parameter System Documentation

## Parameter Class Hierarchy

The V3 parameter system uses a class hierarchy to represent different types of parameters with varying behaviors:

```markdown
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
└── ConfigParameter
```

## Parameter Classes and Their Uses

| Class | Description | GUI Visible | Optimizable | Use Cases |
|-------|-------------|:-----------:|:-----------:|-----------|

---

## Difference Between `CategoricalParameter` and `CategoricalListParameter`

### CategoricalParameter

**CategoricalParameter** is used for selecting a single value from a fixed list of options defined directly in the parameter. For example:

```python
CategoricalParameter(
    name='rebalance_frequency',
    default=config['backtest_params']['rebalance_freq'],
    choices=['daily', 'weekly', 'monthly', 'quarterly', 'yearly'],
    optimize=False,
    description="Portfolio rebalancing frequency"
)
```

The value is a single string (e.g., 'weekly'). Used for simple, fixed choices like frequency or strategy type.

### CategoricalListParameter

**CategoricalListParameter** is used for selecting a group name that maps to a list of items, with the group definitions loaded from an external config file. For example:

```python
CategoricalListParameter(
    name='ticker_group',
    list_name='tech',
    config_file='config/ticker_lists.py',
    optimize=False,
    description="Group of tickers to use in backtest"
)
```

And in `config/ticker_lists.py`:

```python
TICKER_LISTS = {
    'tech': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META'],
    'finance': ['JPM', 'BAC', 'WFC', 'GS', 'MS'],
    'energy': ['XOM', 'CVX', 'COP', 'SLB', 'EOG']
}
```

The value is the group name (e.g., 'tech'), but you can call `get_group_items()` to get the actual list.

Used for user-maintained lists that may change frequently, such as ticker groups or asset classes.

### Summary Table

| Parameter Type           | Choices Source      | Value Type      | Example Use                |
|-------------------------|---------------------|-----------------|----------------------------|
| CategoricalParameter     | Defined in code     | Single value    | Rebalance frequency        |
| CategoricalListParameter | External config     | Group name (list)| Ticker group selection     |

---

| **BaseParameter** | Base class for all parameters | Yes | No | Default for any non-defined variables |
| **NumericParameter** | For numeric values with min/max/step | Yes | Yes | Lookback periods, thresholds, execution delay |
| **CategoricalParameter** | For selection from fixed options | Yes | Yes | Rebalance frequency, strategy selection |
| **CategoricalListParameter** | For user-defined groups in config files | Yes | Yes | Ticker groups, asset classes |
| **ConfigParameter** | For config-only values, not in GUI | No | No | Initial capital, commission rate, slippage rate |

## Parameter Matrix

Below is a comprehensive list of all parameters in the system, their types, and uses:

### Core Engine Parameters

| Parameter | Class | Group | Description | Default | GUI Visible | Optimizable |
|-----------|-------|-------|-------------|---------|:-----------:|:-----------:|
| `initial_capital` | ConfigParameter | core | Initial capital for portfolio | 1000000 | No | No |
| `commission_rate` | ConfigParameter | core | Commission rate for trades | 0.001 | No | No |
| `slippage_rate` | ConfigParameter | core | Slippage rate for trades | 0.001 | No | No |
| `execution_delay` | NumericParameter | core | Trade execution delay in days | 1 | Yes | Yes |
| `rebalance_frequency` | CategoricalParameter | core | Portfolio rebalancing frequency | 'weekly' | Yes | No |

### Data Parameters

| Parameter | Class | Group | Description | Default | GUI Visible | Optimizable |
|-----------|-------|-------|-------------|---------|:-----------:|:-----------:|
| `start_date` | BaseParameter | data | Start date for backtest | '2020-01-01' | No* | No |
| `end_date` | BaseParameter | data | End date for backtest | '2025-04-23' | No* | No |
| `data_storage_mode` | BaseParameter | data | How to handle data (Save/Read/New) | 'Read' | No* | No |
| `price_field` | BaseParameter | data | Price field for calculations | 'Close' | No* | No |
| `risk_free_ticker` | BaseParameter | data | Ticker for risk-free rate | '^IRX' | No* | No |

*These parameters are currently defined in config but not yet converted to parameter classes

### Benchmark Parameters

| Parameter | Class | Group | Description | Default | GUI Visible | Optimizable |
|-----------|-------|-------|-------------|---------|:-----------:|:-----------:|
| `benchmark_ticker` | BaseParameter | benchmark | Ticker for benchmark comparison | 'SPY' | No* | No |
| `benchmark_rebalance_freq` | BaseParameter | benchmark | Benchmark rebalancing frequency | 'yearly' | No* | No |
| `debug_benchmark` | BaseParameter | benchmark | Whether to log benchmark details | True | No* | No |

*These parameters are currently defined in config but not yet converted to parameter classes

### EMA Strategy Parameters

| Parameter | Class | Group | Description | Default | GUI Visible | Optimizable |
|-----------|-------|-------|-------------|---------|:-----------:|:-----------:|
| `st_lookback` | NumericParameter | strategy_ema | Short-term EMA lookback period | 15 | Yes | Yes |
| `mt_lookback` | NumericParameter | strategy_ema | Medium-term EMA lookback period | 70 | Yes | Yes |
| `lt_lookback` | NumericParameter | strategy_ema | Long-term EMA lookback period | 100 | Yes | Yes |
| `top_n` | NumericParameter | strategy_ema | Number of top assets to hold | 2 | Yes | No |

### Reporting Parameters

| Parameter | Class | Group | Description | Default | GUI Visible | Optimizable |
|-----------|-------|-------|-------------|---------|:-----------:|:-----------:|
| `create_excel` | BaseParameter | reporting | Whether to create Excel reports | True | No* | No |
| `save_trade_log` | BaseParameter | reporting | Whether to save trade logs | True | No* | No |
| `metrics` | BaseParameter | reporting | Performance metrics to include | ['total_return', ...] | No* | No |

### Visualization Parameters

| Parameter | Class | Group | Description | Default | GUI Visible | Optimizable |
|-----------|-------|-------|-------------|---------|:-----------:|:-----------:|
| `create_charts` | BaseParameter | visualization | Whether to create charts | True | No* | No |
| `chart_types` | BaseParameter | visualization | Types of charts to create | ['cumulative_returns', ...] | No* | No |
| `chart_format` | BaseParameter | visualization | Format for chart output | 'png' | No* | No |
| `chart_dpi` | BaseParameter | visualization | Resolution for chart output | 300 | No* | No |

*These parameters are currently defined in config but not yet converted to parameter classes

## V3 Engine Core Files

### Parameter System
{{ ... }}

### Data Quality Components
- `v3_engine/data_validator.py` - Data quality validation checks
- `v3_engine/data_quality_metrics.py` - Data quality scoring

### Error Handling
- `v3_engine/exception_handler.py` - Unified error handling
- `v3_engine/error_reporter.py` - Error reporting interface

### Strategy Management
{{ ... }}
- `v3_strategies/strategy_discovery.py` - Dynamic strategy loading
- `v3_strategies/strategy_validator.py` - Strategy configuration validation

## V3 Reporting System Components (Updated May 2025)

### Core Modules

1. **Parameter Registration** (`v3_engine/reporting_parameters.py`)
   - Central registry for all reporting parameters
   - Handles parameter types, defaults and validation
   - Integrated with main parameter registry

2. **Reporting Adapter** (`v3_engine/performance_reporter_adapter.py`)
   - Bridges V3 parameters to legacy reporting code
   - Handles parameter conversion and validation
   - Maintains backward compatibility

3. **Reporting Modules** (`v3_reporting/`)
   - `v3_performance_report.py`: Performance reporting wrapper
   - `v3_allocation_report.py`: Allocation reporting wrapper
   - `v3_visualization.py`: Chart generation wrapper

### Key Features

- **Parameter Flow**:
  ```mermaid
  graph TD
    GUI -->|Parameters| Engine
    Engine -->|Results + Parameters| V3_Reporting
    V3_Reporting -->|Formatted Output| Reports
  ```

- **Error Handling**:
  - Automatic parameter type conversion
  - Fallback logic for missing signal history
  - Graceful degradation when legacy features are unavailable

- **Optimization Support**:
  - Proper handling of parameter optimization tuples
  - Preservation of optimization context through reporting chain
  - Clear labeling of optimized parameters in output

## Parameter Flow

Parameters flow through the system in the following way:

1. **Definition**: Parameters are defined in their respective registration functions
2. **Registration**: Parameters are registered with the V3 registry
3. **GUI Integration**: Parameters marked with `show_in_gui=True` appear in the GUI
4. **Value Setting**: Parameter values are set via GUI or directly in code
5. **Engine Usage**: Parameters are passed to the backtest engine
6. **Reporting**: Parameter values are included in performance reports

## Parameter Conversion Guidelines

When converting config variables to parameter classes:

1. Use **ConfigParameter** for variables that:
   - Should not appear in the GUI
   - Should not be optimized
   - Are loaded from config
   - Should appear in performance reports

2. Use **NumericParameter** for variables that:
   - Have numeric values with min/max bounds
   - May need optimization
   - Should appear in the GUI

3. Use **CategoricalParameter** for variables that:
   - Have a fixed set of options
   - May need optimization over those options
   - Should appear in the GUI

4. Use **BaseParameter** as a fallback for any other variables
