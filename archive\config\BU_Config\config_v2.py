"""
Feeds in v3: he V3 GUI and engine both pull their defaults, ranges and “optimize” flags straight from config/config_v2.py via v3_register_parameters. Any edits you make there (e.g. changing a default, min/max or adding a new define_parameter entry) will be picked up next time you launch v3_gui_core.py and used by the backtest engine.
Configuration parameters for the improved backtest engine v2.

PARAMETER STANDARDS
------------------
All optimizable parameters MUST follow these standards:

1. Use the define_parameter function with the format:
   define_parameter(optimize, default_value, min_value, max_value, increment)
   
   Where:
   - optimize: Bo<PERSON>an (True/False) whether to optimize this parameter
   - default_value: Value to use if not optimizing
   - min_value: Minimum value for optimization range
   - max_value: Maximum value for optimization range
   - increment: Step size for optimization range

2. Returns a tuple in format: ('Y'/'N', default_value, min_value, max_value, increment)

3. Example usage:
   'lookback': define_parameter(True, 60, 30, 90, 10)  # Optimizable
   'top_n': define_parameter(False, 2, 1, 5, 1)  # Fixed value

See docs/parameter_opt.md for complete documentation on parameter standards.
"""

from .paths import *  # Import all path configurations
from .local_parameter_optimization import define_parameter, validate_parameter
from dataclasses import dataclass

@dataclass
class CategoricalParam:
    id: str
    default: str
    choices: list
    meta: dict

# NOTE: All benchmark-related variables are now grouped under `benchmark_params`.
# Use comments in the dictionary to indicate role (e.g., benchmark_primary, benchmark_secondary).
# This is for documentation and future special handling, with no code logic change at this time.

# Helper function to ensure backward compatibility
def _get_param_value(param):
    """
    Extract the value from a parameter, whether it's a tuple or a direct value.
    This ensures backward compatibility with code that expects direct values.
    
    Args:
        param: Either a parameter tuple or a direct value
        
    Returns:
        The parameter value
    """
    if isinstance(param, tuple) and len(param) == 5 and param[0] in ['Y', 'N']:
        return param[1]  # Return the default_value from the tuple
    return param  # Return the value directly if it's not a parameter tuple

# Data parameters
data_params = {
    # List of tickers to include in portfolio
    'tickers': ['SPY', 'SHV', 'EFA', 'TLT', 'PFF'],
    
    # Date range for backtesting
    'start_date': '2020-01-01',
    'end_date': '2025-04-23',
    
    # Price field to use for calculations
    'price_field': 'Close',
    
    # Risk-free rate ticker (used for performance metrics)
    'risk_free_ticker': '^IRX',  # 13-week Treasury Bill
    
    #In "Save" mode: downloads data using the existing logic (as in get_adjusted_close_data), saves to XLSX in data/, and returns DataFrame.
    #In "Read" mode: loads from XLSX in data/, errors if missing.
    #In "New" mode: downloads fresh data (like "Save"), but does NOT save.
    # Data storage mode: 'Save', 'Read', or 'New'
    'data_storage_mode': 'Read',
}

# Backtesting parameters
backtest_params = {
    # Strategy name (equal_weight, momentum, ema)
    'strategy': 'ema',
    
    # Rebalancing frequency
    # Options: 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
    # Pandas frequency strings are converted internally: 'daily'='B', 'weekly'='weekly', 'monthly'='MS', etc.
    'rebalance_freq': 'weekly',
    
    # Trade execution delay (in days)
    # 0 means same-day execution, 1 means next-day execution
    'execution_delay': define_parameter(False, 1, 0, 5, 1),
    
    # Transaction cost as fraction of trade value
    'commission_rate': 0.001,  # 0.1%
    
    # Slippage as fraction of price
    'slippage_rate': 0.001,  # 0.1%
    
    # Initial capital
    'initial_capital': 1000000,
    
    # Benchmark parameters are now grouped in benchmark_params below
}

# --- BENCHMARK PARAMETERS ---
# All benchmark-related variables are grouped here for clarity and future extensibility.
# Use comments to indicate role (e.g., primary, secondary).
benchmark_params = {
    'benchmark_ticker': 'SPY',  # role: benchmark_primary
    'benchmark_rebalance_freq': 'yearly',  # role: benchmark_primary
    'debug_benchmark': True,  # role: benchmark_primary
    'debug_benchmark': True,
}

# Strategy-specific parameters
strategy_params = {
    # Parameters for momentum strategy - using parameter standard format
    'top_n': define_parameter(False, 2, 1, 5, 1),      # Number of top assets to select
    
    # Parameters for EMA strategy - using parameter standard format
    # Following the naming convention from ema_allocation_model.py
    'st_lookback': define_parameter(False, 15, 5, 20, 5),    # Short-term EMA period (STLB)
    'mt_lookback': define_parameter(False, 70, 30, 90, 10),    # Medium-term EMA period (MTLB)
    'lt_lookback': define_parameter(False, 100, 100, 200, 25),   # Long-term EMA period (LTLB)
    'signal_algo': CategoricalParam(
        id='signal_algo',
        default='ema',
        choices=['ema'],
        meta={}
    )
}

# Parameter optimization settings
# optimization_params block commented out per user request
# optimization_params = {
#     'method': 'grid',
#     'n_iter': 10,
#     'evaluation_metric': 'sharpe_ratio',
# }

# Visualization parameters
visualization_params = {
    # Whether to create charts
    'create_charts': True,
    
    # Types of charts to create
    'chart_types': [
        'cumulative_returns',
        'drawdown',
        'return_distribution',
        'monthly_returns',
        'portfolio_weights'
    ],
    
    # Chart format (png, jpg, svg)
    'chart_format': 'png',
    
    # Chart resolution (DPI)
    'chart_dpi': 300,
}

# Reporting parameters
reporting_params = {
    # Whether to create Excel reports
    'create_excel': True,
    
    # Whether to save trade logs
    'save_trade_log': True,
    
    # Output directory for reports and logs
    'output_dir': str(OUTPUT_DIR),
    
    # Performance metrics to include
    'metrics': [
        'total_return',
        'annualized_return',
        'volatility',
        'sharpe_ratio',
        'max_drawdown',
        'win_rate',
    ],
}

# All parameters combined
config_v2 = {
    'venv_path': str(VENV_PATH),
    'custom_lib_path': str(CUSTOM_LIB_PATH),
    'output_dir': str(OUTPUT_DIR),
    'data_params': data_params,
    'backtest_params': backtest_params,
    'strategy_params': strategy_params,
    # List of strategies to run; must match registered strategy names
    'strategies': ['ema'],
    # 'optimization_params': optimization_params,  # commented out unused config
    'visualization_params': visualization_params,
    'reporting_params': reporting_params,
}

# Add backward compatibility layer for parameter access
# This allows existing code to access parameters without changes
class BackwardCompatDict(dict):
    def __getitem__(self, key):
        value = super().__getitem__(key)
        if isinstance(value, dict):
            return BackwardCompatDict({k: _get_param_value(v) for k, v in value.items()})
        return _get_param_value(value)
    
    def get(self, key, default=None):
        try:
            return self[key]
        except KeyError:
            return default

# Wrap config_v2 with backward compatibility
config_v2_compat = BackwardCompatDict(config_v2)

# Replace the original config_v2 with the compatible version
config_v2 = config_v2_compat

# Allocation rules by number of selected assets (top_n)
# Defines weight percentages by rank position:
# 1 -> [100%]
# 2 -> [60%, 40%]
# 3 -> [45%, 35%, 20%]
# 4 -> [35%, 30%, 20%, 15%]
# 5 -> [30%, 25%, 20%, 15%, 10%]
allocation_rules = {
    1: [1.0],
    2: [0.6, 0.4],
    3: [0.45, 0.35, 0.20],
    4: [0.35, 0.30, 0.20, 0.15],
    5: [0.30, 0.25, 0.20, 0.15, 0.10],
}
