# Signals Quick Reference for Warp Drive

**Create this document in Warp Drive with title: "SIGNALS REFERENCE - CPS V4"**

---

# EMA Signals Quick Reference - CPS V4

## Signal Generation Overview

**Algorithm:** EMA-based momentum ranking with equal-weight allocation to top N assets

**Process:**
1. Calculate 3 EMAs: Short-term, Medium-term, Long-term
2. Compute EMA ratios (ST/MT, MT/LT, Average)
3. Rank assets by average EMA ratio (descending)
4. Select top N assets (from `system_top_n` setting)
5. Assign equal weight (1/N) to selected assets, 0 to others

## Two-Phase Architecture

### Phase 1: Signal Generation (`Algo_signal_phase.py`)
- **Input:** Market data
- **Output:** 
  - `signals_output_YYYYMMDD_HHMMSS.csv` (timestamped)
  - `signals_output.parquet` (standard name for trading phase)
- **Execution:** `run_main_v4_signalalgo.bat`

### Phase 2: Trading (`run_trading_phase.py`)
- **Input:** Signal files from Phase 1
- **Output:**
  - `allocation_history_YYYYMMDD_HHMMSS.csv`
  - `trade_log_YYYYMMDD_HHMMSS.csv`
- **Execution:** `run_trading_phase_standalone.bat`

## Key Parameters (from CPS V4 settings)

- `st_lookback`: Short-term EMA period (e.g., 15 days)
- `mt_lookback`: Medium-term EMA period (e.g., 70 days)  
- `lt_lookback`: Long-term EMA period (e.g., 100 days)
- `system_top_n`: Number of top assets to select
- `min_weight`, `max_weight`: Weight bounds (0.0 to 1.0)

## File Locations

- **Signal Generation Code:** `v4/models/ema_allocation_model_v4.py`
- **Core Function:** `ema_allocation_model()`
- **Signal Factory:** `v4/Algo_signal_phase.py`
- **Output Directory:** `v4_trace_outputs/`

## Current Status

- ✅ Signal generation working
- ✅ Trading phase working  
- 🔄 CSV output 70% complete
- ❌ Reporting system needs fixes

## For Detailed Technical Info

Reference the full document: `docs/CPS_v4/How_signals_work.md`

---

**Why This Approach:**
- **Warp Drive:** Quick reference for AI agents to understand signals quickly
- **Local File:** Detailed technical documentation preserved with full diagrams and code examples
- **Best of Both:** Fast access + comprehensive detail when needed
