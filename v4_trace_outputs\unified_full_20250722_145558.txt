===== V4 UNIFIED PIPELINE =====
2025-07-22 14:56:03,906 - INFO - Successfully loaded settings for ema_allocation_model_v4.
2025-07-22 14:56:04,020 - INFO - Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
============================================================
UNIFIED PIPELINE v4 - STARTING
============================================================
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
INFO: Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
2025-07-22 14:56:04,021 - unified_pipeline - INFO - Logger initialized. Log file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\unified_pipeline_20250722_145604.log
2025-07-22 14:56:04,021 - INFO - Logger initialized. Log file: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\unified_pipeline_20250722_145604.log
2025-07-22 14:56:04,025 - unified_pipeline - INFO - [START] 14:56:04 - Unified Pipeline Starting
2025-07-22 14:56:04,025 - INFO - [START] 14:56:04 - Unified Pipeline Starting
[START] 14:56:04 - Unified Pipeline Starting
2025-07-22 14:56:04,025 - unified_pipeline - INFO - [SIGNAL_START] 14:56:04 - Beginning Signal Generation Phase
2025-07-22 14:56:04,025 - INFO - [SIGNAL_START] 14:56:04 - Beginning Signal Generation Phase
[SIGNAL_START] 14:56:04 - Beginning Signal Generation Phase
[MILESTONE] 14:56:04 - --- Starting Signal Generation Phase ---
[MILESTONE] 14:56:04 - 
Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[INFO] 14:56:04 - Settings loaded successfully.
[MILESTONE] 14:56:04 - 
Step 2: Loading market data...
2025-07-22 14:56:04,030 - INFO - [TickerData] Mode=Save, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_2020-01-01_2025-07-21.xlsx
2025-07-22 14:56:08,669 - INFO - [TickerData] Saved data to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_2020-01-01_2025-07-21.xlsx
2025-07-22 14:56:08,669 - WARNING - Price data is empty after filtering for dates: 2020-01-01 to 2025-07-21. Check data availability and date settings. Raw data had 0 rows.
Fetching data for SPY...
Calculating start date based on 10 years back from 2025-07-22: 2015-07-25
Downloaded 2511 rows of data for SPY from 2015-07-25 to 2025-07-22
Fetching data for SHV...
Calculating start date based on 10 years back from 2025-07-22: 2015-07-25
Downloaded 2511 rows of data for SHV from 2015-07-25 to 2025-07-22
Fetching data for EFA...
Calculating start date based on 10 years back from 2025-07-22: 2015-07-25
Downloaded 2511 rows of data for EFA from 2015-07-25 to 2025-07-22
Fetching data for TLT...
Calculating start date based on 10 years back from 2025-07-22: 2015-07-25
Downloaded 2511 rows of data for TLT from 2015-07-25 to 2025-07-22
Fetching data for PFF...
Calculating start date based on 10 years back from 2025-07-22: 2015-07-25
Downloaded 2511 rows of data for PFF from 2015-07-25 to 2025-07-22
[INFO] 14:56:08 - Price data loaded for 0 assets.
[MILESTONE] 14:56:08 - 
Step 3: Generating signals...
[INFO] 14:56:08 -    - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'execution_delay': 1, 'ema_short_period': {'optimize': False, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': False, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': False, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': False, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': False, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
2025-07-22 14:56:08,670 - INFO - Running EMA model with tracing enabled
[ERROR] 14:56:08 - Signal generation failed. Error: index -1 is out of bounds for axis 0 with size 0
Traceback (most recent call last):
  File "F:\AI_Library\my_quant_env\Lib\site-packages\pandas\core\indexes\range.py", line 1018, in __getitem__
    return self._range[new_key]
           ~~~~~~~~~~~^^^^^^^^^
IndexError: range object index out of range

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\Algo_signal_phase.py", line 82, in run_signal_phase
    signals_df = run_ema_model_with_tracing(
        price_data=price_data,
        **strategy_params
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_signal_bridge.py", line 33, in run_ema_model_with_tracing
    trace_results = ema_allocation_model_updated(
        price_data=price_data,
        trace_mode=True,
        **params
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 393, in ema_allocation_model_updated
    model_output = ema_allocation_model(
        price_data=price_data,
    ...<3 lines>...
        **params_for_model
    )
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\models\ema_allocation_model_v4.py", line 233, in ema_allocation_model
    logger.debug(f"EMA Model Inputs (Date: {price_data.index[-1]}) -- ST: {st_lookback}, MT: {mt_lookback}, LT: {lt_lookback}, min_w: {min_weight}, max_w: {max_weight}, system_top_n_arg: {system_top_n}")
                                            ~~~~~~~~~~~~~~~~^^^^
  File "F:\AI_Library\my_quant_env\Lib\site-packages\pandas\core\indexes\range.py", line 1020, in __getitem__
    raise IndexError(
        f"index {key} is out of bounds for axis 0 with size {len(self)}"
    ) from err
IndexError: index -1 is out of bounds for axis 0 with size 0
V4 unified pipeline completed with exit code 
