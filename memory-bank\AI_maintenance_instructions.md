# AI Maintenance Instructions

## PURPOSE

These instructions ensure project documentation stays current across AI sessions without manual intervention.

## MANDATORY SESSION WORKFLOW

### SESSION START (EVERY TIME)

1. **Read this file first** before any other work
2. **Review current status (PRIORITY ORDER):**
   - Read `memory-bank/core_project_tasks_priority.md`
   - Read `memory-bank/progress.md` 
   - Read `memory-bank/AI_Project_Summary.md` for context
   - Check latest session summary in `memory-bank/session_updates/`
3. **Identify session goals** based on current priorities
4. **Note any blockers** from previous sessions

### DURING SESSION

**Track these items continuously:**

- Tasks completed (even partial progress)
- New issues discovered
- Technical decisions made
- Changes to priorities or scope
- Blockers encountered
- Solutions found

### SESSION END (MANDATORY)

**Before ending any session, the AI MUST:**

1. **Update Progress (`memory-bank/progress.md`):**
   
   - Add completed work to "What Works" section
   - Update "What's Left" section 
   - Add new issues to "Known Issues" section
   - Update "Status" section with current phase

2. **Update Task Priorities (`memory-bank/core_project_tasks_priority.md`):**
   
   - <PERSON> completed tasks as ✅ DONE
   - Add new discovered tasks
   - Reprioritize based on new information
   - Update task details with new findings

3. **Create Session Summary:**
   
   - File: `memory-bank/session_updates/YYYY-MM-DD_session_summary.md`
   - Use template: `memory-bank/session_update_template.md`
   - Include: goals, accomplishments, issues, next steps

4. **Update Project Reference (if applicable):**
   
   - File: `memory-bank/Project_Reference.md`
   - Add new key information discovered
   - Update critical project details

5. **Update AI Project Summary (if applicable):**
   
   - File: `memory-bank/AI_Project_Summary.md`
   - Update module/function information
   - Revise data flow understanding

## TRIGGER PHRASES

When the user says these phrases, perform the specified actions immediately:

- **"Update project status"** → Update progress.md and task priorities
- **"End session summary"** → Perform all SESSION END actions above
- **"Plan update"** → Revise plans based on new information
- **"Document decision"** → Add entry to decisions_log.md

## CRITICAL RULES

1. **NEVER skip session end updates** - they maintain project continuity
2. **Be specific** - avoid vague entries like "worked on X"
3. **Preserve history** - don't delete previous entries, append new ones
4. **Date everything** - use YYYY-MM-DD format consistently
5. **Cross-reference** - link related tasks and issues

## FILE OWNERSHIP

**AI Maintains These Files:**

- `memory-bank/progress.md`
- `memory-bank/core_project_tasks_priority.md` 
- `memory-bank/session_updates/*.md`
- `memory-bank/decisions_log.md`
- `memory-bank/project_timeline.md`

**User Maintains These Files:**

- `Global_AI_Rules.md`
- `AIAgent_project_Guidelines.md`
- Architecture and design documents in `docs/`

## SUCCESS METRICS

**Session continuity:** New AI sessions can immediately understand current status
**Progress tracking:** Clear view of what's working vs. blocked
**Knowledge preservation:** Solutions and decisions don't get lost
**Efficiency:** Less time spent catching up, more time on actual work
