===== V4 DECOUPLED PIPELINE =====
2025-07-21 22:11:48,355 - INFO - [MILESTONE] Starting V4 Decoupled Pipeline
2025-07-21 22:11:48,355 - INFO - [MILESTONE] Starting Signal Generation Phase
2025-07-21 22:11:52,174 - INFO - Successfully loaded settings for ema_allocation_model_v4.
Python version: 3.13.1 (tags/v3.13.1:0671451, Dec  3 2024, 19:06:28) [MSC v.1942 64 bit (AMD64)]
Python paths:
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Custom Function Library
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template
  C:\Python313\python313.zip
  C:\Python313\DLLs
  C:\Python313\Lib
  C:\Python313
  F:\AI_Library\my_quant_env
  F:\AI_Library\my_quant_env\Lib\site-packages
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[MILESTONE] 22:11:52 - --- Starting Signal Generation Phase ---
[MILESTONE] 22:11:52 - 
Step 1: Loading settings from settings_CPS_v4.ini...
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[INFO] 22:11:52 - Settings loaded successfully.
[MILESTONE] 22:11:52 - 
Step 2: Loading market data...
2025-07-21 22:11:52,191 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
[INFO] 22:11:53 - Price data loaded for 5 assets.
[MILESTONE] 22:11:53 - 
Step 3: Generating signals...
[INFO] 22:11:53 -    - Using strategy: 'EMA_Crossover' with params: {'signal_algo': {'default': 'ema', 'picklist': ['Strategy_EMA', 'Strategy_SMA', 'Strategy_MACD', 'Strategy_RSI', 'Strategy_BB']}, 'min_weight': 0.0, 'max_weight': 1.0, 'system_top_n': {'optimize': False, 'default_value': 2, 'min_value': 1, 'max_value': 5, 'increment': 1}, 'execution_delay': 0, 'ema_short_period': {'optimize': True, 'default_value': 12, 'min_value': 5, 'max_value': 20, 'increment': 1}, 'ema_long_period': {'optimize': True, 'default_value': 26, 'min_value': 10, 'max_value': 100, 'increment': 1}, 'st_lookback': {'optimize': True, 'default_value': 15, 'min_value': 5, 'max_value': 30, 'increment': 1}, 'mt_lookback': {'optimize': True, 'default_value': 70, 'min_value': 30, 'max_value': 100, 'increment': 5}, 'lt_lookback': {'optimize': True, 'default_value': 100, 'min_value': 50, 'max_value': 200, 'increment': 10}}
2025-07-21 22:11:53,559 - INFO - Running EMA model with tracing enabled
2025-07-21 22:11:54,941 - INFO - Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
2025-07-21 22:11:54,963 - INFO - Saved Short-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_short_20250721_221154.csv
2025-07-21 22:11:54,976 - INFO - Saved Medium-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_medium_20250721_221154.csv
2025-07-21 22:11:54,989 - INFO - Saved Long-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_long_20250721_221154.csv
2025-07-21 22:11:55,032 - INFO - Saved Asset Ranking History (Matrix Format) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_20250721_221154.csv
2025-07-21 22:11:55,064 - INFO - Saved Combined EMA Averages History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\02_ema_average_history_20250721_221154.csv
2025-07-21 22:11:55,097 - INFO - Saved Raw Algorithm Calculation History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_history_20250721_221154.csv
2025-07-21 22:11:55,097 - INFO - Successfully saved detailed signal history data to CSV files including individual EMA files
2025-07-21 22:11:55,097 - INFO - Initialized signal_history DataFrame with shape (1370, 5)
2025-07-21 22:11:55,097 - INFO - Signal history date range: 2020-01-02 00:00:00 to 2025-06-13 00:00:00
2025-07-21 22:11:55,098 - INFO - Ranks DataFrame shape: (6850, 4)
2025-07-21 22:11:55,098 - INFO - Ranks date range: 0 to 6849
2025-07-21 22:11:55,098 - INFO - Initial ranks_df info:
2025-07-21 22:11:55,098 - INFO - Index type: <class 'pandas.core.indexes.range.RangeIndex'>
2025-07-21 22:11:55,098 - INFO - Columns: ['Date', 'Asset', 'EMAXAvg_Value', 'Rank_Ordinal']
2025-07-21 22:11:55,111 - INFO - Sample data:
        Date Asset  EMAXAvg_Value  Rank_Ordinal
0 2020-01-02   SPY            1.0             1
1 2020-01-02   SHV            1.0             2
2025-07-21 22:11:55,120 - INFO - Pivoted ranks DataFrame shape: (1370, 5)
2025-07-21 22:11:55,120 - INFO - Pivoted columns: ['EFA', 'PFF', 'SHV', 'SPY', 'TLT']
2025-07-21 22:11:55,121 - INFO - 2020-01-02 00:00:00: Allocated 60% to SPY, 40% to SHV
2025-07-21 22:11:55,148 - INFO - 2020-03-16 00:00:00: Allocated 60% to TLT, 40% to SHV
2025-07-21 22:11:55,173 - INFO - 2020-05-27 00:00:00: Allocated 60% to TLT, 40% to SHV
2025-07-21 22:11:55,197 - INFO - 2020-08-06 00:00:00: Allocated 60% to SPY, 40% to TLT
2025-07-21 22:11:55,222 - INFO - 2020-10-16 00:00:00: Allocated 60% to SPY, 40% to PFF
2025-07-21 22:11:55,249 - INFO - 2020-12-29 00:00:00: Allocated 60% to EFA, 40% to SPY
2025-07-21 22:11:55,275 - INFO - 2021-03-12 00:00:00: Allocated 60% to SPY, 40% to EFA
2025-07-21 22:11:55,302 - INFO - 2021-05-24 00:00:00: Allocated 60% to SPY, 40% to EFA
2025-07-21 22:11:55,329 - INFO - 2021-08-04 00:00:00: Allocated 60% to SPY, 40% to TLT
2025-07-21 22:11:55,356 - INFO - 2021-10-14 00:00:00: Allocated 60% to SPY, 40% to PFF
2025-07-21 22:11:55,381 - INFO - 2021-12-27 00:00:00: Allocated 60% to SPY, 40% to TLT
2025-07-21 22:11:55,408 - INFO - 2022-03-09 00:00:00: Allocated 60% to SHV, 40% to TLT
2025-07-21 22:11:55,436 - INFO - 2022-05-19 00:00:00: Allocated 60% to SHV, 40% to PFF
2025-07-21 22:11:55,463 - INFO - 2022-08-02 00:00:00: Allocated 60% to PFF, 40% to SHV
2025-07-21 22:11:55,490 - INFO - 2022-10-12 00:00:00: Allocated 60% to SHV, 40% to PFF
2025-07-21 22:11:55,516 - INFO - 2022-12-22 00:00:00: Allocated 60% to EFA, 40% to SHV
2025-07-21 22:11:55,544 - INFO - 2023-03-08 00:00:00: Allocated 60% to EFA, 40% to PFF
2025-07-21 22:11:55,570 - INFO - 2023-05-18 00:00:00: Allocated 60% to EFA, 40% to SPY
2025-07-21 22:11:55,597 - INFO - 2023-08-01 00:00:00: Allocated 60% to SPY, 40% to EFA
2025-07-21 22:11:55,624 - INFO - 2023-10-11 00:00:00: Allocated 60% to SHV, 40% to SPY
2025-07-21 22:11:55,651 - INFO - 2023-12-21 00:00:00: Allocated 60% to SPY, 40% to TLT
2025-07-21 22:11:55,679 - INFO - 2024-03-06 00:00:00: Allocated 60% to SPY, 40% to EFA
2025-07-21 22:11:55,706 - INFO - 2024-05-16 00:00:00: Allocated 60% to SPY, 40% to EFA
2025-07-21 22:11:55,734 - INFO - 2024-07-30 00:00:00: Allocated 60% to SPY, 40% to EFA
2025-07-21 22:11:55,761 - INFO - 2024-10-09 00:00:00: Allocated 60% to SPY, 40% to PFF
2025-07-21 22:11:55,787 - INFO - 2024-12-19 00:00:00: Allocated 60% to SPY, 40% to SHV
2025-07-21 22:11:55,815 - INFO - 2025-03-06 00:00:00: Allocated 60% to EFA, 40% to SHV
2025-07-21 22:11:55,843 - INFO - 2025-05-16 00:00:00: Allocated 60% to EFA, 40% to SPY
2025-07-21 22:11:55,855 - INFO - 2025-06-13 00:00:00: Allocated 60% to EFA, 40% to SPY
2025-07-21 22:11:55,855 - INFO - Signal generation complete. Made 1370 allocations.
2025-07-21 22:11:55,855 - INFO - Final signal_history shape: (1370, 5)
2025-07-21 22:11:55,856 - INFO - Non-zero allocations: 2740
INFO: Trace directory for this run: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs
TRACE: Saved Short-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_short_20250721_221154.csv
TRACE: Saved Medium-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_medium_20250721_221154.csv
TRACE: Saved Long-term EMA History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ema_long_20250721_221154.csv
TRACE: Saved Asset Ranking History (Matrix Format) to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\ranking_20250721_221154.csv
TRACE: Saved Combined EMA Averages History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\02_ema_average_history_20250721_221154.csv
TRACE: Saved Raw Algorithm Calculation History to S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\04_raw_algocalc_history_20250721_221154.csv
[INFO] 22:11:56 - Signals saved to parquet: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output.parquet
[INFO] 22:11:56 - Signals saved to CSV: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250721_221155.csv
[MILESTONE] 22:11:56 - Signals saved to:
  - S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output.parquet
  - S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output_20250721_221155.csv
[MILESTONE] 22:11:56 - Signal generation complete.
2025-07-21 22:11:56,067 - INFO - Signal phase completed successfully, signals saved to: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4_trace_outputs\signals_output.parquet
2025-07-21 22:11:56,067 - INFO - [MILESTONE] Starting Trading Phase
2025-07-21 22:11:56,091 - INFO - [PHASE_START] 22:11:56 - Starting Trading Phase
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
[PHASE_START] 22:11:56 - Starting Trading Phase
2025-07-21 22:11:56,095 - INFO - [TickerData] Mode=Read, file=S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\data\tickerdata_SPY_SHV_EFA_TLT_PFF_20200101_20250615.xlsx
2025-07-21 22:11:56,195 - INFO - Loaded price data with shape (1370, 5)
2025-07-21 22:11:56,195 - INFO - Date range: 2020-01-02 00:00:00 to 2025-06-13 00:00:00
2025-07-21 22:11:56,195 - INFO - Tickers: SPY, SHV, EFA, TLT, PFF
2025-07-21 22:11:56,195 - ERROR - Trading phase failed with error: 'str' object has no attribute 'shape'
Traceback (most recent call last):
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\run_v4_decoupled_pipeline.py", line 69, in run_decoupled_pipeline
    trading_results = trading_module.run_trading_phase(signals_file)
  File "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\run_trading_phase.py", line 144, in run_trading_phase
    logger.info(f"Using provided signals DataFrame with shape {signals.shape}")
                                                               ^^^^^^^^^^^^^
AttributeError: 'str' object has no attribute 'shape'
Loaded settings from: S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\v4\settings\settings_parameters_v4.ini
Found 11 parameter sections
Found 58 named lists for parameter references
V4 decoupled pipeline completed with exit code 
