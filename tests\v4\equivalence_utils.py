# tests/v4/equivalence_utils.py

"""
Utility functions for equivalence testing between CPS V4 pipeline outputs.

This module provides reusable components for detailed DataFrame comparisons
with comprehensive error reporting and configurable tolerance settings.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class EquivalenceError(AssertionError):
    """Custom exception for equivalence assertion failures with detailed context."""
    pass

class DataFrameComparator:
    """
    Comprehensive DataFrame comparison utility with detailed error reporting.
    
    Provides structured comparison of DataFrames with configurable tolerance
    and comprehensive reporting of differences.
    """
    
    def __init__(self, tolerance: float = 1e-6):
        """
        Initialize the comparator.
        
        Args:
            tolerance: Numeric comparison tolerance for floating-point values
        """
        self.tolerance = tolerance
        self.comparison_results = {}
        
    def compare_dataframes(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                          name1: str = "df1", name2: str = "df2", 
                          file_context: str = "") -> bool:
        """
        Perform comprehensive DataFrame comparison.
        
        Args:
            df1: First DataFrame to compare
            df2: Second DataFrame to compare  
            name1: Name/label for first DataFrame (for error reporting)
            name2: Name/label for second DataFrame (for error reporting)
            file_context: Additional context about the files being compared
            
        Returns:
            bool: True if DataFrames are equivalent
            
        Raises:
            EquivalenceError: If DataFrames are not equivalent with detailed error info
        """
        context = f"[{file_context}]" if file_context else ""
        
        # Step 1: Shape comparison
        self._compare_shapes(df1, df2, name1, name2, context)
        
        # Step 2: Column comparison  
        self._compare_columns(df1, df2, name1, name2, context)
        
        # Step 3: Index comparison
        self._compare_indices(df1, df2, name1, name2, context)
        
        # Step 4: Data comparison (numeric vs non-numeric)
        self._compare_data(df1, df2, name1, name2, context)
        
        logger.info(f"{context} DataFrame equivalence verified ✓")
        return True
        
    def _compare_shapes(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                       name1: str, name2: str, context: str) -> None:
        """Compare DataFrame shapes."""
        if df1.shape != df2.shape:
            raise EquivalenceError(
                f"{context} Shape mismatch: {name1} {df1.shape} vs {name2} {df2.shape}"
            )
        logger.debug(f"{context} Shape assertion passed: {df1.shape}")
        
    def _compare_columns(self, df1: pd.DataFrame, df2: pd.DataFrame,
                        name1: str, name2: str, context: str) -> None:
        """Compare column names and order."""
        cols1, cols2 = list(df1.columns), list(df2.columns)
        if cols1 != cols2:
            raise EquivalenceError(
                f"{context} Column mismatch: {name1} {cols1} vs {name2} {cols2}"
            )
        logger.debug(f"{context} Column assertion passed: {len(cols1)} columns")
        
    def _compare_indices(self, df1: pd.DataFrame, df2: pd.DataFrame,
                        name1: str, name2: str, context: str) -> None:
        """Compare DataFrame indices."""
        if not df1.index.equals(df2.index):
            # Provide detailed index mismatch information
            if len(df1.index) != len(df2.index):
                raise EquivalenceError(
                    f"{context} Index length mismatch: "
                    f"{name1} has {len(df1.index)} rows vs {name2} has {len(df2.index)} rows"
                )
            
            # Check for first differing index value
            mismatch_mask = df1.index != df2.index
            if hasattr(mismatch_mask, 'any') and mismatch_mask.any():
                first_mismatch = mismatch_mask.idxmax()
                raise EquivalenceError(
                    f"{context} Index value mismatch at position {first_mismatch}: "
                    f"{name1} has '{df1.index[first_mismatch]}' vs "
                    f"{name2} has '{df2.index[first_mismatch]}'"
                )
            else:
                raise EquivalenceError(f"{context} Index mismatch between {name1} and {name2}")
        
        logger.debug(f"{context} Index assertion passed")
    
    def _compare_data(self, df1: pd.DataFrame, df2: pd.DataFrame,
                     name1: str, name2: str, context: str) -> None:
        """Compare DataFrame data with appropriate methods for numeric/non-numeric."""
        # Separate numeric and non-numeric columns
        numeric_cols = df1.select_dtypes(include=[np.number]).columns
        non_numeric_cols = df1.select_dtypes(exclude=[np.number]).columns
        
        # Compare numeric columns with tolerance
        if len(numeric_cols) > 0:
            self._compare_numeric_data(df1, df2, numeric_cols, name1, name2, context)
            
        # Compare non-numeric columns with exact equality
        if len(non_numeric_cols) > 0:
            self._compare_non_numeric_data(df1, df2, non_numeric_cols, name1, name2, context)
    
    def _compare_numeric_data(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                             numeric_cols: pd.Index, name1: str, name2: str, context: str) -> None:
        """Compare numeric columns with tolerance."""
        logger.debug(f"{context} Checking {len(numeric_cols)} numeric columns")
        
        for col in numeric_cols:
            values1 = df1[col].values
            values2 = df2[col].values
            
            # Handle NaN values
            mask1 = np.isfinite(values1)
            mask2 = np.isfinite(values2)
            
            # Check NaN pattern consistency
            if not np.array_equal(mask1, mask2):
                nan_count1 = np.sum(~mask1)
                nan_count2 = np.sum(~mask2)
                raise EquivalenceError(
                    f"{context} NaN pattern mismatch in column '{col}': "
                    f"{name1} has {nan_count1} NaNs vs {name2} has {nan_count2} NaNs"
                )
            
            # Compare finite values with tolerance
            if np.any(mask1):  # Only if there are finite values
                finite1 = values1[mask1]
                finite2 = values2[mask2]
                
                if not np.allclose(finite1, finite2, atol=self.tolerance, rtol=self.tolerance):
                    # Find first significant difference
                    diff_mask = ~np.isclose(finite1, finite2, atol=self.tolerance, rtol=self.tolerance)
                    if np.any(diff_mask):
                        first_diff_idx = np.where(diff_mask)[0][0]
                        val1 = finite1[first_diff_idx]
                        val2 = finite2[first_diff_idx]
                        abs_diff = abs(val1 - val2)
                        
                        raise EquivalenceError(
                            f"{context} Numeric mismatch in column '{col}' at position {first_diff_idx}: "
                            f"{name1}={val1} vs {name2}={val2} (diff={abs_diff}, tolerance={self.tolerance})"
                        )
        
        logger.debug(f"{context} Numeric data comparison passed")
    
    def _compare_non_numeric_data(self, df1: pd.DataFrame, df2: pd.DataFrame,
                                 non_numeric_cols: pd.Index, name1: str, name2: str, context: str) -> None:
        """Compare non-numeric columns with exact equality.""" 
        logger.debug(f"{context} Checking {len(non_numeric_cols)} non-numeric columns")
        
        for col in non_numeric_cols:
            if not df1[col].equals(df2[col]):
                # Find first mismatch for detailed reporting
                mismatch_mask = df1[col] != df2[col]
                if mismatch_mask.any():
                    first_mismatch_idx = mismatch_mask.idxmax() 
                    val1 = df1.loc[first_mismatch_idx, col]
                    val2 = df2.loc[first_mismatch_idx, col]
                    
                    raise EquivalenceError(
                        f"{context} Non-numeric mismatch in column '{col}' at index {first_mismatch_idx}: "
                        f"{name1}='{val1}' vs {name2}='{val2}'"
                    )
        
        logger.debug(f"{context} Non-numeric data comparison passed")


def compare_output_files(unified_files: Dict[str, Path], decoupled_files: Dict[str, Path],
                        tolerance: float = 1e-6) -> Dict[str, bool]:
    """
    Compare corresponding output files from unified and decoupled pipeline runs.
    
    Args:
        unified_files: Dictionary mapping file types to unified pipeline output files
        decoupled_files: Dictionary mapping file types to decoupled pipeline output files
        tolerance: Numeric comparison tolerance
        
    Returns:
        Dict[str, bool]: Results of comparisons for each file type
        
    Raises:
        EquivalenceError: If any file comparison fails
    """
    comparator = DataFrameComparator(tolerance=tolerance)
    results = {}
    
    # File type mappings and loaders
    file_loaders = {
        'parquet': pd.read_parquet,
        'csv': pd.read_csv
    }
    
    for file_type in unified_files.keys():
        unified_file = unified_files.get(file_type)
        decoupled_file = decoupled_files.get(file_type)
        
        if not (unified_file and decoupled_file and 
                unified_file.exists() and decoupled_file.exists()):
            logger.warning(f"Skipping {file_type} - missing files")
            results[file_type] = False
            continue
            
        try:
            # Determine file extension and appropriate loader
            if unified_file.suffix.lower() == '.parquet':
                loader = file_loaders['parquet']
            elif unified_file.suffix.lower() == '.csv':
                loader = file_loaders['csv']
            else:
                logger.error(f"Unsupported file type: {unified_file.suffix}")
                results[file_type] = False
                continue
            
            # Load DataFrames with special handling for trade log CSV files
            if unified_file.suffix.lower() == '.csv' and 'trade_log' in unified_file.name.lower():
                # Trade log CSV files have an unnamed index column, so we read with index_col=0
                # and parse 'date' column as datetime
                df_unified = pd.read_csv(unified_file, index_col=0, parse_dates=['date'])
                df_decoupled = pd.read_csv(decoupled_file, index_col=0, parse_dates=['date'])
            else:
                df_unified = loader(unified_file)
                df_decoupled = loader(decoupled_file)
            
            # Perform comparison
            comparator.compare_dataframes(
                df_unified, df_decoupled,
                name1="unified", name2="decoupled", 
                file_context=unified_file.name
            )
            
            results[file_type] = True
            logger.info(f"✓ {file_type} comparison passed")
            
        except Exception as e:
            logger.error(f"✗ {file_type} comparison failed: {e}")
            results[file_type] = False
            raise
    
    return results


def validate_pipeline_outputs(output_dir: Path, expected_files: List[str]) -> Dict[str, bool]:
    """
    Validate that expected output files were created by a pipeline run.
    
    Args:
        output_dir: Directory containing pipeline outputs
        expected_files: List of expected output file patterns
        
    Returns:
        Dict[str, bool]: Validation results for each expected file
    """
    validation_results = {}
    
    for file_pattern in expected_files:
        matching_files = list(output_dir.glob(file_pattern))
        validation_results[file_pattern] = len(matching_files) > 0
        
        if matching_files:
            logger.info(f"✓ Found {len(matching_files)} file(s) matching '{file_pattern}'")
        else:
            logger.warning(f"✗ No files found matching '{file_pattern}'")
    
    return validation_results


def create_equivalence_report(comparison_results: Dict[str, bool], 
                             unified_duration: float, decoupled_duration: float,
                             output_file: Optional[Path] = None) -> str:
    """
    Generate a summary report of equivalence testing results.
    
    Args:
        comparison_results: Results from file comparisons
        unified_duration: Duration of unified pipeline execution (seconds)
        decoupled_duration: Duration of decoupled pipeline execution (seconds)
        output_file: Optional file path to save the report
        
    Returns:
        str: Formatted equivalence test report
    """
    passed_count = sum(comparison_results.values())
    total_count = len(comparison_results)
    success_rate = (passed_count / total_count * 100) if total_count > 0 else 0
    
    report = []
    report.append("=" * 60)
    report.append("EQUIVALENCE TEST REPORT")
    report.append("=" * 60)
    report.append("")
    
    # Execution Summary
    report.append("EXECUTION SUMMARY:")
    report.append(f"  Unified Pipeline Duration:   {unified_duration:.2f} seconds")
    report.append(f"  Decoupled Pipeline Duration: {decoupled_duration:.2f} seconds")
    speedup_factor = decoupled_duration / unified_duration if unified_duration > 0 else 0
    report.append(f"  Performance Ratio:           {speedup_factor:.2f}x")
    report.append("")
    
    # Comparison Results
    report.append("COMPARISON RESULTS:")
    for file_type, passed in comparison_results.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        report.append(f"  {file_type:<25} {status}")
    report.append("")
    
    # Summary
    report.append("OVERALL SUMMARY:")
    report.append(f"  Files Compared:    {total_count}")
    report.append(f"  Comparisons Passed: {passed_count}")
    report.append(f"  Success Rate:      {success_rate:.1f}%")
    
    if success_rate == 100:
        report.append("  Result: ALL EQUIVALENCE TESTS PASSED ✓")
    else:
        report.append(f"  Result: {total_count - passed_count} EQUIVALENCE TESTS FAILED ✗")
    
    report.append("=" * 60)
    
    report_text = "\n".join(report)
    
    # Save to file if requested
    if output_file:
        output_file.write_text(report_text)
        logger.info(f"Equivalence report saved to {output_file}")
    
    return report_text
