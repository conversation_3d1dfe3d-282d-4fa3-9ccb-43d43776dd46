@echo off
REM Parameter GUI Refactor Test Batch File
REM This script activates the Python environment and runs the test script

echo Starting Parameter GUI Refactor Test...
echo Timestamp: %date% %time%

REM Path to virtual environment
set ENV_PATH=F:\AI_Library\my_quant_env

REM Activate the virtual environment
call "%ENV_PATH%\Scripts\activate.bat"

REM Change to the test directory
cd /d "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\special_test"

REM Run the test script
echo Running test script...
python test_parameter_gui_refactor.py

REM Check the exit code
if %ERRORLEVEL% EQU 0 (
    echo Test completed successfully.
) else (
    echo Test failed with error code %ERRORLEVEL%.
)

REM Show log location
echo Log files are available in:
echo S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\logs

REM Deactivate the virtual environment
call deactivate

echo Test run complete.
pause
