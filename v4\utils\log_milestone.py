#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/utils/log_milestone.py

Milestone Logging Helper for CPS v4 System

This module provides standardized milestone logging functionality for the CPS v4 
backtest and trading system. It ensures consistent formatting, timing, and
output across all phases of the pipeline.

Author: AI Assistant
Date: 2025-01-18
"""

import logging
import sys
from datetime import datetime
from typing import Optional, Dict, Any, Union
from pathlib import Path

# Milestone type constants
MILESTONE_START = "START"
MILESTONE_COMPLETE = "COMPLETE"
MILESTONE_ERROR = "ERROR"
MILESTONE_WARNING = "WARNING"
MILESTONE_PHASE_START = "PHASE_START"
MILESTONE_PHASE_COMPLETE = "PHASE_COMPLETE"
MILESTONE_CHECKPOINT = "CHECKPOINT"

# Default milestone types for common operations
DEFAULT_MILESTONES = {
    "signal_start": "SIGNAL_START",
    "signal_complete": "SIGNAL_COMPLETE", 
    "trading_start": "TRADING_START",
    "trading_complete": "TRADING_COMPLETE",
    "pipeline_start": "PIPELINE_START",
    "pipeline_complete": "PIPELINE_COMPLETE",
    "data_load": "DATA_LOAD",
    "validation": "VALIDATION",
    "save_output": "SAVE_OUTPUT"
}


def log_milestone(
    logger: logging.Logger,
    message: str,
    milestone_type: str = MILESTONE_CHECKPOINT,
    include_timestamp: bool = True,
    console_output: bool = True,
    flush_output: bool = True,
    extra_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log a milestone with standardized formatting.
    
    Args:
        logger: Logger instance to use for output
        message: Main milestone message
        milestone_type: Type of milestone (use constants above)
        include_timestamp: Whether to include timestamp in message
        console_output: Whether to also print to console
        flush_output: Whether to flush stdout after console output
        extra_data: Optional dictionary of additional data to include
    """
    # Format timestamp if requested
    if include_timestamp:
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{milestone_type}] {timestamp} - {message}"
    else:
        formatted_message = f"[{milestone_type}] {message}"
    
    # Add extra data if provided
    if extra_data:
        data_str = ", ".join(f"{k}={v}" for k, v in extra_data.items())
        formatted_message += f" ({data_str})"
    
    # Log the message
    if milestone_type == MILESTONE_ERROR:
        logger.error(formatted_message)
    elif milestone_type == MILESTONE_WARNING:
        logger.warning(formatted_message)
    else:
        logger.info(formatted_message)
    
    # Console output if requested
    if console_output:
        print(formatted_message)
        if flush_output:
            sys.stdout.flush()


def create_milestone_logger(
    name: str,
    log_level: str = "INFO",
    log_file: Optional[Union[str, Path]] = None
) -> logging.Logger:
    """
    Create a logger specifically configured for milestone logging.
    
    Args:
        name: Logger name
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional path to log file
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler if specified
    if log_file:
        file_handler = logging.FileHandler(str(log_file))
        file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def log_phase_start(logger: logging.Logger, phase_name: str, **kwargs) -> None:
    """Log the start of a major phase."""
    log_milestone(
        logger, 
        f"Starting {phase_name} Phase", 
        milestone_type=MILESTONE_PHASE_START,
        **kwargs
    )


def log_phase_complete(logger: logging.Logger, phase_name: str, **kwargs) -> None:
    """Log the completion of a major phase."""
    log_milestone(
        logger, 
        f"{phase_name} Phase Complete", 
        milestone_type=MILESTONE_PHASE_COMPLETE,
        **kwargs
    )


def log_error_milestone(logger: logging.Logger, error_message: str, **kwargs) -> None:
    """Log an error milestone."""
    log_milestone(
        logger,
        f"Error: {error_message}",
        milestone_type=MILESTONE_ERROR,
        **kwargs
    )


def log_data_milestone(
    logger: logging.Logger,
    data_description: str,
    data_shape: Optional[tuple] = None,
    date_range: Optional[tuple] = None,
    **kwargs
) -> None:
    """
    Log a data-related milestone with common data attributes.
    
    Args:
        logger: Logger instance
        data_description: Description of the data
        data_shape: Optional shape tuple (rows, cols)
        date_range: Optional date range tuple (start, end)
        **kwargs: Additional arguments for log_milestone
    """
    extra_data = {}
    if data_shape:
        extra_data['shape'] = f"{data_shape[0]}x{data_shape[1]}"
    if date_range:
        extra_data['date_range'] = f"{date_range[0]} to {date_range[1]}"
    
    log_milestone(
        logger,
        data_description,
        milestone_type="DATA_MILESTONE",
        extra_data=extra_data,
        **kwargs
    )
