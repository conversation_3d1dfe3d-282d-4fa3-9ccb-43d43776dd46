# CPS V4 Project Setup

## Project Directory
**Working Directory**: `S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template`

## Quick Setup Commands for New Sessions
```powershell
# Navigate to project directory
cd "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"

# Verify project structure
Get-ChildItem -Name | Select-Object -First 10

# Check V4 structure
Get-ChildItem -Path "v4" -Directory -Name
```

## Key Project Files
- **Main Entry Point**: `main_v4_production_run.py`
- **Settings**: `v4/settings/settings_CPS_v4.py` 
- **Codebase Map**: `memory-bank/codebase_map.md`
- **Documentation**: `memory-bank/` directory

## Virtual Environment
- **Environment Path**: `F:\AI_Library\my_quant_env` (Windows 11 environment variable: QUANT_ENV_PATH)
- **Activation**: Use batch files in project root (e.g., `run_main_v4_prod2.bat`)

## Warp Session Persistence
To help <PERSON>p retain this knowledge across sessions:

1. **Project Directory**: Always start with the `cd` command above
2. **Context Files**: Reference `memory-bank/codebase_map.md` for complete architecture
3. **Working Files**: Check `Status.txt` and recent files in project root
4. **Environment**: Batch files handle environment setup automatically

## Architecture Status (2025-06-28)
- ✅ **Unified Entry Point**: `main_v4_production_run.py` fully documented
- ✅ **Decoupled Scripts**: `run_signal_phase.py`, `run_trading_phase.py` mapped
- ✅ **Reporting Modules**: `v4_reporting/*` comprehensively catalogued  
- ✅ **Engine Functions**: `v4/engine/*` completely documented
- ✅ **Settings Pattern**: `settings_CPS_v4.py` loading pattern documented
- ✅ **Data Flow**: Signal → Trading → Reporting pipeline mapped
- ✅ **Configuration Management**: CPS V4 parameter types and patterns documented

## Critical Files Updated
- `memory-bank/codebase_map.md` - **FULLY UPDATED** with complete V4 architecture
- Contains all modules, functions, methods, data flow patterns, and configuration management
- Includes GUI components, testing framework, utilities, and integration points
- Documents parameter types (SimpleA, SimpleN, ComplexN, AlphaList)
- Maps file naming conventions and performance monitoring patterns

## Next Session Quick Start
1. Run: `cd "S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template"`
2. Review: `memory-bank/codebase_map.md` for complete architecture
3. Check: `Status.txt` for current project status
4. Verify: V4 components with `Get-ChildItem -Path "v4" -Recurse -Name "*.py" | wc -l`
