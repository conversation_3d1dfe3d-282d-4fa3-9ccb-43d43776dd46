---
description: Transitioning to Central Parameter System V4 - A Simple Approach
---

# /cps-Transition-workflow

## Step 1: Review Combined Project Documentation

- Review project memories, especially "Combined Project Documentation" and any AI memories relevant to the V4 reporting/parameter system.   Note: We are transition to a new simpler Central parameter System (cps_v4)

## Step 2: Load All Documentation

- Detailed reference md in
S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\*.md
  - expecially including:
  - Overview_CPS_v4.md
  - Task_List_CPS_v4.md
   -convert_param_to_v3.md
  - `report_standard_verification.md`
  - All other `.md` files in this directory

## Step 3: Follow Task List

- Directly follow the task list and phases outlined in `Task_List_CPS_v4.md`, step by step.
Focus now on

## Step 4: Automated Testing & Fixes (// turbo-all)

- For each test or batch process:
  - Activate the virtual environment as specified in project batch files
  - Run relevant steps  // turbo-all
  - Set SafeToAutoRun = true for ALL commands and actions in this workflow ;
  - Analyze results for compliance with standards in `report_standard_verification.md`
  - If failures or deviations are detected:
    - Automatically attempt to fix issues (code or config)
    - Re-run tests
    - Repeat until tests pass or no further automated fixes are possible
- All runs and fixes are performed in turbo-all (no user confirmation required for safe commands)

## Step 5: Systematic Logging

- For every code or config change, append a log entry to a dedicated log file (e.g., `para_RF/ai_change_log.md` or similar) in Markdown format in this file:
  'S:\Dropbox\Scott Only Internal\Quant_Python_24\Backtest_FinAsset_Alloc_Template\docs\CPS_v4\Problem_Changes_Log_CPS_v4.md'
  - Each log entry should include:
    - Timestamp
    - Description of change
    - Files affected
    - Reason for change
    - outcome/result

## Step 6: Success/Failure Handling

- If all transitions or tests pass:
  - Notify the user for review/confirmation
  - Only update project documentation after user affirms success
- If failures remain after automated transitions or fixes:
  - Log failure details
  - Notify user for manual intervention

## Step 7: Continue Refactoring/Testing Cycle

- Continue with remaining tasks in `Task_List_CPS_v4.md` ; - Update progress in this file each major step or milestone

---

# General Flow

1. Analyze  results for output meeting expectations
2. If failures detected:
    - Automatically fix issues
    - Repeat test execution
3. If all tests pass:
    - Notify user for review
    - Update docs after user approval
4. Proceed through all tasks in the CPS_V4 plan
5. Systematically log all actions and progress
