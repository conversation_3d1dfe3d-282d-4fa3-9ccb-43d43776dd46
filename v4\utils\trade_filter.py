"""
v4/utils/trade_filter.py
Trade Filter Module for enforcing trading constraints based on current allocations.

Includes utilities for fetching portfolio allocations, computing differences, and filtering trades.
Business calendar check using price-data availability as sole truth.
"""

import pandas as pd
import logging
from datetime import date, datetime, timedelta
from typing import Dict, Optional
from ..engine.portfolio_v4 import Portfolio
from ..settings.settings_CPS_v4 import load_settings
from .smart_logging import create_smart_logger

# Configure smart logging
logger = create_smart_logger(__name__)

# Load settings once at module level
settings = load_settings()
DEVIATION_THRESHOLD = settings.get('backtest', {}).get('deviation_threshold', 0.02)


def fetch_current_allocation(portfolio: Portfolio) -> pd.Series:
    """
    Fetch the current allocation from the portfolio.

    Args:
        portfolio (Portfolio): The current portfolio object

    Returns:
        pd.Series: Current allocations as a percentage of total portfolio value
    """
    total_value = portfolio.get_total_value()
    if total_value == 0:
        return pd.Series()

    weights = portfolio.get_weights()
    return pd.Series(weights)


def compute_absolute_diff(current: pd.Series, proposed: pd.Series) -> pd.Series:
    """
    Compute the absolute percentage point difference between current and proposed allocations.
    This measures deviation as percentage of total portfolio equity.

    Args:
        current (pd.Series): Current allocations (as % of total portfolio)
        proposed (pd.Series): Proposed allocations (as % of total portfolio)

    Returns:
        pd.Series: Absolute percentage point differences
    """
    return (proposed - current).abs()


def is_business_day(check_date: date, price_data: pd.DataFrame) -> bool:
    """
    Check if a given date is a business day based on price data availability.
    Uses price-data availability as the sole truth for business calendar.
    
    Args:
        check_date (date): Date to check
        price_data (pd.DataFrame): Historical price data with datetime index
        
    Returns:
        bool: True if the date has price data (is a business day), False otherwise
    """
    if price_data.empty:
        logger.warning("Price data is empty, cannot determine business day")
        return False
        
    # Convert check_date to pandas timestamp for comparison
    check_timestamp = pd.Timestamp(check_date)
    
    # Check if the date exists in the price data index
    return check_timestamp in price_data.index


def filter_trades(current_allocation: pd.Series, proposed_allocation: pd.Series, 
                 portfolio: Optional[Portfolio] = None, price_data: Optional[pd.DataFrame] = None,
                 trade_date: Optional[date] = None) -> Dict[str, pd.Series]:
    """
    Filter trades that exceed the allowed deviation threshold and enforce business calendar.
    
    Args:
        current_allocation (pd.Series): Current asset allocations
        proposed_allocation (pd.Series): Proposed asset allocations
        portfolio (Portfolio, optional): Portfolio object for additional context
        price_data (pd.DataFrame, optional): Price data for business calendar check
        trade_date (date, optional): Date of the proposed trade
        
    Returns:
        Dict[str, pd.Series]: Dictionary with 'allowed' and 'filtered' allocations
    """
    # Smart logging: detailed logs only for recent dates
    logger.debug(f"\n=== TRADE FILTER DEBUG for {trade_date} ===", target_date=trade_date)
    logger.debug(f"Filtering trades with deviation threshold: {DEVIATION_THRESHOLD:.1%}", target_date=trade_date)
    logger.debug(f"Current allocation: {current_allocation.to_dict()}", target_date=trade_date)
    logger.debug(f"Proposed allocation: {proposed_allocation.to_dict()}", target_date=trade_date)
    
    # Business calendar check
    if trade_date and price_data is not None:
        if not is_business_day(trade_date, price_data):
            logger.warning(f"Trade date {trade_date} is not a business day based on price data availability")
            return {
                'allowed': pd.Series(dtype=float),
                'filtered': proposed_allocation.copy(),
                'reason': 'Non-business day'
            }
    
    # Align indices to ensure both series have the same assets
    all_assets = current_allocation.index.union(proposed_allocation.index)
    current_aligned = current_allocation.reindex(all_assets, fill_value=0.0)
    proposed_aligned = proposed_allocation.reindex(all_assets, fill_value=0.0)
    
    logger.debug(f"Current aligned: {current_aligned.to_dict()}", target_date=trade_date)
    logger.debug(f"Proposed aligned: {proposed_aligned.to_dict()}", target_date=trade_date)
    
    # Calculate absolute percentage point differences (as % of total equity)
    absolute_diff = compute_absolute_diff(current_aligned, proposed_aligned)
    
    logger.debug(f"Absolute differences: {absolute_diff.to_dict()}", target_date=trade_date)
    
    # Check if ANY asset deviation exceeds threshold
    exceeds_mask = absolute_diff > DEVIATION_THRESHOLD
    any_exceeds_threshold = exceeds_mask.any()
    
    logger.debug(f"Assets exceeding threshold: {exceeds_mask[exceeds_mask].to_dict()}", target_date=trade_date)
    logger.debug(f"Any exceeds threshold: {any_exceeds_threshold}", target_date=trade_date)
    
    if any_exceeds_threshold:
        # If any asset exceeds threshold, allow ALL proposed trades (full rebalance)
        allowed_trades = proposed_aligned.copy()
        filtered_trades = pd.Series(dtype=float)
        
        # Always log rebalance decisions (important events)
        logger.rebalance_event(f"[{trade_date}] Threshold exceeded - executing full rebalance", target_date=trade_date)
        
        logger.debug(f"Assets exceeding {DEVIATION_THRESHOLD:.1%} threshold:", target_date=trade_date)
        for asset in absolute_diff[absolute_diff > DEVIATION_THRESHOLD].index:
            current_weight = current_aligned.get(asset, 0.0)
            proposed_weight = proposed_aligned[asset]
            deviation = absolute_diff.get(asset, 0.0)
            logger.debug(f"  {asset}: {current_weight:.2%} -> {proposed_weight:.2%} (deviation: {deviation:.1%})", target_date=trade_date)
    else:
        # If no asset exceeds threshold, filter ALL trades (no rebalance)
        allowed_trades = pd.Series(dtype=float)
        filtered_trades = proposed_aligned.copy()
        
        # Only log filtered trades for recent dates (reduces noise)
        logger.debug(f"[{trade_date}] NO REBALANCE: No assets exceed {DEVIATION_THRESHOLD:.1%} threshold", target_date=trade_date)
    
    logger.debug(f"Allowed trades count: {len(allowed_trades)}", target_date=trade_date)
    logger.debug(f"Filtered trades count: {len(filtered_trades)}", target_date=trade_date)
    logger.debug(f"=== END TRADE FILTER DEBUG ===", target_date=trade_date)
    
    return {
        'allowed': allowed_trades,
        'filtered': filtered_trades,
        'reason': 'Deviation threshold exceeded' if len(filtered_trades) > 0 else None
    }

