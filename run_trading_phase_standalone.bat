@echo off
REM ============================================
REM Script: run_trading_phase_standalone.bat
REM Description: Super-stable launcher for V4 trading phase with full + filtered logs
REM ============================================

SETLOCAL

REM --- Paths ---
SET "PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe"
SET "SCRIPT_DIR=%~dp0"
SET "TRADING_SCRIPT=%SCRIPT_DIR%v4\run_trading_phase.py"
SET "OUTPUT_DIR=%SCRIPT_DIR%v4_trace_outputs"
SET "SIGNAL_FILE=%OUTPUT_DIR%\signals_output_20250622_192844.csv"

REM --- If signal file passed as parameter, use that instead ---
IF NOT "%~1"=="" (
    SET "SIGNAL_FILE=%~1"
    echo Using provided signal file: %SIGNAL_FILE%
)

REM --- Ensure output directory exists ---
IF NOT EXIST "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)

REM --- Timestamp (YYYYMMDD_HHMMSS) ---
SET "TIMESTAMP=%DATE:~10,4%%DATE:~4,2%%DATE:~7,2%_%TIME:~0,2%%TIME:~3,2%%TIME:~6,2%"
SET "TIMESTAMP=%TIMESTAMP: =0%"

REM --- Log files ---
SET "FULL_LOG=%OUTPUT_DIR%\trading_full_%TIMESTAMP%.txt"
SET "FILTERED_LOG=%OUTPUT_DIR%\trading_filtered_%TIMESTAMP%.txt"

echo [%TIME%] Running V4 trading phase (full log: "%FULL_LOG%" )

REM --- Run Trading Phase ---
echo [%TIME%] Starting V4 trading phase...
(
    echo ===== V4 TRADING PHASE =====
    "%PYTHON_EXE%" "%TRADING_SCRIPT%" "%SIGNAL_FILE%"
    SET "EXIT_CODE=%ERRORLEVEL%"
    echo V4 trading phase completed with exit code %EXIT_CODE%
) > "%FULL_LOG%" 2>&1

REM --- Create filtered log ---
echo ===== FILTERED OUTPUT ===== > "%FILTERED_LOG%"
echo Run Time: %DATE% %TIME% >> "%FILTERED_LOG%"
echo. >> "%FILTERED_LOG%"
echo ===== MILESTONES ===== >> "%FILTERED_LOG%"
findstr /C:"[MILESTONE]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
echo. >> "%FILTERED_LOG%"
echo ===== WARNINGS ^& ERRORS ===== >> "%FILTERED_LOG%"
findstr /C:"[ERROR]" /C:"[WARNING]" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul
findstr /i "error warning exception traceback failed fatal" "%FULL_LOG%" >> "%FILTERED_LOG%" 2>nul

echo [%TIME%] V4 trading phase finished with exit code %EXIT_CODE%
IF %EXIT_CODE% EQU 0 (
    echo Trading phase completed successfully.
) ELSE (
    echo Trading phase encountered errors. Check the logs for details.
)
echo Full log: "%FULL_LOG%"
echo Filtered log: "%FILTERED_LOG%"

ENDLOCAL
exit /b %EXIT_CODE%
