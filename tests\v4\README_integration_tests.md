# V4 Pipeline Integration Tests

## Overview

This directory contains end-to-end integration tests for the V4 backtesting pipeline. These tests validate the full signal→trading pipeline flow using production code and real historical data.

## Test Structure

### Files

- `test_integration_pipeline.py` - Main integration test suite
- `production_modules/` - Copied production modules for testing
- `run_integration_tests.bat` - Batch file to execute tests
- `README_integration_tests.md` - This documentation

### Production Modules Copied

The following production modules are copied to `production_modules/` for testing:

- `run_v4_pipeline.py` - Main pipeline orchestrator
- `run_signal_phase.py` - Signal generation phase
- `run_trading_phase.py` - Trading execution phase

## Test Coverage

### 1. Full Pipeline Execution (`test_full_signal_trading_pipeline`)

**Purpose**: Validates that the complete signal→trading pipeline executes successfully.

**Assertions**:
- Pipeline returns results
- Results contain expected structure (performance, trade_log, allocation_history)
- No fatal errors during execution

### 2. Trade Deviation Threshold (`test_trade_deviation_threshold`)

**Purpose**: Ensures trades are only executed when allocation deviations exceed 2%.

**Test Cases**:
- Small deviation (≤2%): Should be filtered and not executed
- Large deviation (>2%): Should be allowed and executed

**Assertions**:
- Filter correctly identifies small vs. large deviations
- Only significant allocation changes trigger trades

### 3. Non-Price Data Days (`test_non_price_data_days_skipped`)

**Purpose**: Verifies that weekends/holidays without price data are properly skipped.

**Test Cases**:
- Weekend dates: Should be identified as non-business days
- Business dates: Should be identified as valid trading days
- Trading attempts on non-business days: Should be rejected

**Assertions**:
- Business day detection works correctly
- No trades executed on non-price data days

### 4. Real Data Integration (`test_real_data_integration`)

**Purpose**: Validates pipeline using actual historical data files from `v4_trace_outputs/`.

**Test Cases**:
- Signal file structure validation
- Trade log structure validation
- Data consistency checks

**Assertions**:
- Signal weights sum to approximately 1.0
- Trade logs contain required columns
- Files are not empty and have expected structure

## Key Features Tested

### 1. Production Flow Compliance

- **No Mocks**: Tests use actual production modules and real data
- **Configuration-Driven**: Uses actual settings from `settings_parameters_v4.ini`
- **Real Price Data**: Leverages existing ticker data files

### 2. Business Rules Validation

- **2% Deviation Threshold**: Enforced via `v4.utils.trade_filter`
- **Business Calendar**: Price data availability determines trading days
- **Signal Validation**: Allocation weights must sum to 100%

### 3. End-to-End Workflow

1. **Signal Generation**: EMA-based signals with historical data
2. **Trade Filtering**: Deviation threshold and business day validation
3. **Trade Execution**: Portfolio updates and trade logging
4. **Results Calculation**: Performance metrics and allocation history

## Running Tests

### Option 1: Batch File (Recommended)

```bash
tests/v4/run_integration_tests.bat
```

### Option 2: Direct Pytest

```bash
python -m pytest tests/v4/test_integration_pipeline.py -v
```

## Test Data

### Required Files

The tests expect the following files to exist in `v4_trace_outputs/`:

- `signals_output_*.csv` - Historical signal files
- `trade_log_*.csv` - Historical trade logs
- Price data files in `v4/data/` directory

### Data Validation

Tests automatically validate:
- Signal file format and content
- Trade log structure
- Price data availability for business day checking

## Expected Outcomes

### Successful Test Run

All tests should pass, indicating:
- Pipeline executes without errors
- Trade filtering works correctly
- Business day logic functions properly
- Real data integration is stable

### Common Issues

1. **Missing Data Files**: Ensure `v4_trace_outputs/` contains recent signal and trade files
2. **Environment Setup**: Verify virtual environment is properly configured
3. **Module Imports**: Check that production modules are correctly copied

## Integration with CI/CD

These tests can be integrated into continuous integration workflows to:
- Validate production deployments
- Catch regressions in pipeline logic
- Ensure data consistency across updates

## Maintenance

### Updating Tests

When production code changes:
1. Re-copy affected modules to `production_modules/`
2. Update test assertions if expected behavior changes
3. Verify tests still pass with new data files

### Adding New Tests

New integration tests should:
- Use production modules and real data
- Test specific business logic requirements
- Include clear assertions and error messages
- Follow the existing naming convention
