; test_settings_small_range.ini
; Small historical date range for automated testing
; Date: 2025-01-18

; =====================================================
; PARAMETER LISTS
; =====================================================
[Lists]
; ETF Groups
Group1ETFBase = ('SPY', 'SHV', 'EFA', 'TLT', 'PFF')
ETFpicklist = Group1ETFBase

; Strategy algorithms
Strategy_EMA = ema
StrategyAlgos = Strategy_EMA

; Benchmark tickers
Benchmark_SP500 = '^GSPC'
BenchmarkTickers = Benchmark_SP500

; Rebalance frequencies
Rebalance_Monthly = 'monthly'
RebalanceFreqs = Rebalance_Monthly

; Boolean options
True_Option = True
False_Option = False
BooleanOptions = True_Option, False_Option

; =====================================================
; CORE PARAMETERS
; =====================================================
[Core]
; SimpleA parameters - SMALL TEST RANGE
risk_free_ticker = ^IRX
start_date = 2020-01-01
end_date = 2020-03-31

; SimpleN parameters
lookback = 60
top_n = 2

; ComplexN parameters
max_allocation = (optimize=False, default_value=0.25, min_value=0.1, max_value=0.5, increment=0.05)
min_allocation = (optimize=False, default_value=0.0, min_value=0.0, max_value=0.1, increment=0.01)

; AlphaList parameters
tickers = (Group1ETFBase, ETFpicklist)
benchmark_ticker = (Benchmark_SP500, BenchmarkTickers)

; =====================================================
; STRATEGY PARAMETERS
; =====================================================
[Strategy]
; SimpleA parameters
signal_algo = (Strategy_EMA, StrategyAlgos)

; SimpleN parameters
min_weight = 0.0
max_weight = 1.0
strategy_name = EMA_Crossover_Test
system_top_n = {'optimize': False, 'default_value': 2, 'min_value': 1, 'max_value': 5, 'increment': 1}
execution_delay = 0

; ComplexN parameters
ema_short_period = (optimize=False, default_value=12, min_value=5, max_value=20, increment=1)
ema_long_period = (optimize=False, default_value=26, min_value=10, max_value=100, increment=1)
st_lookback = (optimize=False, default_value=15, min_value=5, max_value=30, increment=1)
mt_lookback = (optimize=False, default_value=70, min_value=30, max_value=100, increment=5)
lt_lookback = (optimize=False, default_value=100, min_value=50, max_value=200, increment=10)

; =====================================================
; EMA MODEL PARAMETERS
; =====================================================
[ema_model]
short_period = 12
medium_period = 26
long_period = 50

; =====================================================
; BACKTEST PARAMETERS
; =====================================================
[Backtest]
; SimpleN parameters
initial_capital = 1000000
commission_rate = 0.001
slippage_rate = 0.0005
deviation_threshold = 0.02

; SimpleA parameters
rebalance_freq = (Rebalance_Monthly, RebalanceFreqs)
benchmark_rebalance_freq = (Rebalance_Monthly, RebalanceFreqs)

; SimpleA (boolean) parameters
signal_history = False
include_cash = True

; =====================================================
; ALLOCATION PARAMETERS
; =====================================================
[Allocation]
; SimpleA (boolean) parameters
history = True
target_chart_include = False

; =====================================================
; REPORT PARAMETERS
; =====================================================
[Report]
; SimpleA (boolean) parameters
create_excel = false
save_trade_log = True
include_summary = True
export_simple_validation_files = true

; SimpleA parameters for output
output_directory = output

; =====================================================
; PERFORMANCE PARAMETERS
; =====================================================
[Performance]
; SimpleN parameters
risk_free_rate = 0.02

; SimpleA (boolean) parameters
include_benchmark = True

; =====================================================
; VISUALIZATION PARAMETERS
; =====================================================
[Visualization]
; SimpleA (boolean) parameters
create_charts = False
colorblind_friendly = False
show_annotations = False

; SimpleN parameters
chart_dpi = 150

; =====================================================
; SYSTEM PARAMETERS
; =====================================================
[System]
; SimpleA parameters
log_level = INFO
log_file = logs/test_cps_v4.log

; Unified vs Legacy Pipeline Control
integration_flag = False
retain_csv_signal_output = True

; Retry and Error Recovery Configuration
enable_retry_wrapper = True
max_retries = 1
retry_delay_seconds = 2
retry_on_db_errors = True
retry_on_api_errors = True

; =====================================================
; DATA PARAMETERS
; =====================================================
[data_params]
; Required parameters for data loading
tickers = (Group1ETFBase, ETFpicklist)
start_date = 20200101
end_date = 20200331
price_field = Close
data_storage_mode = Read
