#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Equivalence Testing for CPS v4 Pipeline Comparison

Compares outputs from decoupled vs unified pipeline execution to verify equivalence.
This module provides comprehensive assertions for numerical equivalence with detailed
difference reporting and fail-fast behavior.

Author: AI Assistant  
Date: 2025-01-18
"""

import sys
import os
import argparse
from pathlib import Path
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np
from datetime import datetime
import logging
try:
    import pytest
except ImportError:
    pytest = None

# Add project root to Python path
project_root = Path(__file__).resolve().parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

# Import utilities
from equivalence_utils import (
    DataFrameComparator,
    compare_output_files,
    create_equivalence_report
)

# Import pipeline functions
from production_modules.run_v4_pipeline import run_pipeline
from v4.run_unified_pipeline import run_unified_pipeline

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Test configuration
TOLERANCE = 1e-6  # Float comparison tolerance
OUTPUT_DIR = project_root / 'v4_trace_outputs'
TEST_SETTINGS = str(project_root / 'tests/v4/test_settings_small_range.ini')

def pytest_fixture_wrapper(func):
    """Wrapper to handle pytest fixture when pytest is not available."""
    if pytest is not None:
        return pytest.fixture(scope="module")(func)
    return func

@pytest_fixture_wrapper
def test_output_cleanup():
    """Clean up test output files before and after test execution."""
    
    def cleanup_files():
        """Remove test output files."""
        output_patterns = [
            'signals_output*.parquet',
            'signals_output*.csv',
            'allocation_history*.csv', 
            'trade_log*.csv',
            'Signalwtg_output*.parquet'
        ]
        
        removed_files = []
        for pattern in output_patterns:
            for file_path in OUTPUT_DIR.glob(pattern):
                if file_path.exists():
                    try:
                        os.remove(file_path)
                        removed_files.append(file_path.name)
                    except PermissionError:
                        logger.warning(f"Could not remove {file_path} - file may be in use")
        
        if removed_files:
            logger.info(f"Cleaned up {len(removed_files)} output files")
    
    # Cleanup before tests
    cleanup_files()
    
    yield
    
    # Cleanup after tests
    cleanup_files()


def get_latest_output_files():
    """
    Retrieve the most recent output files from both pipeline runs.
    
    Returns:
        dict: Dictionary with file paths for each output type
    """
    files = {
        'signals_parquet': None,
        'allocation_csv': None, 
        'trade_log_csv': None,
        'signalwtg_parquet': None
    }
    
    # Find most recent files for each type
    # signals_output.parquet (base filename)
    signals_files = list(OUTPUT_DIR.glob('signals_output.parquet'))
    if not signals_files:
        # Try timestamped versions
        signals_files = sorted(OUTPUT_DIR.glob('signals_output_*.parquet'))
    if signals_files:
        files['signals_parquet'] = signals_files[-1]  # Most recent
    
    # allocation_history.csv (base filename) 
    allocation_files = list(OUTPUT_DIR.glob('allocation_history.csv'))
    if not allocation_files:
        # Try timestamped versions
        allocation_files = sorted(OUTPUT_DIR.glob('allocation_history_*.csv'))
    if allocation_files:
        files['allocation_csv'] = allocation_files[-1]  # Most recent
    
    # trade_log.csv (base filename)
    trade_files = list(OUTPUT_DIR.glob('trade_log.csv')) 
    if not trade_files:
        # Try timestamped versions
        trade_files = sorted(OUTPUT_DIR.glob('trade_log_*.csv'))
    if trade_files:
        files['trade_log_csv'] = trade_files[-1]  # Most recent
    
    # Signalwtg_output.parquet (base filename)
    signalwtg_files = list(OUTPUT_DIR.glob('Signalwtg_output.parquet'))
    if not signalwtg_files:
        # Try timestamped versions  
        signalwtg_files = sorted(OUTPUT_DIR.glob('Signalwtg_output_*.parquet'))
    if signalwtg_files:
        files['signalwtg_parquet'] = signalwtg_files[-1]  # Most recent
        
    return files




def test_unified_vs_decoupled_equivalence(test_output_cleanup):
    """
    Test complete equivalence between unified and decoupled pipeline outputs.
    
    This test runs both pipelines and performs detailed assertions on all output files:
    1. Shape assertions (exact row/column matching)
    2. Numeric equality with tolerance (np.allclose with 1e-6)
    3. Non-numeric exact equality
    4. Fail-fast reporting with detailed diffs
    """
    start_time = datetime.now()
    logger.info("Starting equivalence test between unified and decoupled pipelines")
    
    # STEP 1: Run Decoupled Pipeline (Two-Step Process)
    logger.info("=" * 60)
    logger.info("STEP 1: Running Decoupled Pipeline")
    logger.info("=" * 60)
    
    decoupled_start = datetime.now()
    decoupled_results = run_pipeline()
    decoupled_duration = datetime.now() - decoupled_start
    
    assert decoupled_results is not None, "Decoupled pipeline returned None"
    logger.info(f"Decoupled pipeline completed in {decoupled_duration}")
    
    # Capture decoupled output files
    decoupled_files = get_latest_output_files()
    logger.info(f"Decoupled output files: {decoupled_files}")
    
    # Move decoupled files to preserve them during unified run
    decoupled_preserved = {}
    for file_type, file_path in decoupled_files.items():
        if file_path and file_path.exists():
            preserved_path = file_path.with_name(f"decoupled_{file_path.name}")
            file_path.rename(preserved_path)
            decoupled_preserved[file_type] = preserved_path
            logger.info(f"Preserved decoupled {file_type} as {preserved_path.name}")
    
    # STEP 2: Run Unified Pipeline (Single-Step Process)
    logger.info("=" * 60)
    logger.info("STEP 2: Running Unified Pipeline") 
    logger.info("=" * 60)
    
    unified_start = datetime.now()
    unified_results = run_unified_pipeline(custom_settings_file=TEST_SETTINGS)
    unified_duration = datetime.now() - unified_start
    
    assert unified_results is not None, "Unified pipeline returned None"
    logger.info(f"Unified pipeline completed in {unified_duration}")
    
    # Capture unified output files
    unified_files = get_latest_output_files()
    logger.info(f"Unified output files: {unified_files}")
    
    # STEP 3: Load and Compare Each Output File Pair
    logger.info("=" * 60)
    logger.info("STEP 3: Performing Equivalence Assertions")
    logger.info("=" * 60)
    
    # Use utility function for comprehensive file comparison
    try:
        comparison_results = compare_output_files(
            unified_files=unified_files,
            decoupled_files=decoupled_preserved,
            tolerance=TOLERANCE
        )
        comparison_count = sum(comparison_results.values())
        
    except Exception as e:
        logger.error(f"File comparison failed: {e}")
        raise
    
    # STEP 4: Generate Comprehensive Test Report
    total_duration = datetime.now() - start_time
    
    # Create detailed equivalence report
    report = create_equivalence_report(
        comparison_results=comparison_results,
        unified_duration=unified_duration.total_seconds(),
        decoupled_duration=decoupled_duration.total_seconds(),
        output_file=OUTPUT_DIR / f"equivalence_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    )
    
    # Log the report
    for line in report.split('\n'):
        logger.info(line)
    
    # Final assertions
    assert comparison_count >= 2, (
        f"Too few file comparisons performed ({comparison_count}). "
        "Expected at least signals_output.parquet and allocation_history.csv"
    )
    
    # Verify all comparisons passed
    failed_comparisons = [k for k, v in comparison_results.items() if not v]
    assert len(failed_comparisons) == 0, (
        f"The following file comparisons failed: {failed_comparisons}"
    )


if __name__ == "__main__":
    # Allow running the test directly for debugging
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # Create a mock fixture for standalone execution
    class MockFixture:
        def __enter__(self):
            return None
        def __exit__(self, *args):
            return None
    
    with MockFixture():
        test_unified_vs_decoupled_equivalence(MockFixture())
