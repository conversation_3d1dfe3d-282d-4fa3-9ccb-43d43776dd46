# AI-Maintained Documentation System

## Overview

This system enables AI agents to automatically maintain project documentation, plans, and task lists across sessions.

## Implementation Steps

### Step 1: Create AI Maintenance Instructions

**File:** `memory-bank/AI_maintenance_instructions.md`

This file will contain the specific instructions for what the AI should update and when.

### Step 2: Create Update Templates

**Files to create:**

- `memory-bank/session_update_template.md` - Template for session summaries
- `memory-bank/progress_tracking_template.md` - Template for progress updates

### Step 3: Establish Session Workflow

**Start of each session:** AI reads maintenance instructions
**End of each session:** AI updates specified files according to instructions

### Step 4: Create Automated Triggers

**Phrases to train yourself to use:**

- "Update project status" - triggers AI to update progress files
- "End session summary" - triggers AI to document what was accomplished
- "Plan update" - triggers AI to revise plans based on new information

## File Structure

```
memory-bank/
├── AI_maintenance_instructions.md     # Core instructions for AI
├── session_updates/                   # Session-by-session progress
│   ├── 2025-06-28_session_summary.md
│   └── 2025-06-29_session_summary.md
├── core_project_tasks_priority.md     # Living task list (AI updates)
├── progress.md                        # Living progress tracker (AI updates)
├── project_timeline.md               # Living timeline (AI updates)
└── decisions_log.md                  # Living decisions log (AI updates)
```

## AI Maintenance Instructions Template

The AI will follow these instructions automatically:

### Session Start Actions:

1. Read `AI_maintenance_instructions.md`
2. Review `core_project_tasks_priority.md` for current priorities
3. Check `progress.md` for latest status
4. Note any blockers or issues from previous sessions

### During Session Actions:

1. Track completed tasks
2. Note new issues or decisions
3. Identify changes to priorities or timelines

### Session End Actions:

1. Update `progress.md` with completed work
2. Update `core_project_tasks_priority.md` with new/changed tasks
3. Create session summary in `session_updates/`
4. Update `decisions_log.md` with any architectural or technical decisions
5. Update `project_timeline.md` if milestones change

## Benefits

- **Persistent Memory:** Each AI session builds on previous work
- **Automatic Documentation:** No manual effort required
- **Living Documents:** Plans and tasks stay current
- **Decision Tracking:** Important choices are preserved
- **Progress Visibility:** Clear view of what's working and what's blocked
