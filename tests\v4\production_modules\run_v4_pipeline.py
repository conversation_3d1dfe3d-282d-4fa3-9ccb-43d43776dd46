"""
v4/run_v4_pipeline.py
Pipeline orchestrator script for the decoupled backtest architecture.
Part of the CPS v4 compliant backtest system.

This script coordinates the execution of all phases in sequence:
1. Run Signal Phase
2. Validate Signal Output
3. Run Trading Phase with Signal Output
4. Generate Final Reports
"""

import os
import sys
from pathlib import Path
import logging
from datetime import datetime

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import phase runners
from v4.Algo_signal_phase import run_signal_phase
from v4.run_trading_phase import run_trading_phase

# Configure smart logging
from v4.utils.smart_logging import create_smart_logger
logger = create_smart_logger(__name__)

def run_pipeline():
    """
    Run complete V4 pipeline.
    
    This function orchestrates the entire backtest process:
    1. Signal generation
    2. Trading simulation
    3. Results collection
    
    Returns:
        dict: Backtest results
    """
    start_time = datetime.now()
    logger.info(f"[MILESTONE] Starting V4 Pipeline at {start_time}")
    
    # Ensure output directory exists
    output_dir = _project_root / "v4_trace_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Phase 1: Signal Generation
        logger.info("Running Signal Phase...")
        signals_file = run_signal_phase()
        logger.info(f"Signal phase completed. Output: {signals_file}")
        
        # Phase 2: Trading
        logger.info("Running Trading Phase...")
        results = run_trading_phase(signals_file=signals_file)
        logger.info("Trading phase completed.")
        
        # Calculate and log performance metrics
        if results and 'performance' in results:
            perf = results['performance']
            logger.info("Performance Summary:")
            logger.info(f"  - Total Return: {perf.get('total_return', 0):.2%}")
            logger.info(f"  - CAGR: {perf.get('cagr', 0):.2%}")
            logger.info(f"  - Volatility: {perf.get('volatility', 0):.2%}")
            logger.info(f"  - Sharpe Ratio: {perf.get('sharpe', 0):.2f}")
            logger.info(f"  - Max Drawdown: {perf.get('max_drawdown', 0):.2%}")
            logger.info(f"  - Turnover: {perf.get('turnover', 0):.2f}")
            logger.info(f"  - Win Rate: {perf.get('win_rate', 0):.2%}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"[MILESTONE] Pipeline complete! Duration: {duration}")
        
        return results
    
    except Exception as e:
        logger.error(f"Pipeline execution failed: {e}", exc_info=True)
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(f"[MILESTONE] Pipeline failed after {duration}")
        raise

if __name__ == "__main__":
    run_pipeline()
