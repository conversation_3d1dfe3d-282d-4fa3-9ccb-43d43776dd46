#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
v4/run_unified_pipeline.py

Unified Pipeline Façade for CPS v4 Backtest System
Orchestrates the complete signal generation and trading workflow in a single execution.

This module serves as the main entry point for running the complete backtesting pipeline,
handling both signal generation and trading phases sequentially. It provides:
- CLI argument parsing and settings loading
- Shared logging across phases
- Direct DataFrame handoff between signal and trading phases
- Standardized output file management
- Comprehensive error handling and milestone logging

Author: AI Assistant
Date: 2025-01-18
"""

import sys
import os
import argparse
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, Dict, Any
import pandas as pd

# Add project root to path
_script_path = Path(__file__).resolve()
_project_root = _script_path.parent.parent
if str(_project_root) not in sys.path:
    sys.path.insert(0, str(_project_root))

# Import core V4 components
from v4.settings.settings_CPS_v4 import load_settings
from v4.Algo_signal_phase import run_signal_phase
from v4.run_trading_phase import run_trading_phase
from v4.utils.tracing_utils import setup_trace_directory, save_df_to_trace_dir
from v4.utils.log_milestone import log_milestone, log_phase_start, log_phase_complete, log_error_milestone


def setup_logger(settings: Dict[str, Any]) -> logging.Logger:
    """Initialize shared logger for the unified pipeline.
    
    Args:
        settings: Configuration settings from CPS v4
        
    Returns:
        Configured logger instance
    """
    # Get log level from settings, default to INFO
    log_level = settings.get('system', {}).get('log_level', 'INFO').upper()
    
    # Configure logging with both console and file output
    logger = logging.getLogger('unified_pipeline')
    logger.setLevel(getattr(logging, log_level, logging.INFO))
    
    # Remove existing handlers to avoid duplicates
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler - save to v4_trace_outputs
    trace_dir = setup_trace_directory()
    log_file = trace_dir / f"unified_pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(str(log_file))
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    logger.info(f"Logger initialized. Log file: {log_file}")
    return logger




def modify_run_trading_to_accept_dataframe(signals_df: Optional[pd.DataFrame] = None, 
                                         signals_file: Optional[str] = None) -> Dict[str, Any]:
    """Modified version of run_trading_phase to accept DataFrame directly.
    
    This function extends the existing run_trading_phase functionality to accept
    pre-computed signals as a DataFrame, enabling direct handoff from signal generation.
    
    Args:
        signals_df: Pre-computed signals DataFrame (takes precedence over signals_file)
        signals_file: Path to signals file (legacy path)
        
    Returns:
        Results dictionary from backtest execution
    """
    from v4.settings.settings_CPS_v4 import load_settings
    from v4.engine.data_loader_v4 import load_data_for_backtest
    from v4.engine.backtest_v4 import BacktestEngine
    from v4.run_trading_phase import validate_signals
    
    logger = logging.getLogger('unified_pipeline.trading')
    logger.info("[MILESTONE] Starting Trading Phase")
    
    # Load settings and data
    settings = load_settings()
    data_result = load_data_for_backtest(settings)
    price_data = data_result['price_data']
    
    # Log data information
    logger.info(f"Loaded price data with shape {price_data.shape}")
    logger.info(f"Date range: {price_data.index.min()} to {price_data.index.max()}")
    logger.info(f"Tickers: {', '.join(price_data.columns)}")
    
    # Handle signals input - DataFrame takes precedence
    if signals_df is not None:
        logger.info("Using pre-computed signals DataFrame from signal generation phase")
        signals = signals_df.copy()
        logger.info(f"Signals DataFrame shape: {signals.shape}")
        logger.info(f"Signals date range: {signals.index.min()} to {signals.index.max()}")
    else:
        # Fallback to file-based loading (legacy path) - CSV only
        # Note: Uses CSV format only (Parquet eliminated for performance)
        if signals_file is None:
            # Find the most recent timestamped signal file
            import glob
            signal_pattern = str(_project_root / "v4_trace_outputs" / "signals_output_*.csv")
            signal_files = glob.glob(signal_pattern)
            if signal_files:
                # Sort by filename to get most recent timestamp
                signals_file = max(signal_files)
                logger.info(f"No signal source provided, using most recent timestamped file: {signals_file}")
            else:
                raise FileNotFoundError("No timestamped signal files found in v4_trace_outputs")
        else:
            logger.info(f"Using provided signal file: {signals_file}")

        signals_path = Path(signals_file)
        if not signals_path.exists():
            logger.error(f"Signals file {signals_file} not found!")
            raise FileNotFoundError(f"Signals file {signals_file} not found!")

        try:
            # Load CSV file and set Date column as index
            signals = pd.read_csv(signals_file, index_col='Date', parse_dates=True)
            logger.info(f"Loaded signals from CSV with shape {signals.shape}")
            logger.info(f"Signals date range: {signals.index.min()} to {signals.index.max()}")

        except Exception as e:
            logger.error(f"Error loading signals file: {e}")
            raise

    # Validate signals
    if not validate_signals(signals, price_data):
        logger.error("Signal validation failed")
        raise ValueError("Signal validation failed")
    
    # Validate that signals sum to 100% (within tolerance)
    signal_sums = signals.sum(axis=1)
    max_deviation = abs(signal_sums - 1.0).max()
    if max_deviation > 0.01:  # 1% tolerance
        logger.warning(f"Signal validation warning! Max deviation from 100%: {max_deviation:.3%}")
    else:
        logger.info(f"Signal validation passed. Max deviation from 100%: {max_deviation:.3%}")
    
    # Run backtest
    logger.info("Initializing backtest engine")
    engine = BacktestEngine(settings)
    
    # Validate that BacktestEngine has required methods
    if not hasattr(engine, 'run_backtest_with_signals'):
        logger.error("BacktestEngine does not have run_backtest_with_signals method")
        raise AttributeError("BacktestEngine does not have run_backtest_with_signals method")
    
    logger.info("Running backtest with pre-computed signals")
    results = engine.run_backtest_with_signals(signals, price_data)
    
    # Save results
    output_dir = _project_root / "v4_trace_outputs"
    os.makedirs(output_dir, exist_ok=True)
    
    # Add timestamp to filenames
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    allocation_history = results.get('allocation_history')
    trade_log = results.get('trade_log')
    
    if allocation_history is not None:
        allocation_file = output_dir / f"allocation_history_{timestamp}.csv"
        allocation_history.to_csv(str(allocation_file))
        
        logger.info(f"Saved allocation history to: {allocation_file}")
    else:
        logger.warning("No allocation history in results")
    
    if trade_log is not None:
        trades_file = output_dir / f"trade_log_{timestamp}.csv"
        trade_log.to_csv(str(trades_file))
        
        logger.info(f"Saved trade log to: {trades_file}")
    else:
        logger.warning("No trade log in results")
    
    logger.info("[MILESTONE] Trading phase complete")
    return results


def run_unified_pipeline(custom_settings_file: Optional[str] = None,
                        signals_file: Optional[str] = None,
                        skip_signal_generation: bool = False) -> Dict[str, Any]:
    """Run the complete unified pipeline with signal generation and trading phases.
    
    Args:
        custom_settings_file: Optional path to custom settings file
        signals_file: Optional path to pre-computed signals file (skips signal generation)
        skip_signal_generation: If True, skip signal generation and use existing signals
        
    Returns:
        Dictionary containing results from both phases
    """
    pipeline_start_time = datetime.now()
    
    try:
        # 1. Load Settings
        print("="*60)
        print("UNIFIED PIPELINE v4 - STARTING")
        print("="*60)
        
        if custom_settings_file:
            settings = load_settings(custom_file=custom_settings_file)
        else:
            settings = load_settings()
        
        # 2. Initialize Shared Logger
        logger = setup_logger(settings)
        log_milestone(logger, "Unified Pipeline Starting", "START")
        
        results = {}
        signals_df = None
        
        # 3. Signal Generation Phase
        if not skip_signal_generation and signals_file is None:
            log_milestone(logger, "Beginning Signal Generation Phase", "SIGNAL_START")
            
            try:
                # Run signal generation and capture the output
                signals_output_path = run_signal_phase()
                
                if signals_output_path and Path(signals_output_path).exists():
                    # Load the generated signals DataFrame (CSV only)
                    # Note: Uses CSV format only (Parquet eliminated for performance)
                    signals_df = pd.read_csv(signals_output_path, index_col='Date', parse_dates=True)
                    
                    logger.info(f"Successfully loaded signals DataFrame with shape: {signals_df.shape}")
                    results['signals_file_path'] = signals_output_path
                    results['signals_dataframe'] = signals_df
                    
                    log_milestone(logger, "Signal Generation Complete", "POST_SIGNAL")
                else:
                    raise RuntimeError("Signal generation did not produce expected output file")
                    
            except Exception as e:
                logger.error(f"Signal generation phase failed: {e}")
                raise
        else:
            if signals_file:
                logger.info(f"Skipping signal generation, using provided signals file: {signals_file}")
            else:
                logger.info("Skipping signal generation as requested")
        
        # 4. Trading Phase - Feed signals directly
        log_milestone(logger, "Beginning Trading Phase", "TRADING_START")
        
        try:
            # Direct execution without retry
            log_milestone(logger, "Executing trading phase directly")
            trading_results = modify_run_trading_to_accept_dataframe(
                signals_df=signals_df,
                signals_file=signals_file
            )
            
            results.update(trading_results)
            log_milestone(logger, "Trading Phase Complete", "POST_TRADING")
            
        except Exception as e:
            log_error_milestone(logger, f"Trading phase failed: {e}")
            raise
        
        # 5. Save consolidated output files to v4_trace_outputs/
        log_milestone(logger, "Saving Consolidated Output Files", "SAVING_OUTPUTS")
        
        trace_dir = setup_trace_directory()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save signals if available
        if signals_df is not None:
            save_df_to_trace_dir(
                signals_df, 
                f"unified_signals_{timestamp}.csv",
                step_description="Unified Pipeline Signals"
            )
        
        # Save allocation history if available
        if 'allocation_history' in results and results['allocation_history'] is not None:
            save_df_to_trace_dir(
                results['allocation_history'],
                f"unified_allocation_history_{timestamp}.csv", 
                step_description="Unified Pipeline Allocation History"
            )
        
        # Save trade log if available
        if 'trade_log' in results and results['trade_log'] is not None:
            save_df_to_trace_dir(
                results['trade_log'],
                f"unified_trade_log_{timestamp}.csv",
                step_description="Unified Pipeline Trade Log"
            )
        
        # 6. Pipeline Complete
        pipeline_end_time = datetime.now()
        pipeline_duration = pipeline_end_time - pipeline_start_time
        
        log_milestone(logger, f"UNIFIED PIPELINE COMPLETE - Duration: {pipeline_duration}", "COMPLETE")
        
        results['pipeline_start_time'] = pipeline_start_time
        results['pipeline_end_time'] = pipeline_end_time
        results['pipeline_duration'] = pipeline_duration
        
        print("="*60)
        print("UNIFIED PIPELINE v4 - COMPLETED SUCCESSFULLY")
        print(f"Total Duration: {pipeline_duration}")
        print("="*60)
        
        return results
        
    except Exception as e:
        # Route exceptions to logger then re-raise
        if 'logger' in locals():
            logger.error(f"Unified pipeline failed with error: {e}")
            logger.error("Full traceback:", exc_info=True)
        else:
            print(f"Pipeline failed before logger initialization: {e}")
        
        print("="*60)
        print("UNIFIED PIPELINE v4 - FAILED")
        print("="*60)
        
        # Re-raise the exception
        raise


def parse_cli_arguments() -> argparse.Namespace:
    """Parse command line arguments for the unified pipeline.
    
    Returns:
        Parsed arguments namespace
    """
    parser = argparse.ArgumentParser(
        description='Unified Pipeline v4 - Complete Signal Generation and Trading Workflow',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_unified_pipeline.py                           # Run complete pipeline
  python run_unified_pipeline.py --settings custom.ini    # Use custom settings
  python run_unified_pipeline.py --signals signals.csv    # Use existing signals
  python run_unified_pipeline.py --skip-signals           # Skip signal generation
        """
    )
    
    parser.add_argument(
        '--settings', 
        type=str, 
        help='Path to custom settings file (default: use settings_CPS_v4.ini)'
    )
    
    parser.add_argument(
        '--signals', 
        type=str, 
        help='Path to pre-computed signals file (skips signal generation)'
    )
    
    parser.add_argument(
        '--skip-signals', 
        action='store_true', 
        help='Skip signal generation phase and use existing signals'
    )
    
    parser.add_argument(
        '--verbose', 
        action='store_true', 
        help='Enable verbose logging output'
    )
    
    return parser.parse_args()


def main() -> None:
    """Main entry point for the unified pipeline."""
    try:
        # Parse CLI arguments
        args = parse_cli_arguments()
        
        # Run the unified pipeline
        results = run_unified_pipeline(
            custom_settings_file=args.settings,
            signals_file=args.signals,
            skip_signal_generation=args.skip_signals
        )
        
        # Exit successfully
        sys.exit(0)
        
    except KeyboardInterrupt:
        print("\nPipeline interrupted by user")
        sys.exit(1)
        
    except Exception as e:
        print(f"\nPipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
