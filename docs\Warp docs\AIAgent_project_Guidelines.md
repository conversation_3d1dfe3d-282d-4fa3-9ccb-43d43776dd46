# AI Agent Guidelines for Backtest_FinAsset_Alloc_Template

This document provides guidelines for the AI agent working on this project.

## 1. Project Overview

- **Objective:** The primary goal is to develop a simple, flexible, and optimizable backtesting and trading pipeline (CPS V4).
- **Core Principle:** All logic, especially algorithm selection and parameters, must be driven by configuration files, not hardcoded in the source.

## 2. Key Commands

- **Run Production Pipeline:** `
- Out of date = run_main_v4_prod2.bat ; prior 
- 

## 3. Coding Style & Conventions

- **Modularity:** Keep Python modules under 450 lines.
- **Parameter Access:** All parameters must be accessed directly from the `settings_CPS_v4.py` module. Do not use adapters or fallbacks.
- **Naming:** Follow standard Python PEP 8 naming conventions.
- **Comments:** Add comments to explain the *why* of complex logic, not the *what*.

## 4. Testing Strategy

- **Philosophy:** All testing and validation must use the full production code flow. Do not create synthetic test harnesses.  Never use Mock ups or simulated data in testing. Always start by copying needed modules into testing directory structure, and create a thin orchestration layer if necessary.
- **Test Location:** Unit tests for `v4` should be located in `tests/v4`.
- **Focus:** _ Ask you to clarify at start of test design.

## 5. Architectural Principles

- **Decoupling:** The Signal Generation and Trading phases are decoupled and should be run as separate, sequential processes, until the unified process is validated.
- **Data Handoff:** The handoff between the signal and trading phases is done via `.parquet` files located in `v4_trace_outputs/`.  ToDo - validate this - currently overriden using csv file.
- **Immutability:** Treat data flowing between major components (like signals) as immutable once generated.

## 6. Specific "Do's and Don'ts"

- **DO:** Use the `read_file` tool to examine a file before attempting a `replace` operation.
- **DO:** Confirm the location of core engine files before writing tests.
- **DON'T:** Modify files in the `archive/` directory.
- **DON'T:** Introduce new third-party libraries without explicit permission.
