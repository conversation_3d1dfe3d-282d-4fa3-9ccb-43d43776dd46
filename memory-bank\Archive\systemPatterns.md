# System Patterns

- Modular separation: signal generation, allocation, execution, portfolio management, reporting
- Centralized portfolio state (single source of truth)
- All path and parameter definitions centralized in config/paths.py and config files
- No fallback/backup path logic outside config/paths.py (per user rules)
- Feedback loops: portfolio state informs allocation, execution, and reporting
- Strict compliance with Custom Function Library interface

_Expand with additional architecture diagrams or patterns as needed._

**Update (April 2025):**
- Benchmark parameter grouping and parameter audit are now standard patterns.
- Parameter grouping by role (e.g., benchmark) is handled via sub-dictionaries and docstring tags, not code logic.
