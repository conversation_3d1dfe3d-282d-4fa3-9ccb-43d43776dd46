@echo off
REM Test script for documentation ingestion tool migration
echo ==========================================
echo DOCUMENTATION INGEST TOOL MIGRATION TEST
echo ==========================================

REM Set Python executable path - using isolated virtual environment
SET PYTHON_EXE=F:\AI_Library\my_quant_env\Scripts\python.exe

REM Verify Python exists
if not exist "%PYTHON_EXE%" (
    echo [X] ERROR: Python not found at %PYTHON_EXE%
    pause
    exit /b 1
)

echo [i] Using Python: %PYTHON_EXE%

REM Run the test script
"%PYTHON_EXE%" "%~dp0test_migration.py"

REM Check exit code and log success
if %ERRORLEVEL% EQU 0 (
    echo [✓] Migration test completed successfully.
) else (
    echo [X] ERROR: Migration test failed. Check logs.
)

pause
