# V3 Module + Functions Reference (AI-Optimized)

<!-- AI: CREATE MEMORY FROM THIS FILE
Title: V3 Module Function Reference
Tags: [module_reference, function_listing, v3_system]
-->

> **Last Updated**: 2025-06-03
> **Purpose**: Comprehensive reference of all V3 modules and their functions
> 
> **Related Files**:
> 
> - [System Files + Flow](systemFiles+Flow_AI.md)
> - [Parameter System Reference](v3_parameter_system_reference.md)

## 🔍 Quick Module Lookup

| Component            | Key Modules                                                                     | Status |
| -------------------- | ------------------------------------------------------------------------------- |:------:|
| **Parameter System** | `v3_engine/parameter_registry.py`, `v3_engine/parameters.py`                    | ✅      |
| **GUI**              | `app/gui/v3_gui_core.py`, `app/gui/v3_parameter_widgets.py`                     | ✅      |
| **Strategy**         | `v3_engine/ema_v3_adapter.py`                                                   | ✅      |
| **Reporting**        | `v3_reporting/v3_performance_report.py`, `v3_reporting/v3_allocation_report.py` | ✅      |

## 🧩 Core Engine Modules

| Module                                       | Key Functions                                                    | Status | Description                    |
| -------------------------------------------- | ---------------------------------------------------------------- |:------:| ------------------------------ |
| `v3_engine/parameter_registry.py`            | `register_parameter()`, `get_parameter()`                        | ✅      | Central parameter registry     |
| `v3_engine/parameters.py`                    | `NumericParameter.__init__()`, `CategoricalParameter.validate()` | ✅      | Parameter class definitions    |
| `v3_engine/strategy_parameter_set.py`        | `register_parameters()`, `get_parameter_values()`                | ✅      | Strategy parameter container   |
| `v3_engine/parameter_optimizer.py`           | `get_optimization_combinations()`, `optimize_parameters()`       | ✅      | Parameter optimization         |
| `v3_engine/ema_v3_adapter.py`                | `generate_signal()`, `calculate_ema_metrics()`                   | ✅      | EMA strategy adapter           |
| `v3_engine/performance_reporter_adapter.py`  | `generate_performance_report()`                                  | ✅      | Reporting adapter              |
| `v3_engine/V3_perf_repadapt_legacybridge.py` | `convert_legacy_parameters()`                                    | ✅      | Legacy bridge functions        |
| `v3_engine/V3_perf_repadapt_paramconvert.py` | `convert_parameter_tuple()`                                      | ✅      | Parameter conversion utilities |
| `v3_engine/gui_parameter_manager.py`         | `create_parameter_widgets()`, `update_parameters()`              | ✅      | GUI parameter management       |
| `v3_engine/data_validator.py`                | `validate_signal_allocation()`, `validate_price_data()`          | ⚠️     | Data validation utilities      |

## 📊 Reporting Modules

| Module                                           | Key Functions                                                       | Status | Description                         |
| ------------------------------------------------ | ------------------------------------------------------------------- |:------:| ----------------------------------- |
| `v3_reporting/v3_performance_report.py`          | `generate_v3_performance_report()`                                  | ✅      | Performance report generation       |
| `v3_reporting/v3_allocation_report.py`           | `generate_v3_allocation_report()`                                   | ✅      | Allocation report generation        |
| `v3_reporting/reporting_parameters.py`           | `register_reporting_parameters()`                                   | ✅      | Reporting parameter definitions     |
| `v3_reporting/visualization_parameters.py`       | `register_visualization_parameters()`                               | ✅      | Visualization parameter definitions |
| `v3_reporting/parameter_registry_integration.py` | `register_all_reporting_parameters()`, `get_reporting_parameters()` | ✅      | Parameter registry integration      |
| `v3_reporting/v3_performance_charts.py`          | `create_return_chart()`, `create_drawdown_chart()`                  | ✅      | Performance chart generation        |
| `v3_reporting/v3_trade_log.py`                   | `format_trade_log()`, `summarize_trades()`                          | ✅      | Trade log formatting                |

## 🖥️ GUI Modules

| Module                              | Key Functions                                             | Status | Description            |
| ----------------------------------- | --------------------------------------------------------- |:------:| ---------------------- |
| `app/gui/v3_gui_core.py`            | `MainWindowV3.__init__()`, `_run_backtest()`              | ✅      | Main GUI window        |
| `app/gui/v3_parameter_widgets.py`   | `create_parameter_widget()`, `update_from_widget()`       | ✅      | Parameter widgets      |
| `app/gui/v3_gui_actions.py`         | `run_backtest_action()`, `save_config_action()`           | ✅      | GUI action handlers    |
| `app/gui/v3_register_parameters.py` | `register_core_parameters()`, `register_ema_parameters()` | ✅      | Parameter registration |

## 📈 Strategy Modules

| Module                                | Key Functions                                               | Status | Description                   |
| ------------------------------------- | ----------------------------------------------------------- |:------:| ----------------------------- |
| `v3_strategies/ema_strategy.py`       | `calculate_signal()`, `get_weights()`                       | ✅      | EMA strategy implementation   |
| `v3_strategies/strategy_base.py`      | `Strategy.__init__()`, `generate_signal()`                  | ✅      | Base strategy class           |
| `v3_strategies/strategy_factory.py`   | `create_strategy()`, `get_available_strategies()`           | 🔄     | Strategy factory (planned)    |
| `v3_strategies/strategy_validator.py` | `validate_strategy_config()`, `check_required_parameters()` | 🔄     | Strategy validation (planned) |

## 🔄 V2 to V3 Module Mapping

| V2 Module                              | V3 Module                                   | Status | Key Changes                            |
| -------------------------------------- | ------------------------------------------- |:------:| -------------------------------------- |
| `config/config_v2.py`                  | `v3_engine/parameter_registry.py`           | ✅      | Parameter tuples → type-safe objects   |
| `config/parameter_optimization.py`     | `v3_engine/parameter_optimizer.py`          | ✅      | Enhanced categorical parameter support |
| `models/ema_allocation_model.py`       | `v3_engine/ema_v3_adapter.py`               | ✅      | Adapter pattern for V3 parameters      |
| `performance/performance_reporting.py` | `v3_engine/performance_reporter_adapter.py` | ✅      | Adapter pattern for reporting          |
| `config_interface.py`                  | `v3_engine/gui_parameter_manager.py`        | ✅      | Enhanced GUI integration               |

## 📊 Data Quality Modules

| V3 Module                     | Legacy Equivalent       | Key Functions                                       | Status | Key Changes                        |
| ----------------------------- | ----------------------- | --------------------------------------------------- |:------:| ---------------------------------- |
| `v3_engine/data_validator.py` | `data/quality_check.py` | `validate_data()`, `check_missing()`                | ⚠️     | Basic validation rules implemented |
| `v3_engine/data_cleaner.py`   | N/A                     | `clean_price_data()`, `handle_missing_values()`     | 🔄     | Planned data cleaning utilities    |
| `v3_engine/data_metrics.py`   | N/A                     | `calculate_data_metrics()`, `report_data_quality()` | 🔄     | Planned data quality metrics       |

## ⚠️ Error Handling Modules

| V3 Module                        | Legacy Equivalent | Key Functions                               | Status | Description                   |
| -------------------------------- | ----------------- | ------------------------------------------- |:------:| ----------------------------- |
| `v3_engine/error_handler.py`     | N/A               | `handle_error()`, `log_error()`             | 🔄     | Centralized error handling    |
| `v3_engine/validation_errors.py` | N/A               | `ValidationError`, `ParameterError`         | 🔄     | Error class definitions       |
| `v3_engine/error_reporter.py`    | N/A               | `report_errors()`, `format_error_message()` | 🔄     | User-friendly error reporting |

## 🔄 Strategy Loading System

```mermaid
flowchart LR
    StrategyFactory[v3_strategies/strategy_factory.py] -->|Create| Strategy[v3_strategies/strategy_base.py]
    Strategy -->|Register Parameters| ParameterRegistry[v3_engine/parameter_registry.py]
    ParameterRegistry -->|Parameter Values| Strategy
    Strategy -->|Validate Config| StrategyValidator[v3_strategies/strategy_validator.py]

    classDef factory fill:#BBDEFB,stroke:#1976D2,color:#0D47A1;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef validator fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;

    class StrategyFactory factory;
    class Strategy strategy;
    class ParameterRegistry param;
    class StrategyValidator validator;
```

## 🔄 Component Interactions

```mermaid
flowchart LR
    %% Data Flow
    DataLoader[data/data_loader.py] -->|OHLCV Data| EMAAdapter[v3_engine/ema_v3_adapter.py]
    DataLoader -->|Benchmark Data| BacktestEngine[engine/backtest.py]

    %% Parameter Flow
    ParameterRegistry[v3_engine/parameter_registry.py] -->|Parameters| EMAAdapter
    ParameterRegistry -->|Parameters| BacktestEngine

    %% Execution Flow
    EMAAdapter -->|Signals| BacktestEngine

    %% Reporting Flow
    BacktestEngine -->|Results| PerformanceReporter[v3_reporting/v3_performance_report.py]
    BacktestEngine -->|Allocations| AllocationReporter[v3_reporting/v3_allocation_report.py]

    %% Styling
    classDef data fill:#F5F5F5,stroke:#616161,color:#212121;
    classDef param fill:#C8E6C9,stroke:#388E3C,color:#1B5E20;
    classDef strategy fill:#FFECB3,stroke:#F57F17,color:#E65100;
    classDef report fill:#F3E5F5,stroke:#8E24AA,color:#4A148C;

    class DataLoader data;
    class ParameterRegistry param;
    class EMAAdapter strategy;
    class BacktestEngine strategy;
    class PerformanceReporter,AllocationReporter report;
```

## 📝 Parameter Class Documentation

### Parameter Class Hierarchy

```text
BaseParameter
├── NumericParameter
├── CategoricalParameter
│   └── CategoricalListParameter
└── ConfigParameter
```

### Parameter Usage Examples

#### NumericParameter

```python
# Definition
lookback_period = NumericParameter(
    name='lookback_period',
    default=30,
    min_val=10,
    max_val=100,
    step=5,
    show_in_gui=True,
    optimizable=True
)

# Registration
registry.register_parameter('ema', lookback_period)

# Usage
value = registry.get_parameter('lookback_period', 'ema').value
```

#### CategoricalParameter

```python
# Definition
rebalance_frequency = CategoricalParameter(
    name='rebalance_frequency',
    default='monthly',
    options=['daily', 'weekly', 'monthly', 'quarterly'],
    show_in_gui=True,
    optimizable=True
)

# Registration
registry.register_parameter('core', rebalance_frequency)

# Usage
freq = registry.get_parameter('rebalance_frequency', 'core').value
```

## 📈 Parameter Flow

1. **Definition**: Parameters are defined in registration modules
   
   ```python
   # In v3_reporting/reporting_parameters.py
   def register_reporting_parameters(registry=None):
       # Use provided registry or get the global one
       if registry is None:
           registry = get_registry()
   
       # Register reporting parameters
       reporting_params = [
           StrategyOptimizeParameter(
               name="create_excel",
               param_type="categorical",
               default=True,
               choices=[True, False],
               description="Whether to create Excel reports",
               show_in_gui=True,
               show_in_report=True,
               group="reporting"
           ),
           # More parameters...
       ]
   
       # Register all reporting parameters
       for param in reporting_params:
           registry.register_parameter('reporting', param.to_v3_parameter())
   ```

2. **Registration**: Parameters are registered with the registry
   
   ```python
   # In v3_gui_core.py
   registry = ParameterRegistry()
   register_core_parameters(registry)
   register_ema_parameters(registry)
   ```

3. **GUI Integration**: Parameters appear in the GUI
   
   ```python
   # In v3_parameter_widgets.py
   def create_widgets(registry):
       for param in registry.get_parameters('ema'):
           if param.show_in_gui:
               create_parameter_widget(param)
   ```

4. **Value Setting**: Values are set via GUI or code
   
   ```python
   # In v3_gui_actions.py
   def update_from_gui():
       for param, widget in param_widgets.items():
           param.value = get_value_from_widget(widget)
   ```

5. **Engine Usage**: Parameters are used in backtest
   
   ```python
   # In ema_v3_adapter.py
   def generate_signal(registry):
       lookback = registry.get_parameter('lookback_period', 'ema').value
       # Use lookback in calculations
   ```

6. **Reporting**: Parameters appear in reports
   
   ```python
   # In v3_performance_report.py
   def generate_report(registry, results):
       params = {p.name: p.value for p in registry.get_all_parameters()}
       # Include params in report
   ```

## 🧪 Verification System

| Module                            | Key Functions                                                           | Status | Description                  |
| --------------------------------- | ----------------------------------------------------------------------- |:------:| ---------------------------- |
| `tests/verify_v3_reporting.py`    | `setup_test_environment()`, `run_all_tests()`                           | ✅      | Main verification script     |
| `tests/test_v3_reporting.py`      | `test_parameter_registration()`, `test_performance_report_generation()` | ✅      | End-to-end test script       |
| `verify_v3_reporting.py` (legacy) | `verify_file_size()`, `verify_excel_file()`                             | ⚠️     | Outdated verification script |

### Verification Process

The verification system has been refactored to run in an isolated test environment using the following process:

1. `run_v3_verification.bat` sets environment variables and activates the Python environment
2. `tests/verify_v3_reporting.py` performs verification in an isolated test environment
3. Verification results are logged and a report is generated

## 🔍 Recent Changes (June 2025)

1. **Parameter System**:
   
   - Added validation for execution_delay parameter
   - Fixed optimization handling for categorical parameters
   - Identified parameter_registry.py as exceeding 450-line limit

2. **Reporting**:
   
   - Refactored verification system into tests directory
   - Added parameter registry integration module
   - Split reporting and visualization parameters into separate modules
   - Enhanced adapter pattern with legacy bridge components

3. **GUI**:
   
   - Added parameter group visibility controls
   - Improved parameter widget layout
   - Fixed stalling issues in backtest execution

---

*This document is maintained as the comprehensive reference for all V3 modules and functions. Update as the system evolves.*
