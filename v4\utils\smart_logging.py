"""
v4/utils/smart_logging.py
Intelligent logging system with date-based verbosity control.

This module provides smart logging functionality that automatically adjusts
verbosity based on the data being processed:
- Recent dates (last 2 weeks): Full detailed logging
- Older dates: Only important events (trades, errors, milestones)
- Special events: Always logged regardless of date
"""

import logging
from datetime import date, datetime, timedelta
from typing import Optional, Union
import pandas as pd

# Configuration
DETAILED_LOGGING_DAYS = 14  # Show detailed logs for last 2 weeks
SUMMARY_INTERVAL_DAYS = 30  # Show periodic summaries for older dates

class SmartLogger:
    """Wrapper around standard logger with date-based intelligence."""
    
    def __init__(self, name: str, base_logger: Optional[logging.Logger] = None):
        """Initialize smart logger."""
        self.name = name
        self.logger = base_logger or logging.getLogger(name)
        self._daily_summary_count = {}  # Track summaries per date
        
    def should_show_detailed_logs(self, target_date: Optional[Union[date, pd.Timestamp]] = None) -> bool:
        """Determine if detailed logs should be shown based on date."""
        if target_date is None:
            return True  # Default to showing logs if no date provided
        
        # Convert to date if timestamp
        if isinstance(target_date, pd.Timestamp):
            target_date = target_date.date()
        elif hasattr(target_date, 'date'):
            target_date = target_date.date()
        
        cutoff_date = datetime.now().date() - timedelta(days=DETAILED_LOGGING_DAYS)
        return target_date >= cutoff_date
    
    def should_show_periodic_summary(self, target_date: Optional[Union[date, pd.Timestamp]] = None) -> bool:
        """Determine if we should show periodic summary for older dates."""
        if target_date is None:
            return False
            
        # Convert to date if timestamp
        if isinstance(target_date, pd.Timestamp):
            target_date = target_date.date()
        elif hasattr(target_date, 'date'):
            target_date = target_date.date()
        
        # Don't show summaries for recent dates (they get detailed logs)
        if self.should_show_detailed_logs(target_date):
            return False
        
        # Show summary every N days for older dates
        date_key = target_date.strftime('%Y-%m')
        if date_key not in self._daily_summary_count:
            self._daily_summary_count[date_key] = 0
            return True  # First time seeing this month
        
        self._daily_summary_count[date_key] += 1
        return self._daily_summary_count[date_key] % SUMMARY_INTERVAL_DAYS == 0
    
    # Standard logging methods with smart behavior
    def debug(self, msg: str, target_date: Optional[Union[date, pd.Timestamp]] = None):
        """Debug level logging (only shown for recent dates)."""
        if self.should_show_detailed_logs(target_date):
            self.logger.debug(msg)
    
    def info(self, msg: str, target_date: Optional[Union[date, pd.Timestamp]] = None, force: bool = False):
        """Info level logging with optional date filtering."""
        if force or self.should_show_detailed_logs(target_date) or self.should_show_periodic_summary(target_date):
            self.logger.info(msg)
    
    def warning(self, msg: str, target_date: Optional[Union[date, pd.Timestamp]] = None):
        """Warning level logging (always shown)."""
        self.logger.warning(msg)
    
    def error(self, msg: str, target_date: Optional[Union[date, pd.Timestamp]] = None):
        """Error level logging (always shown)."""
        self.logger.error(msg)
    
    # Special purpose logging methods
    def milestone(self, msg: str, **kwargs):
        """Log important milestones (always shown)."""
        self.logger.info(f"[MILESTONE] {msg}")
    
    def trade_event(self, msg: str, target_date: Optional[Union[date, pd.Timestamp]] = None):
        """Log trading events (always important)."""
        self.logger.info(f"[TRADE] {msg}")
    
    def rebalance_event(self, msg: str, target_date: Optional[Union[date, pd.Timestamp]] = None):
        """Log rebalancing events (always important)."""
        self.logger.info(f"[REBALANCE] {msg}")
    
    def performance_summary(self, msg: str):
        """Log performance summaries (always shown)."""
        self.logger.info(f"[PERFORMANCE] {msg}")
    
    def daily_progress(self, current_day: int, total_days: int, current_date: Union[date, pd.Timestamp], 
                      portfolio_value: float = None, target_date: Optional[Union[date, pd.Timestamp]] = None):
        """Log daily progress with smart frequency."""
        show_detailed = self.should_show_detailed_logs(target_date or current_date)
        show_summary = self.should_show_periodic_summary(target_date or current_date)
        
        # Always show first few and last few days
        is_edge_day = current_day <= 5 or current_day > (total_days - 5)
        
        if show_detailed or show_summary or is_edge_day:
            date_str = current_date.strftime('%Y-%m-%d') if hasattr(current_date, 'strftime') else str(current_date)
            progress_msg = f"Processing day {current_day}/{total_days}: {date_str}"
            if portfolio_value is not None:
                progress_msg += f" (Portfolio: ${portfolio_value:,.2f})"
            self.logger.info(progress_msg)


def create_smart_logger(name: str) -> SmartLogger:
    """Create a smart logger instance."""
    # Ensure basic logging is configured
    logging.basicConfig(
        level=logging.DEBUG,  # Set to DEBUG to allow SmartLogger to control filtering
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        force=False  # Don't override existing configuration
    )
    base_logger = logging.getLogger(name)
    return SmartLogger(name, base_logger)


# Example usage patterns for different modules:

def configure_module_logging(module_name: str, level: str = "INFO") -> SmartLogger:
    """Configure smart logging for a specific module."""
    # Set up basic logging configuration if not already done
    logging.basicConfig(
        level=getattr(logging, level.upper(), logging.INFO),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    return create_smart_logger(module_name)
