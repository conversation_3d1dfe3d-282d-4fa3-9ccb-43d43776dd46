"""
Configuration parameters for the financial allocation backtesting framework.
"""

from .paths import *  # Import all path configurations

# Data parameters
data_params = {
    # List of tickers to include in portfolio
    'tickers': ['SPY', 'SHV', 'EFA', 'TLT', 'PFF'],
    
    # Date range for backtesting
    'start_date': '2020-01-01',
    'end_date': '2025-04-04',
    
    # Price field to use for calculations
    'price_field': 'Close',
    
    # Risk-free rate ticker (used for performance metrics)
    'risk_free_ticker': '^IRX',  # 13-week Treasury Bill
}

# Backtesting parameters
backtest_params = {
    # Strategy name (equal_weight, momentum, ema)
    'strategy': 'ema',
    
    # Rebalancing frequency
    # Options: 'daily', 'weekly', 'monthly', 'quarterly', 'yearly'
    # Pandas frequency strings are converted internally: 'daily'='B', 'weekly'='weekly', 'monthly'='MS', etc.
    'rebalance_freq': 'weekly',
    
    # Trade execution delay (in days)
    # 0 means same-day execution, 1 means next-day execution
    'execution_delay': 1,
    
    # Transaction cost as fraction of trade value
    'transaction_cost': 0.001,  # 0.1%
    
    # Initial capital
    'initial_capital': 10000,
    
    # Benchmark ticker for performance comparison
    'benchmark_ticker': 'SPY',
}

# Strategy-specific parameters
strategy_params = {
    # Parameters for momentum strategy
    'lookback': 60,  # Lookback period for momentum calculation
    'top_n': 2,      # Number of top assets to select
    
    # Parameters for EMA strategy
    # Following the naming convention from ema_allocation_model.py
    'st_lookback': 10,    # Short-term EMA period (STLB)
    'mt_lookback': 50,    # Medium-term EMA period (MTLB)
    'lt_lookback': 150,   # Long-term EMA period (LTLB)
}

# Parameter optimization settings
optimization_params = {
    # Method to use for optimization (grid, random, bayesian)
    'method': 'grid',
    
    # Number of iterations for random or bayesian optimization
    'n_iter': 10,
    
    # Evaluation metric to optimize for
    'evaluation_metric': 'sharpe_ratio',
    
    # Parameter grid for optimization
    'param_grid': {
        'strategy': ['equal_weight', 'momentum', 'ema'],
        'lookback': [30, 60, 90],
        'top_n': [1, 2, 3],
        'st_lookback': [5, 10, 20],
        'mt_lookback': [20, 50, 100],
        'lt_lookback': [100, 150, 200],
    },
}

# Visualization parameters
visualization_params = {
    # Whether to create charts
    'create_charts': True,
    
    # Types of charts to create
    'chart_types': [
        'cumulative_returns',
        'drawdown',
        'return_distribution',
        'monthly_returns',
        'portfolio_weights'
    ],
    
    # Chart format (png, jpg, svg)
    'chart_format': 'png',
    
    # Chart resolution (DPI)
    'chart_dpi': 300,
}

# Reporting parameters
reporting_params = {
    # Whether to create Excel reports
    'create_excel': True,
    
    # Whether to save trade logs
    'save_trade_log': True,
    
    # Output directory for reports and logs
    'output_dir': str(OUTPUT_DIR),
    
    # Performance metrics to include
    'metrics': [
        'total_return',
        'annualized_return',
        'volatility',
        'sharpe_ratio',
        'max_drawdown',
        'win_rate',
    ],
}

# All parameters combined
config = {
    'venv_path': str(VENV_PATH),
    'custom_lib_path': str(CUSTOM_LIB_PATH),
    'output_dir': str(OUTPUT_DIR),
    'data_params': data_params,
    'backtest_params': backtest_params,
    'strategy_params': strategy_params,
    'optimization_params': optimization_params,
    'visualization_params': visualization_params,
    'reporting_params': reporting_params,
}
