# Signal Phase Deep Dive Analysis
## CPS v4 Pipeline Signal Generation Phase

**Date**: Current  
**Analysis Target**: Step 3 - Signal Phase Deep Dive  
**Files Analyzed**: `Algo_signal_phase.py`, `data_loader_v4.py`, `ema_signal_bridge.py`  

---

## 1. FILE ANALYSIS: Algo_signal_phase.py

**Location**: `v4/Algo_signal_phase.py`  
**Purpose**: Signal generation phase script for decoupled backtest architecture  

### Functions & Classes:

#### 1.1 log_message(message, level="INFO")
- **Purpose**: Centralized logging function to standardize output format
- **Key Parameters**:
  - `message` (str): Message to log
  - `level` (str, default="INFO"): Log level - INFO, WARNING, ERROR, CRITICAL, MILESTONE
- **Input/Output**:
  - **Input**: Message string and log level
  - **Output**: Formatted console output with timestamp
  - **Return**: None

#### 1.2 run_signal_phase()
- **Purpose**: Main orchestration function that runs signal generation phase and saves output
- **Key Parameters**: None (all parameters come from settings)
- **Input/Output**:
  - **Input**: None (loads settings internally)
  - **Output**: 
    - CSV file: `signals_output_{timestamp}.csv` in `v4_trace_outputs/`
    - Console logs via `log_message()`
  - **Return**: `csv_filepath` (str) or None on error
- **Parameter Sources**:
  - All settings from `settings_CPS_v4.py` → `load_settings()`
  - Strategy parameters from `settings['strategy']`
  - Strategy name from `strategy_params['strategy_name']` (default: 'equal_weight')

---

## 2. FILE ANALYSIS: data_loader_v4.py

**Location**: `v4/engine/data_loader_v4.py`  
**Purpose**: Data loader module (CPS v4 compliant) for loading historical price data  

### Functions & Classes:

#### 2.1 get_adjusted_close_data(tickers_list, start_date_obj, end_date_obj, price_field_str)
- **Purpose**: Load and return adjusted close price data for given tickers and date range
- **Key Parameters**:
  - `tickers_list` (list): List of ticker symbols
  - `start_date_obj` (datetime.date or None): Standardized start date
  - `end_date_obj` (datetime.date or None): Standardized end date  
  - `price_field_str` (str): Price field to extract (e.g., 'Close', 'Adj Close')
- **Input/Output**:
  - **Input**: Ticker list, date objects, price field string
  - **Output**: DataFrame with price data for specified tickers/dates
  - **Return**: `price_data` (DataFrame)
- **Parameter Sources**: Called by `load_ticker_data()` with parameters from settings

#### 2.2 load_ticker_data(tickers_list, start_date_str, end_date_str, price_field_str, current_mode)
- **Purpose**: Manage ticker data based on passed-in parameters (read from file or fetch fresh)
- **Key Parameters**:
  - `tickers_list` (list): List of ticker symbols
  - `start_date_str` (str): Start date string (YYYY-MM-DD)
  - `end_date_str` (str or None): End date string (YYYY-MM-DD)
  - `price_field_str` (str): Price field to use
  - `current_mode` (str): Data storage mode ('Read', 'Save', 'New')
- **Input/Output**:
  - **Input**: Parameters as listed above
  - **Output**: 
    - .xlsx file: `tickerdata_{tickers}_{start_date}_{end_date}.xlsx` (if mode='Save')
    - DataFrame with loaded ticker data
  - **Return**: `df` (DataFrame)
- **Parameter Sources**: Called by `load_data_for_backtest()` with settings parameters

#### 2.3 get_returns_data(price_data)
- **Purpose**: Calculate returns from price data using pct_change()
- **Key Parameters**:
  - `price_data` (DataFrame): Historical price data
- **Input/Output**:
  - **Input**: Price data DataFrame
  - **Output**: Returns DataFrame
  - **Return**: `returns` (DataFrame)

#### 2.4 load_data_for_backtest(current_settings)
- **Purpose**: Load all necessary data for backtesting using the provided settings object
- **Key Parameters**:
  - `current_settings` (dict): The current, freshly loaded settings object
- **Input/Output**:
  - **Input**: Settings dictionary from `load_settings()`
  - **Output**: Dictionary with price_data, returns_data, risk_free_rate
  - **Return**: `dict` with keys: 'price_data', 'returns_data', 'risk_free_rate'
- **Parameter Sources**:
  - All parameters from `settings_CPS_v4.py` → `current_settings['data_params']`:
    - `tickers`: From `data_params.tickers` (default: ('SPY', 'QQQ', 'IWM', 'GLD', 'TLT'))
    - `start_date`: From `data_params.start_date` (required)
    - `end_date`: From `data_params.end_date` (optional)
    - `price_field`: From `data_params.price_field` (default: 'Close')
    - `data_storage_mode`: From `data_params.data_storage_mode` (default: 'Read')
  - Risk-free rate: From `current_settings['performance']['risk_free_rate']` (default: 0.0)

---

## 3. FILE ANALYSIS: ema_signal_bridge.py

**Location**: `v4/models/ema_signal_bridge.py`  
**Purpose**: Bridge module to connect EMASignalGenerator with ema_allocation_model for tracing  

### Functions & Classes:

#### 3.1 run_ema_model_with_tracing(price_data, **params)
- **Purpose**: Run the EMA allocation model in trace mode and save detailed signal history data
- **Key Parameters**:
  - `price_data` (DataFrame): Historical price data
  - `**params`: Additional parameters for the EMA model (passed through to `ema_allocation_model_updated`)
- **Input/Output**:
  - **Input**: Price data DataFrame and parameters
  - **Output**: 
    - Multiple CSV files in `v4_trace_outputs/`:
      - `ema_short_{timestamp}.csv`
      - `ema_medium_{timestamp}.csv`
      - `ema_long_{timestamp}.csv`
      - `ranking_{timestamp}.csv`
      - `02_ema_average_history_{timestamp}.csv`
      - `04_raw_algocalc_history_{timestamp}.csv`
    - Signal history DataFrame (60%/40% allocation to top 2 assets)
  - **Return**: `signal_history` (DataFrame with 0.0/0.6/0.4 allocations)
- **Parameter Sources**: Parameters passed through from calling function

#### 3.2 _bridge_generate_signals(price_data, **params)
- **Purpose**: Alias for run_ema_model_with_tracing for backward compatibility
- **Key Parameters**: Same as `run_ema_model_with_tracing`
- **Input/Output**: Same as `run_ema_model_with_tracing`
- **Return**: Same as `run_ema_model_with_tracing`

#### 3.3 _bridge_validate_signals(self, signals)
- **Purpose**: Placeholder validator – passthrough for now
- **Key Parameters**:
  - `signals`: Signals to validate
- **Input/Output**:
  - **Input**: Signals data
  - **Output**: Same signals (passthrough)
  - **Return**: `signals` (unchanged)

---

## 4. PARAMETER SOURCES (settings_CPS_v4.py)

### 4.1 Key Parameters Used by Signal Phase:

#### From [data_params] section:
- `tickers`: (Group1ETFBase, ETFpicklist) → Default: ('SPY', 'SHV', 'EFA', 'TLT', 'PFF')
- `start_date`: 2020-01-01
- `end_date`: 2025-07-21  
- `price_field`: Close
- `data_storage_mode`: Save

#### From [Strategy] section:
- `strategy_name`: EMA_Crossover
- `min_weight`: 0.0
- `max_weight`: 1.0
- `ema_short_period`: (optimize=False, default_value=12, min_value=5, max_value=20, increment=1)
- `ema_long_period`: (optimize=False, default_value=26, min_value=10, max_value=100, increment=1)
- `st_lookback`: (optimize=False, default_value=15, min_value=5, max_value=30, increment=1)
- `mt_lookback`: (optimize=False, default_value=70, min_value=30, max_value=100, increment=5)
- `lt_lookback`: (optimize=False, default_value=100, min_value=50, max_value=200, increment=10)

#### From [ema_model] section:
- `short_period`: 12
- `medium_period`: 26
- `long_period`: 50

#### From [System] section:
- `system_lookback`: (optimize=True, default_value=60, min_value=20, max_value=120, increment=5)
- `system_top_n`: (optimize=False, default_value=2, min_value=1, max_value=5, increment=1)

#### From [Performance] section:
- `risk_free_rate`: 0.02

---

## 5. EXECUTION SEQUENCE: Pipeline Entry to Signal Output

### 5.1 Entry Point: `run_signal_phase()` in Algo_signal_phase.py

```
1. run_signal_phase()
   ├── log_message("--- Starting Signal Generation Phase ---", "MILESTONE")
   └── Step 1: Load Settings
       ├── settings = load_settings()  # from settings_CPS_v4.py
       └── log_message("Settings loaded successfully.")
```

### 5.2 Data Loading Phase:

```
2. Step 2: Load market data
   ├── all_data = load_data_for_backtest(current_settings=settings)
   │   ├── Extract data_params from settings
   │   ├── tickers = data_params.get('tickers', ('SPY', 'QQQ', 'IWM', 'GLD', 'TLT'))
   │   ├── start_date_str = data_params.get('start_date')  # Required
   │   ├── end_date_str = data_params.get('end_date')      # Optional
   │   ├── price_field = data_params.get('price_field', 'Close')
   │   ├── local_mode = data_params.get('data_storage_mode', 'Read')
   │   │
   │   ├── raw_df = load_ticker_data(local_tickers, start_date_str, end_date_str, local_price_field, local_mode)
   │   │   ├── If mode='Read': Load from .xlsx file
   │   │   └── If mode='Save'/'New': 
   │   │       ├── df = get_adjusted_close_data(tickers_list, start_date_obj, end_date_obj, price_field_str)
   │   │       │   ├── For each ticker:
   │   │       │   │   ├── data = data_fetch_stock_data(ticker=ticker, period="max", interval="1d")
   │   │       │   │   ├── data = standardize_dataframe_index(data)
   │   │       │   │   ├── data = filter_dataframe_by_dates(data, start_ts, end_ts)
   │   │       │   │   └── price_data[ticker] = data[price_field]
   │   │       │   └── Return price_data.ffill().fillna(0)
   │   │       └── If mode='Save': df.to_excel(xlsx_path)
   │   │
   │   ├── df = filter_dataframe_by_dates(raw_df, start_date=start_date_str, end_date=end_date_str)
   │   ├── rf_value = current_settings.get('performance', {}).get('risk_free_rate', 0.0)
   │   ├── rf = pd.Series(rf_value, index=df.index)
   │   └── return {'price_data': df, 'returns_data': get_returns_data(df), 'risk_free_rate': rf}
   │
   ├── price_data = all_data['price_data']
   └── log_message(f"Price data loaded for {len(price_data.columns)} assets.")
```

### 5.3 Signal Generation Phase:

```
3. Step 3: Generate signals
   ├── raw_strategy_params = settings.get('strategy', {})
   ├── strategy_params = raw_strategy_params.copy()
   ├── strategy_to_run = strategy_params.pop('strategy_name', 'equal_weight')
   │
   ├── signals_df = run_ema_model_with_tracing(price_data=price_data, **strategy_params)
   │   ├── Call ema_allocation_model_updated(price_data=price_data, trace_mode=True, **params)
   │   │   ├── Get current_date = price_data.index[-1]
   │   │   ├── Extract system_top_n from settings (default: 2)
   │   │   │
   │   │   ├── model_output = ema_allocation_model(price_data, system_top_n=current_system_top_n, trace_mode=True, **params)
   │   │   │   ├── Calculate EMAs: short_ema, med_ema, long_ema = calculate_ema_metrics(price_data)
   │   │   │   │   ├── Using st_lookback, mt_lookback, lt_lookback from settings
   │   │   │   │   ├── short_ema[column] = tech_exponential_moving_average(price_data[column], window=st_lookback)
   │   │   │   │   ├── med_ema[column] = tech_exponential_moving_average(price_data[column], window=mt_lookback)
   │   │   │   │   └── long_ema[column] = tech_exponential_moving_average(price_data[column], window=lt_lookback)
   │   │   │   │
   │   │   │   ├── Calculate ratios: stmtemax, mtltemax, emaxavg, stmtemax_df, mtltemax_df, emaxavg_df = calculate_ema_ratios(short_ema, med_ema, long_ema)
   │   │   │   │   ├── stmtemax_df = short_ema / med_ema
   │   │   │   │   ├── mtltemax_df = med_ema / long_ema
   │   │   │   │   └── emaxavg_df = (stmtemax_df + mtltemax_df) / 2
   │   │   │   │
   │   │   │   ├── ranked_assets = emaxavg.sort_values(ascending=False)  # Rank by EMAXAvg
   │   │   │   ├── weights = {asset: 0.0 for asset in price_data.columns}
   │   │   │   ├── rule_weights = get_allocation_weights(top_n=system_top_n, algorithm=algo_name)
   │   │   │   ├── selected_assets = ranked_assets.index[:top_n]  # Top 2 assets
   │   │   │   ├── Assign weights: weights[top_assets[0]] = 0.6, weights[top_assets[1]] = 0.4
   │   │   │   └── return (weights, ratios_output, ranks_df, signal_output, short_ema, med_ema, long_ema, stmtemax_df, mtltemax_df, emaxavg_df)
   │   │   │
   │   │   ├── Generate historical_ranks_df for all dates in emaxavg_hist_df
   │   │   └── return ({current_date: weights}, ratios_output, ranks_df, signal_output, short_ema_df, med_ema_df, long_ema_df, stmtemax_hist_df, mtltemax_hist_df, emaxavg_hist_df)
   │   │
   │   ├── Save trace outputs to CSV files:
   │   │   ├── save_df_to_trace_dir(short_ema_df, f"ema_short_{current_timestamp}.csv")
   │   │   ├── save_df_to_trace_dir(med_ema_df, f"ema_medium_{current_timestamp}.csv")
   │   │   ├── save_df_to_trace_dir(long_ema_df, f"ema_long_{current_timestamp}.csv")
   │   │   ├── save_df_to_trace_dir(matrix_ranks_df, f"ranking_{current_timestamp}.csv")
   │   │   ├── save_df_to_trace_dir(ema_averages, f"02_ema_average_history_{current_timestamp}.csv")
   │   │   └── save_df_to_trace_dir(raw_signals, f"04_raw_algocalc_history_{current_timestamp}.csv")
   │   │
   │   ├── Create signal_history DataFrame (0.0 for all assets initially)
   │   ├── For each date in price_data.index:
   │   │   ├── Get most recent ranks as of that date
   │   │   ├── If len(latest_ranks) >= 2:
   │   │   │   ├── top_assets = latest_ranks.sort_values().head(2).index.tolist()
   │   │   │   ├── signal_history.loc[date, top_assets[0]] = 0.6
   │   │   │   └── signal_history.loc[date, top_assets[1]] = 0.4
   │   └── return signal_history
   │
   └── log_message(f"   - Using strategy: '{strategy_to_run}' with params: {strategy_params}")
```

### 5.4 Output Generation Phase:

```
4. Save signals to CSV
   ├── trace_csv_dir = os.path.join(_project_root, 'v4_trace_outputs')
   ├── os.makedirs(trace_csv_dir, exist_ok=True)
   ├── timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
   ├── csv_filename = f"signals_output_{timestamp}.csv"
   ├── csv_filepath = os.path.join(trace_csv_dir, csv_filename)
   ├── signals_df.to_csv(csv_filepath)  # Keep index=True to preserve dates
   ├── log_message(f"Signals saved to timestamped CSV: {csv_filepath}")
   └── return csv_filepath
```

---

## 6. OUTPUT FILES GENERATED

### 6.1 Primary Signal Output:
- **File**: `v4_trace_outputs/signals_output_{timestamp}.csv`
- **Content**: Final signal allocations matrix (dates × assets) with 0.0/0.6/0.4 values
- **Format**: CSV with Date as index, assets as columns

### 6.2 Trace Output Files:
- **Individual EMA Files**:
  - `ema_short_{timestamp}.csv` - Short-term EMA values
  - `ema_medium_{timestamp}.csv` - Medium-term EMA values  
  - `ema_long_{timestamp}.csv` - Long-term EMA values
  
- **Combined Files**:
  - `02_ema_average_history_{timestamp}.csv` - All EMAs combined with suffixes (_ST, _MT, _LT)
  - `04_raw_algocalc_history_{timestamp}.csv` - Raw algorithm calculations (STMTEMAX, MTLTEMAX, EMAXAvg)
  
- **Ranking File**:
  - `ranking_{timestamp}.csv` - Multi-level columns with EMAXAvg_Value and Rank_Ordinal for each asset

### 6.3 Data Storage Files (if data_storage_mode='Save'):
- **File**: `data/tickerdata_{tickers}_{start_date}_{end_date}.xlsx`
- **Content**: Historical price data for the specified tickers and date range

---

## 7. KEY INSIGHTS

### 7.1 Parameter Flow:
- All parameters originate from `settings_parameters_v4.ini`
- Settings are loaded once at the beginning of `run_signal_phase()`
- Parameters flow through multiple function calls without modification
- ComplexN parameters are automatically extracted to their default_value

### 7.2 Data Flow:
- Price data flows: Raw market data → Filtered data → EMA calculations → Signal generation
- Signal flow: EMA ratios → Asset ranking → Allocation weights → Signal history matrix
- All intermediate results are saved as CSV files for tracing

### 7.3 Signal Generation Logic:
- Uses 3 EMAs (short, medium, long) with configurable lookback periods
- Calculates ratios: STMTEMAX = Short/Medium, MTLTEMAX = Medium/Long
- Final signal: EMAXAvg = (STMTEMAX + MTLTEMAX) / 2
- Ranking: Assets ranked by EMAXAvg (descending)
- Allocation: Top 2 assets get 60%/40%, others get 0%

### 7.4 File Output Strategy:
- All outputs use timestamped filenames for tracking
- CSV format only (Parquet eliminated for performance)
- Files saved to `v4_trace_outputs/` directory
- Both detailed trace files and final signal output are generated

---

*End of Signal Phase Deep Dive Analysis*
